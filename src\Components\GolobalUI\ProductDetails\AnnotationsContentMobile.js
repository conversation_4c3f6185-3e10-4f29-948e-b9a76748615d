import React, { Component } from "react";
import "./ProductDetails.css";

class AnnotationsContentMobile extends Component {

  render() {
    return (
        <div className="AnnotationsContentMobile" style={{display: this.props.AnnotationsMobileContentDisplay, 
            animation: this.props.PDMAnimation}}>
            <div style={{position: "absolute", width: "100%", height: "22%", top: "0%"}} onClick={()=> this.props.showAnnotationsMobile("hide")}>
            </div>
            <div style={{position: "absolute", width: "100%", height: "28%", top: "72%"}} onClick={()=> this.props.showAnnotationsMobile("hide")}>   
            </div>
            
        </div>
    );
  }
}

export default AnnotationsContentMobile;
