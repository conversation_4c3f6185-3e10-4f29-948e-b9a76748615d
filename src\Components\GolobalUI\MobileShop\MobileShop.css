.MobileShop {
  position: absolute;
  height: 86%;
  width: 90%;
  margin-left: 5%;
  margin-top: 10.5%;
  z-index: 999;
  background-color: rgba(255, 255, 255, 1);
  border-radius: 0px;
  padding: 0%;
  overflow-y: hidden;
  overflow-x: hidden;
  display: block;
  font-family: Caslon;
  backdrop-filter: blur(8px);
}
.MobileShopPageContent {
  width: 100%;
  height: 92%;
  margin-left: 0;
  margin-right: auto;
  padding: 0%;
  padding-left: 0%;
  padding-bottom: 1%;
  padding-top: 1%;
  overflow-y: scroll;
  overflow-x: hidden;
}
.Mobileproduct {
  width: 100%;
  height: 33%;
  text-align: center;
  align-items: center;
  display: flex;
  margin-top: 0.2%;
  /* border-bottom: 0.5px solid #000000; */
}
.MobileProductTitle {
  width: 50%;
  height: 90%;
  padding-right: 4%;
  padding-left: 2%;
  box-sizing: border-box;
  /* margin-top: auto;
      margin-bottom: auto; */
}
.MobileProductTitle p {
  text-align: left;
  font-weight: 500;
  display: block;
  font-size: 0.8em;
  font-style: italic;
  border-top: none;
  margin-block-start: 0.7em;
  margin-block-end: 0.7em;
}
.MobileProductTitle span {
  font-weight: normal;
  display: block;
  text-align: left;
  font-size: 3.5vw;
}
.MobileProductDiscription {
  width: 100%;
  height: auto;
  text-align: left;
  font-size: 0.5em;
  padding-top: 0%;
  padding-left: 0%;
  padding-right: 0%;
  font-weight: 200;
  font-style: normal;
  color: black;
  /* line-height: 1.25; */
}
.MobileProductDiscription p {
  text-align: center;
  margin: 0;
  margin-block-start: 0em;
  margin-block-end: 0em;
}
.MobileProductImage {
  width: 50%;
  height: 100%;
  position: relative;
  display: grid;
  display: -ms-grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: 1fr;
  grid-column-gap: 0px;
  grid-row-gap: 0px;
}
/* .MobileProductImage img {
    position: absolute;
    width: 8vw;
    height: auto;
    margin-right: auto;
    margin-left: 15%;
    top: 0;
    margin-top: 5%;
  } */
.Mobiledisplayproduct {
  width: 100%;
}
.Mobiledisplayproduct img {
  margin-top: 60%;
  width: 100%;
}
.Mobiledisplayproduct span {
  font-style: normal;
  position: relative;
  width: 100%;
  font-size: 3vw;
  height: auto;
  margin-left: 0%;
  margin-right: auto;
  display: inline-flex;
  background-color: none;
  bottom: 0%;
  text-align: left;
  margin-bottom: 20%;
}
.Mobilecountershop {
  position: absolute;
  width: 25%;
  height: 100%;
  font-size: 0.7vw;
  height: auto;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  background-color: #f5f5f5 !important;
  bottom: 0%;
  margin-bottom: 2%;
  margin-left: 4%;
}
.Mobilecountershop button {
  display: inline;
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 2%;
  margin: 0%;
  width: 25%;
  height: 100%;
  box-sizing: border-box;
}
.Mobilereduce {
  float: left;
}
.Mobileincrease {
  float: right;
}
.MobileBlackCart {
  position: relative;
  padding: 5%;
  margin-left: auto;
  margin-right: auto;
  width: 80%;
  font-size: 0.5em;
  background-color: black;
  color: #ffff;
  white-space: nowrap;
  border: none;
  outline: none;
  margin-top: 100%;
}
#Mobileprettysmall {
  margin-top: 100%;
}
.mobilecheckout {
  width: 100%;
  background-color: #000000;
  color: #ffff;
  border: none;
  height: 8%;
  font-size: 1em;
  justify-content: center;
  text-align: center;
  padding-left: 10%;
  padding-right: 10%;
  padding-bottom: 2%;
  outline: none;
}
.mobilecheckout:hover {
  background-color: #f5f5f5;
  color: grey;
  border: none;
  outline: none;
}
.mobilecheckout:active {
  border: none;
  outline: none;
}
.mobilecheckout:focus {
  border: none;
  outline: none;
}

.MobileCartdiv {
  width: 100%;
  height: 90%;
  margin-top: 10%;
  background-color: rgba(255, 255, 255, 0.7);
  position: absolute;
  z-index: 9999;
}
.MobileIncorpButton {
  position: absolute;
  width: 30%;
  height: 5%;
  padding: 0%;
  left: 0;
  margin-left: 12%;
  top: 0;
  margin-top: 1.5%;
  background-color: rgb(252, 248, 240);
  border: none;
}
.MobileIncorpButton img {
  width: 100%;
}
