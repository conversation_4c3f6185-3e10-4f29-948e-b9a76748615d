.MobileCartContainer {
    position: relative;
    width: 80%;
    margin-left: 10%;
    margin-top: 5%;
    height: 100%;
    background-color: #fff ;
  }
  .MobileCloseCart {
    width: 25px;
    height: 25px;
    position: absolute;
    right: 0;
    margin-right: 6%;
    background-color: #f5f5f5;
    border-radius: 50%;
    border: none;
    padding: 1%;
    cursor: pointer;
    top: -2%;
    z-index: 999999999999999;
  }
  .MobileCloseCart img {
    width: 60%;
  }
  .MobileCartFooter {
    width: 100%;
    height: 6%;
    position: absolute;
    bottom: 0;
    margin-bottom: 2%;
  }
  .MobileCartCheckout{
        width: 100%;
        height: 100%;
        background-color: black;
        color: white;
        cursor: pointer;
        text-transform: uppercase;
        font-size: 1em;
  }
  .MobileCartBody {
    position: relative;
    width: 100%;
    padding-left: 5%;
    padding-right: 5%;
    box-sizing: border-box;
}
  .MobileCartItems {
    height: auto;
    max-height: 24vh;
    overflow-y: scroll;
    width: 100%;
    margin-top: 2%;
    margin-bottom: 2%;
    padding: 2%;
  }
  .MobileCartItems ul {
    margin-block-start: 0em;
    margin-block-end: 0em;
    padding-inline-start: 0px;
  }
  /*
  .MobileShop {
    position: absolute;
    height: 82%;
    width: 90%;
    margin-left: 5%;
    margin-top: 10%;
    z-index: 999;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 0px;
    padding: 0%;
    overflow-y: hidden;
    overflow-x: hidden;
    display: block;
    font-family: Caslon;
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}
.MobileCartdiv {
    width: 99%;
    height: 97%;
    margin-top: 10%;
    background-color: rgba(255, 255, 255, 0.7);
    position: absolute;
    z-index: 9999;
    border: 1px solid red;
}*/