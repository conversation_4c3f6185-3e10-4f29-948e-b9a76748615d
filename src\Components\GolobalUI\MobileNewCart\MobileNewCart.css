.MobileNewCartPage{
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0%;
    top: 0%;
    background-color: rgba(150, 150, 150, 0.7);
    z-index: 5;
}
.MobileNewCart{
    /* background-color: #f2f2f2; */
    background-color: #f6f6f6;
    width: 95%;
    /* height: 90%; */
    height: 75%;
    /* margin-top: 15%; */
    top: 23%;
    position: absolute;
    z-index: 9999;
    /* margin-left: 5%;
    margin-right: 5%; */
    margin-left: 2.5%;
    /* border-radius: 12px; */
    border-radius: 0px;
    padding: 5%;
    box-sizing: border-box;
    z-index: 6;
}
.MobileNewCartEmpty{
    /* background-color: #f2f2f2; */
    background-color: #f6f6f6;
    width: 95%;
    /* height: 90%; */
    /* height: 75%; */
    /* margin-top: 15%; */
    top: 23%;
    position: absolute;
    z-index: 9999;
    /* margin-left: 5%;
    margin-right: 5%; */
    margin-left: 2.5%;
    /* border-radius: 12px; */
    border-radius: 0px;
    padding: 5%;
    box-sizing: border-box;
    z-index: 6;
}
.MobileNewCartTitle{
    width: 100%;
    text-transform:uppercase;
    display: flex;
    align-items: left;
    text-align: left;
    justify-content: left;
    border:none;
    font-size: 4.5vw;
    padding-bottom: 3%;
    border-bottom: 0.5px solid #000; 
}
.LinesContainerNew{
    width: 100%;
    /* height: 40%; */
    /* height: 35%; */
    overflow: auto;
}
.NewMobileCartFooter {
    width: 90%;
    height: 10%;
    position: absolute;
    bottom: 3%;
    /* margin-bottom: 1%; */
    text-transform: uppercase;
  }
  .NewMobileCartCheckout{
    width: 100%;
    height: 100%;
    background-color: black;
    color: white;
    cursor: pointer;
    text-transform: uppercase;
    font-size: 1em;
    border: none;
    font-family: 'Inter';
}
.NewCartOpenSection {
    width: 100%;
    margin-top: 4%;
    height: auto;
    position: relative;
  }