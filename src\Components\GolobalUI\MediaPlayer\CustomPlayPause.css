.PlayerContainer {
  position: relative;
  width: 12%;
  background-color: transparent !important;
  float: right;
  padding: 0;
  /* margin-top: -9%; */
  /* margin-top: -7%; */
  margin-top: -1%;
  cursor: pointer;
  outline: none;
  border: none;
}
.PlayerContainer img {
  width: 100%;
}
.PlayerContainerVideo {
  position: absolute;
  width: 10%;
  height: 10%;
  background-color: #fff !important;
  padding: 3%;
  margin-top: 20%;
  right: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  cursor: pointer;
  outline: none;
  border: none;
  z-index: 99;
  border-radius: 50%;
}
.PlayerContainerVideo img {
  width: 100%;
}
@media only screen and (max-width: 600px) {
  .PlayerContainer {
    position: relative;
    width: 18%;
    background-color: transparent !important;
    float: right;
    padding: 0;
    /* margin-top: -9%; */
    /* margin-top: -7%; */
    margin-top: -20%;
    cursor: pointer;
    outline: none;
    border: none;
  }
}