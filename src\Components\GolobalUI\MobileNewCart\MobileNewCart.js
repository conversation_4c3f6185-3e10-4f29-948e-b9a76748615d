import React, { useState } from "react";
import LineItem from "../../LineItem";
import "./MobileNewCart.css";

function MobileNewCart(props) {
  const [Policy1height, setPolicy1height] = useState("50%");
  const [Policy1status, setPolicy1status] = useState("closed");
  const [policy1img, setpolicy1img] = useState("plus (1).png");

  const [Policy2height, setPolicy2height] = useState("50%");
  const [Policy2status, setPolicy2status] = useState("closed");
  const [policy2img, setpolicy2img] = useState("plus (1).png");

  const [itemsCartheight, setitemsCartheight] = useState("38%");

  const [PolicyP1, setPolicyP1] = useState("none");
  const [PolicyP2, setPolicyP2] = useState("none");

  const openCheckout = () => {
    window.open(props.checkout.checkoutUrl);
  };
  const SwitchDisplayPolices = (action) => {
    if (action == "D1") {
      if (Policy1status == "closed") {
        setPolicy1height("8vh");
        setPolicy1status("opened");
        setpolicy1img("minus-sign.png");
        if (Policy2status == "opened") {
          setPolicy2height("50%");
          setPolicy2status("closed");
          setpolicy2img("plus (1).png");
        }
      } else if (Policy1status == "opened") {
        setPolicy1height("50%");
        setPolicy1status("closed");
        setpolicy1img("plus (1).png");
      }
    } else if (action == "D2") {
      if (Policy2status == "closed") {
        setPolicy2height("4vh");
        setitemsCartheight("33%");
        setPolicy2status("opened");
        setpolicy2img("minus-sign.png");
        setPolicyP2("");
        if (Policy1status == "opened") {
          setPolicy1height("50%");
          setPolicy1status("closed");
          setpolicy1img("plus (1).png");
          setPolicyP2("none");
        }
      } else if (Policy2status == "opened") {
        setPolicy2height("50%");
        setitemsCartheight("38%");
        setPolicy2status("closed");
        setpolicy2img("plus (1).png");
        setPolicyP2("none");
      }
    }
  };
  let line_items = props.checkout.lines
    ? props.checkout.lines.edges.map((line_item) => {
        console.log(line_item);
        return (
          <LineItem
            removeLineItemInCart={props.removeLineItemInCart}
            updateLineItemInCartAddMobile={props.updateLineItemInCartAddMobile}
            updateLineItemInCartLessMobile={
              props.updateLineItemInCartLessMobile
            }
            key={line_item.node.id.toString()}
            line_item={line_item.node}
          />
        );
      })
    : [];

  return (
    <div
      className="MobileNewCartPage"
      style={{ display: props.shopPageDisplay, animation: props.PDMAnimation }}
    >
      <div
        style={{
          position: "absolute",
          width: "100%",
          height: "100%",
          top: "0%",
        }}
        onClick={() => props.ShopPageDisplaySwitchSpecial("open")}
      ></div>
      {props.totalitems === 0 ? (
        <div
          className="MobileNewCartEmpty"
          style={{
            display: props.shopPageDisplay,
            animation: props.PDMAnimation,
          }}
        >
          <div className="MobileNewCartTitle">your cart</div>
          <div className="CartDetails" style={{ marginTop: "4%" }}>
            <div
              className="CartDetailsHeadEmpty"
              style={{ fontStyle: "italic", fontFamily: "Caslon" }}
            >
              Cart empty
            </div>
            <div
              className="CartDetailsHeadEmpty"
              style={{ fontStyle: "italic", fontFamily: "Caslon" }}
            >
              Heart full
            </div>
          </div>
        </div>
      ) : (
        <div
          className="MobileNewCart"
          style={{
            display: props.shopPageDisplay,
            animation: props.PDMAnimation,
          }}
        >
          <div className="MobileNewCartTitle">your cart</div>
          <div
            className="LinesContainerNew"
            style={{ height: itemsCartheight }}
          >
            <ul style={{ paddingInlineStart: "10px" }}> {line_items}</ul>
          </div>
          <div className="CartDetails">
            {/* <div className="CartDetailsHead" style={{fontFamily: "Inter"}}>PAYMENT DETAILS</div> */}
            <div className="CartDetailsList">
              <ul style={{ fontFamily: "Inter" }}>
                <div style={{ borderBottom: "1px solid rgb(217, 217, 217)" }}>
                  <li style={{ letterSpacing: "-0.2vw" }}>
                    Subtotal{" "}
                    <span>
                      €
                      {props.checkout.cost && props.checkout.cost.subtotalAmount
                        ? props.checkout.cost.subtotalAmount.amount
                            .toString(10)
                            .split(".")[0].length > 3
                          ? props.checkout.cost.subtotalAmount.amount
                              .toString(10)
                              .substring(
                                0,
                                props.checkout.cost.subtotalAmount.amount
                                  .toString(10)
                                  .split(".")[0].length - 3,
                              ) +
                            "," +
                            props.checkout.cost.subtotalAmount.amount
                              .toString(10)
                              .substring(
                                props.checkout.cost.subtotalAmount.amount
                                  .toString(10)
                                  .split(".")[0].length - 3,
                              ) +
                            "0"
                          : props.checkout.cost.subtotalAmount.amount + "0"
                        : 0.0}
                    </span>
                  </li>
                  <li style={{ letterSpacing: "-0.2vw" }}>
                    Shipping{" "}
                    <span style={{ fontSize: "2vw", letterSpacing: "-0.2vw" }}>
                      Calculated at next step
                    </span>
                    {/* <p>Include long distance fee of for 100km</p> */}
                  </li>
                </div>
                <li
                  style={{
                    marginTop: "4%",
                    marginBottom: "0%",
                    color: "#000",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <b style={{ letterSpacing: "-0.2vw" }}>Total</b>{" "}
                  <span>
                    <b
                      style={{
                        fontSize: "8px",
                        marginBottom: "2px",
                        letterSpacing: "-0.2vw",
                      }}
                    >
                      Euro&nbsp;&nbsp;
                    </b>
                    {console.log(props.checkout)}
                    <b style={{ fontSize: "3.5vw" }}>
                      €
                      {props.checkout.cost && props.checkout.cost.totalAmount
                        ? props.checkout.cost.totalAmount.amount
                            .toString(10)
                            .split(".")[0].length > 3
                          ? props.checkout.cost.totalAmount.amount
                              .toString(10)
                              .substring(
                                0,
                                props.checkout.cost.totalAmount.amount
                                  .toString(10)
                                  .split(".")[0].length - 3,
                              ) +
                            "," +
                            props.checkout.cost.totalAmount.amount
                              .toString(10)
                              .substring(
                                props.checkout.cost.totalAmount.amount
                                  .toString(10)
                                  .split(".")[0].length - 3,
                              ) +
                            "0"
                          : props.checkout.cost.totalAmount.amount + "0"
                        : 0.0}
                    </b>
                  </span>
                </li>
              </ul>
            </div>
          </div>
          <div className="NewCartOpenSection">
            {/* <div className="Policy" style={{ height: Policy1height, paddingLeft: "0%", paddingRight: "0%" }}>
                    <button onClick={() => SwitchDisplayPolices("D1")} style={{ paddingLeft: "0%", paddingRight: "0%" }}>
                        CANCELLATION POLICY <img src={policy1img} />
                    </button>
                </div> */}
            <div className="CartBodyMessage">
              By purchasing INCORP products you become part of the pionners
              building a self sustaining healing arts platform.
            </div>
            <div
              className="Policy"
              style={{
                height: Policy2height,
                paddingLeft: "0%",
                paddingRight: "0%",
              }}
            >
              <button
                onClick={() => SwitchDisplayPolices("D2")}
                style={{ paddingLeft: "0%", paddingRight: "0%" }}
              >
                RETURN policy <img src={policy2img} />
              </button>
              <p style={{ height: Policy2height, display: PolicyP2 }}>
                INCORP products are unique art pieces each numbered and are non
                refundable.
              </p>
            </div>
          </div>
          <div className="NewMobileCartFooter">
            <button className="NewMobileCartCheckout" onClick={openCheckout}>
              checkout
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default MobileNewCart;
