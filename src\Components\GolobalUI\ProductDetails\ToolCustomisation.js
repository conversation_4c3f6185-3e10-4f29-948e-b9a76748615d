import React, { Component } from "react";
import "./ProductDetails.css";

class ToolCustomisation extends Component {

  customiseObsidian = () => {
    let event = new CustomEvent("customiseObsidian", {});
    window.dispatchEvent(event);
  }

  customiseOpal = () => {
    let event = new CustomEvent("customiseOpal", {});
    window.dispatchEvent(event);
  }

  render() {
    return (
        <div className="ToolCustomisation" style={{display: this.props.ToolCustomisationDisplay, animation: this.props.ToolsCustAnimation}}>
            <button onClick={() => this.customiseObsidian()}  style={{width: "40px", height: "40px", border: "none", padding:"0px", backgroundColor: "transparent"}}> 
                <img className="toolsCustButtons" src= "Incorp-ToolObsidian.png" />
            </button>
            <div style={{width: "1px", height: "30px", backgroundColor: "#000"}}></div>
            <button onClick={() => this.customiseOpal()} style={{width: "40px", height: "40px", border: "none", padding:"0px", backgroundColor: "transparent", borderTop: "0px solid black"}}> 
                <img className="toolsCustButtons" src= "Incorp-ToolOpal.png" />
            </button>
        </div>
    );
  }
}

export default ToolCustomisation;
