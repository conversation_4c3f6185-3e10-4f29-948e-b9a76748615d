import React, { Component } from "react";
import { withMediaProps } from "react-media-player";
import "./CustomPlayPause.css";

class CustomVideoPlay extends Component {
  shouldComponentUpdate({ media }) {
    return this.props.media.isPlaying !== media.isPlaying;
  }

  _handlePlayPause = () => {
    this.props.media.playPause();
  };

  render() {
    const { media } = this.props;
    return (
      <button className="PlayerContainerVideo" onClick={this._handlePlayPause}>
        {media.isPlaying ? <img src="pause-button.png" /> : <img src="play-buttton.png" />}
      </button>
    );
  }
}

export default withMediaProps(CustomVideoPlay);
