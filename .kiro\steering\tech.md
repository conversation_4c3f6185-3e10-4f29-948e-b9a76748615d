# Technology Stack

## Framework & Core Libraries

- **React 18.2.0**: Main UI framework with hooks-based architecture
- **Create React App**: Build system and development server
- **Babylon.js 6.35.0**: 3D rendering engine for interactive product visualization
- **GSAP 3.12.4**: Animation library for UI transitions and effects

## E-commerce & Data

- **Apollo Client 3.8.8**: GraphQL client for Shopify integration
- **Shopify Storefront API**: Product catalog, cart, and checkout management
- **GraphQL**: Query language for API communication

## UI & Styling

- **Bootstrap 5.3.2**: CSS framework for responsive layout
- **Custom CSS**: Caslon font family, custom animations, and 3D scene styling
- **React Burger Menu**: Mobile navigation component

## Physics & Interaction

- **Cannon.js**: Physics engine for 3D interactions
- **PEP.js**: Pointer events polyfill for touch support
- **Expo Sensors**: Device orientation and accelerometer support

## Development & Build

- **React Scripts 5.0.1**: Build tooling and webpack configuration
- **JavaScript Obfuscator**: Code protection for production builds
- **Netlify**: Deployment and hosting platform

## Common Commands

### Development

```bash
npm start                    # Start development server
npm test                     # Run test suite
npm run build               # Production build (Linux/Mac)
npm run winBuild            # Production build (Windows)
```

### Deployment

- **Staging**: Create PR to `staging-branch` - triggers automatic deployment
- **Production**: Manual trigger via GitHub Actions or PR to `production-branch`

## Build Configuration

- Source maps disabled in production (`GENERATE_SOURCEMAP=false`)
- Separate Windows build command for environment variable handling
- Netlify integration for continuous deployment
