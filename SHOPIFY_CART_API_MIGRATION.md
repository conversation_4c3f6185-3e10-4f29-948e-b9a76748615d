# Shopify Cart API Migration Guide

## Overview

This migration updates the INCORP app from the deprecated Shopify Checkout API to the new Cart API to comply with Shopify's April 1, 2025 deadline.

## Changes Made

### 1. Updated `src/checkout.js`

- **Replaced** `CheckoutFragment` with `CartFragment`
- **Updated** GraphQL schema to match Cart API structure:
  - `checkout.webUrl` → `cart.checkoutUrl`
  - `checkout.lineItems` → `cart.lines`
  - `lineItem.variant` → `line.merchandise`
  - `variant.price.amount` → `merchandise.price.amount`

### 2. New Mutations

| Old Checkout API            | New Cart API              |
| --------------------------- | ------------------------- |
| `checkoutCreate`            | `cartCreate`              |
| `checkoutLineItemsAdd`      | `cartLinesAdd`            |
| `checkoutLineItemsUpdate`   | `cartLinesUpdate`         |
| `checkoutLineItemsRemove`   | `cartLinesRemove`         |
| `checkoutCustomerAssociate` | `cartBuyerIdentityUpdate` |

### 3. Updated `src/App.js`

- **Renamed** all mutation hooks to use Cart API
- **Updated** variable structures:
  - `checkoutId` → `cartId`
  - `lineItems` → `lines`
  - `variantId` → `merchandiseId`
  - `lineItemIds` → `lineIds`
- **Modified** response data access patterns
- **Updated** customer association to use `buyerIdentity`

## Key API Differences

### Input Structure Changes

```javascript
// OLD Checkout API
{
  checkoutId: "...",
  lineItems: [{ variantId: "...", quantity: 1 }]
}

// NEW Cart API
{
  cartId: "...",
  lines: [{ merchandiseId: "...", quantity: 1 }]
}
```

### Response Structure Changes

```javascript
// OLD: Access product title
res.data.checkoutLineItemsAdd.checkout.lineItems.edges[0].node.title;

// NEW: Access product title
res.data.cartLinesAdd.cart.lines.edges[0].node.merchandise.product.title;
```

## Testing Required

### 1. Cart Creation

- Verify initial cart creation on app load
- Check that cart ID is properly stored

### 2. Add to Cart

- Test adding products to cart
- Verify product titles display correctly in notifications
- Check quantity tracking

### 3. Cart Updates

- Test quantity increases/decreases
- Verify mobile and desktop update functions
- Test removing items completely

### 4. Customer Association

- Test user login/registration
- Verify cart persists after authentication

### 5. Checkout Flow

- Ensure cart redirects to proper checkout URL
- Test final purchase flow

## Potential Issues to Watch

1. **Response Structure**: The new Cart API has different nested structures - watch for undefined errors
2. **Line Item IDs**: Cart line IDs may differ from checkout line item IDs
3. **Currency Codes**: New API includes currency codes that weren't in old structure
4. **Checkout URL**: Verify the `checkoutUrl` from cart works properly

## Rollback Plan

If issues arise, the old mutations are temporarily aliased in `checkout.js` for quick rollback, but this is only a temporary measure as the old API will stop working April 1, 2025.

## Next Steps

1. Test all cart functionality thoroughly
2. Update any other components that directly access cart/checkout data
3. Consider updating UI to take advantage of new Cart API features
4. Remove legacy aliases once migration is confirmed working
