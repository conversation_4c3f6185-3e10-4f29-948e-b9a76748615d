// src/App.js
import React, { Component } from "react";
import "./MobileProductSwitcher.css";
import useState from "react-usestateref";

// import ""
class MobileProductSwitcher extends Component {
  state = { 
    counterforbig: 0,
    counterforsmall: 0,
    IBorder: "1.5px solid black",
    YouBorder: "none",
    WeBorder: "none",
    IBG: "#fff",
    YouBG: "#000",
    WeBG: "#000",
    IColor: "#000",
    YouColor: "#fff",
    WeColor: "#fff",
    ILeft: "none",
    YouLeft: "",
    WeLeft: "none",
    IRight: "none",
    YouRight: "none",
    WeRight: "",
    counterforibig:0,counterforismall:0,counterforwebig:0,counterforwesmall:0,counterforyoubig:0,counterforyousmall:0,counterforobsidian:0,counterforopal:0,counterfortiger:0,
   };

  constructor(props) {
    super(props);
  }

  
  switcherButtonClicked = (bottle) => {
    let event = new CustomEvent("switcherButtonPressed", {
      detail: bottle
    });
    window.dispatchEvent(event);
    if (bottle == "I") {
      this.setState({
        IBorder: "1.5px solid black",
        YouBorder: "none",
        WeBorder: "none",
        IBG: "#fff",
        YouBG: "#000",
        WeBG: "#000",
        IColor: "#000",
        YouColor: "#fff",
        WeColor: "#fff",
      })
      let eventToolObsidian = new CustomEvent("customiseObsidian", {});
      window.dispatchEvent(eventToolObsidian);
    }
    if (bottle == "You") {
      this.setState({
        IBorder: "none",
        YouBorder: "1.5px solid black",
        WeBorder: "none",
        IBG: "#000",
        YouBG: "#fff",
        WeBG: "#000",
        IColor: "#fff",
        YouColor: "#000",
        WeColor: "#fff",
      })
      let eventToolOpal = new CustomEvent("customiseOpalInBackground", {});
      window.dispatchEvent(eventToolOpal);
    }
    if (bottle == "We") {
      this.setState({
        IBorder: "none",
        YouBorder: "none",
        WeBorder: "1.5px solid black",
        IBG: "#000",
        YouBG: "#000",
        WeBG: "#fff",
        IColor: "#fff",
        YouColor: "#fff",
        WeColor: "#000",
      })
      let eventToolObsidian = new CustomEvent("customiseObsidian", {});
      window.dispatchEvent(eventToolObsidian);
    }
    let eventTool = new CustomEvent("ToolsCustAnimationHide", {});
    window.dispatchEvent(eventTool);
  }
  switcherButtonClickedModified = (bottle) => {
    let event = new CustomEvent("switcherButtonPressed", {
      detail: bottle
    });
    window.dispatchEvent(event);
    if (bottle == "I") {
      this.setState({
        IBorder: "1.5px solid black",
        YouBorder: "none",
        WeBorder: "none",
        IBG: "#fff",
        YouBG: "#000",
        WeBG: "#000",
        IColor: "#000",
        YouColor: "#fff",
        WeColor: "#fff",
        ILeft: "none",
        YouLeft: "",
        WeLeft: "none",
        IRight: "none",
        YouRight: "none",
        WeRight: "",
      })
      let eventToolObsidian = new CustomEvent("customiseObsidian", {});
      window.dispatchEvent(eventToolObsidian);
    }
    if (bottle == "You") {
      this.setState({
        IBorder: "none",
        YouBorder: "1.5px solid black",
        WeBorder: "none",
        IBG: "#000",
        YouBG: "#fff",
        WeBG: "#000",
        IColor: "#fff",
        YouColor: "#000",
        WeColor: "#fff",
        ILeft: "none",
        YouLeft: "none",
        WeLeft: "",
        IRight: "",
        YouRight: "none",
        WeRight: "none",
      })
      let eventToolOpal = new CustomEvent("customiseOpalInBackground", {});
      window.dispatchEvent(eventToolOpal);
    }
    if (bottle == "We") {
      this.setState({
        IBorder: "none",
        YouBorder: "none",
        WeBorder: "1.5px solid black",
        IBG: "#000",
        YouBG: "#000",
        WeBG: "#fff",
        IColor: "#fff",
        YouColor: "#fff",
        WeColor: "#000",
        ILeft: "",
        YouLeft: "none",
        WeLeft: "none",
        IRight: "none",
        YouRight: "",
        WeRight: "none",
      })
      let eventToolObsidian = new CustomEvent("customiseObsidian", {});
      window.dispatchEvent(eventToolObsidian);
    }
    let eventTool = new CustomEvent("ToolsCustAnimationHide", {});
    window.dispatchEvent(eventTool);
  }

  increasecounterforbig=()=>{
    if(this.props.PDITitle == "I" ){
      this.setState({
       counterforibig:this.state.counterforibig+1
      })
    }
    else if(this.props.PDITitle == "WE"){
      this.setState({
        counterforwebig:this.state.counterforwebig+1
       })
     }
    
   else if (this.props.PDITitle == "YOU"){
    this.setState({
      counterforyoubig:this.state.counterforyoubig+1
     })
   }
  }

  IncreaseCounterForobsedian=()=>{
    this.setState({
      counterforobsidian:this.state.counterforobsidian+1
     })
  }
  IncreaseCounterForopal=()=>{
    this.setState({
      counterforopal:this.state.counterforopal+1
     })
  }
  IncreaseCounterFortiger=()=>{
    this.setState({
      counterfortiger:this.state.counterfortiger+1
     })
  }

  
  DecreaseCounterForobsedian=()=>{
    if(this.state.counterforobsidian>0){
    this.setState({
      counterforobsidian:this.state.counterforobsidian-1
     })
    }
  }
  DecreaseCounterForopal=()=>{
    if(this.state.counterforopal>0){
    this.setState({
      counterforopal:this.state.counterforopal-1
     })
    }
  }
  DecreaseCounterFortiger=()=>{
    if(this.state.counterfortiger>0){
    this.setState({
      counterfortiger:this.state.counterfortiger-1
     })
    }
  }

  increasecounterforsmall=()=>{
    if(this.props.PDITitle == "I" ){
      this.setState({
       counterforismall:this.state.counterforismall+1
      })
    }
    else if(this.props.PDITitle == "WE"){
      this.setState({
        counterforwesmall:this.state.counterforwesmall+1
       })
     }
    
   else if (this.props.PDITitle == "YOU"){
    this.setState({
      counterforyousmall:this.state.counterforyousmall+1
     })
   }
  }

  decreasecounterforbig=()=>{
    if(this.props.PDITitle == "I" ){
      if(this.state.counterforibig>0){
         this.setState({
       counterforibig:this.state.counterforibig-1
      })
      }
     
    }
    else if(this.props.PDITitle == "WE"){
      if(this.state.counterforwebig>0){

      this.setState({
        counterforwebig:this.state.counterforwebig-1
       })
      }
     }
    
   else if (this.props.PDITitle == "YOU"){
    if(this.state.counterforyoubig>0){
    this.setState({
      counterforyoubig:this.state.counterforyoubig-1
     })
    }
   }
  }

  decreasecounterforsmall=()=>{
    if(this.state.counterforismall>0){
    if(this.props.PDITitle == "I" ){
      this.setState({
       counterforismall:this.state.counterforismall-1
      })
    }
    }
    else if(this.props.PDITitle == "WE"){
      if(this.state.counterforwesmall>0){
      this.setState({
        counterforwesmall:this.state.counterforwesmall-1
       })
      }
     }
    
   else if (this.props.PDITitle == "YOU"){
    if(this.state.counterforyousmall>0){
    this.setState({
      counterforyousmall:this.state.counterforyousmall-1
     })
    }
   }
  }

  returncurrentcounter=()=>{
    if(this.props.PDITitle == "I" ){
      return this.state.counterforibig
    }
    else if(this.props.PDITitle == "WE"){
      return this.state.counterforwebig

     }
    
   else if (this.props.PDITitle == "YOU"){
    return this.state.counterforyoubig

   }
  }
  returncurrentcounterforsmall=()=>{
    if(this.props.PDITitle == "I" ){
      return this.state.counterforismall
    }
    else if(this.props.PDITitle == "WE"){
      return this.state.counterforwesmall

     }
    
   else if (this.props.PDITitle == "YOU"){
    return this.state.counterforyousmall

   }
  }
  addtocart = () => {
    setTimeout(() => {
      this.props.ShopPageDisplaySwitchSpecial("open")
    }, 500);
    // console.log(this.state.counterforobsidian,this.state.counterforopal)
    if(this.state.counterforibig>0){
      
        setTimeout(() => {
          this.props.addToCartmobile(this.state.counterforibig,"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE1OTEzNjAwMg==")

        }, 1000);
    }
    if(this.state.counterforismall>0){

      this.props.addToCartmobile(this.state.counterforismall,"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODAzMzQ3MDcyMg==")
    }
    if(this.state.counterforyoubig>0){
      setTimeout(() => {
      this.props.addToCartmobile(this.state.counterforyoubig,"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM3MTQ4OTY2MTE4Ng==")
    }, 1000);
    }
    if(this.state.counterforyousmall>0){
      this.props.addToCartmobile(this.state.counterforyousmall,"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE2MDExOTA0Mg==")
    }
    if(this.state.counterforwesmall>0){
      this.props.addToCartmobile(this.state.counterforwesmall,"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE1OTg4OTY2Ng==")
    }
    if(this.state.counterforwebig>0){
      setTimeout(() => {
      this.props.addToCartmobile(this.state.counterforwebig,"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM3MTQ5NTM5NTU4Ng==")
    }, 1000);
    }

    if(this.state.counterforobsidian>0){
      setTimeout(() => {

      this.props.addToCartmobile(this.state.counterforobsidian,"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE2ODAxNjEzMA==")
    }, 1500);
    }
    if(this.state.counterforopal>0){
      setTimeout(() => {

      this.props.addToCartmobile(this.state.counterforopal,"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE5Mjc1NTk3MA==")
    }, 500);
    }
    if(this.state.counterfortiger>0){
      setTimeout(() => {

      this.props.addToCartmobile(this.state.counterfortiger,"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80Mzc3Nzk4MDg1ODYyNg==")
    }, 500);
    }
    if(this.props.currentModel == "toolObsidian"){
      if(this.state.counterforobsidian==0){
        setTimeout(() => {
          this.props.addToCartmobile(1,"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE2ODAxNjEzMA==")
        }, 500);
      }
    } else if (this.props.currentModel == "toolOpal"){
      if(this.state.counterforopal==0){
        setTimeout(() => {
          this.props.addToCartmobile(1,"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE5Mjc1NTk3MA==")
        }, 500);
      }
    } else if (this.props.currentModel == "toolTiger"){
      if(this.state.counterfortiger==0){
        setTimeout(() => {
          this.props.addToCartmobile(1,"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80Mzc3Nzk4MDg1ODYyNg==")
        }, 500);
      }
    } else if (this.props.currentModel == "body1"){
      if(this.state.counterforibig==0){
        setTimeout(() => {
          this.props.addToCartmobile(1,"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE1OTEzNjAwMg==")
        }, 500);
      }
    } else if (this.props.currentModel == "body2"){
      if(this.state.counterforyoubig==0){
        setTimeout(() => {
          this.props.addToCartmobile(1,"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM3MTQ4OTY2MTE4Ng==")
        }, 500);
      }
    } else if (this.props.currentModel == "body3"){
      if(this.state.counterforwebig==0){
        setTimeout(() => {
          this.props.addToCartmobile(1,"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM3MTQ5NTM5NTU4Ng==")
        }, 500);
      }
    } else {
      if (this.props.PDITitle == "I") {
        if(this.state.counterforismall==0){
          setTimeout(() => {
            this.props.addToCartmobile(1,"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODAzMzQ3MDcyMg==")
          }, 500);
        }
      } else if (this.props.PDITitle == "YOU") {
        if(this.state.counterforyousmall==0){
          setTimeout(() => {
            this.props.addToCartmobile(1,"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE2MDExOTA0Mg==")
          }, 500);
        }
      } else if (this.props.PDITitle == "WE") {
        if(this.state.counterforwesmall==0){
          setTimeout(() => {
            this.props.addToCartmobile(1,"Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE1OTg4OTY2Ng==")
          }, 500);
        }
      }
    }

    setTimeout(() => {
      this.setState({
        counterforibig:0,counterforismall:0,counterforwebig:0,counterforwesmall:0,counterforyoubig:0,counterforyousmall:0,counterforobsidian:0,counterforopal:0, counterfortiger:0,
      })
    }, 2000);
   
  }

  productClick = (product) => {
    let eventProductClick = new CustomEvent("productClick", {detail: product});
    window.dispatchEvent(eventProductClick);
  }
    
  render() {
    return (
      <div className="MobileProductDetails" style={{animation: "fadeIn 1.5s"}}>
        <div className="MobileThreeButtonsSwitcher">
          {/* <button onClick={() => this.switcherButtonClicked("I")} style={{borderBottom: this.state.IBorder, backgroundColor: this.state.IBG, color: this.state.IColor}}>I</button>
          <button onClick={() => this.switcherButtonClicked("You")} style={{borderBottom: this.state.YouBorder, backgroundColor: this.state.YouBG, color: this.state.YouColor}}>You</button>
          <button onClick={() => this.switcherButtonClicked("We")} style={{borderBottom: this.state.WeBorder, backgroundColor: this.state.WeBG, color: this.state.WeColor}}>We</button> */}
          
          <button onClick={() => this.switcherButtonClickedModified("I")} style={{backgroundColor: "transparent",height: "80px", width: "44px", position: "absolute", left: "-13%", top: "10%", display: this.state.ILeft}}>
            <img style={{height: "80px", width: "44px"}} src= "I_trans.png" />
          </button>
          <button onClick={() => this.switcherButtonClickedModified("You")} style={{backgroundColor: "transparent",height: "80px", width: "44px", position: "absolute", left: "-13%", top: "10%", display: this.state.YouLeft}}>
            <img style={{height: "80px", width: "44px"}} src= "You_trans.png" />
          </button>
          <button onClick={() => this.switcherButtonClickedModified("We")} style={{backgroundColor: "transparent",height: "80px", width: "44px", position: "absolute", left: "-13%", top: "10%", display: this.state.WeLeft}}>
            <img style={{height: "80px", width: "44px"}} src= "We_trans.png" />
          </button>

          <button onClick={() => this.switcherButtonClickedModified("I")} style={{backgroundColor: "transparent",height: "80px", width: "44px", position: "absolute", right: "-13%", top: "10%", display: this.state.IRight}}>
            <img style={{height: "80px", width: "44px"}} src= "I_trans.png" />
          </button>
          <button onClick={() => this.switcherButtonClickedModified("You")} style={{backgroundColor: "transparent",height: "80px", width: "44px", position: "absolute", right: "-13%", top: "10%", display: this.state.YouRight}}>
            <img style={{height: "80px", width: "44px"}} src= "You_trans.png" />
          </button>
          <button onClick={() => this.switcherButtonClickedModified("We")} style={{backgroundColor: "transparent",height: "80px", width: "44px", position: "absolute", right: "-13%", top: "10%", display: this.state.WeRight}}>
            <img style={{height: "80px", width: "44px"}} src= "We_trans.png" />
          </button>
        </div>
        <div className="MobileProductDetailsTitle" style={{opacity: this.props.productTitleOpacity, animation: this.props.productTitleAnimation}} onClick={()=> this.props.showProductDetailMobile("show")}>
          {this.props.PDICardLine.substr(0,1) == "I" ? (
            <div>
            <span style={{fontStyle: "normal"}}>I</span><br></br>
            <span style={{fontStyle: "italic"}}>{this.props.PDICardLine.substr(0,4)}</span>
            <span>{this.props.PDICardLine.substr(4, this.props.PDICardLine.indexOf('.')-2)}</span><br></br>
            <span style={{fontStyle: "italic"}}>{this.props.PDICardLine.substr(0,4)}</span>
            <span>{this.props.PDICardLine.substr(this.props.PDICardLine.indexOf('.')+6,this.props.PDICardLine.length)}</span>
            </div>
          ) : (
          this.props.PDICardLine.substr(0,3) == "YOU" ? (
            <div>
            <span style={{fontStyle: "normal"}}>YOU</span><br></br>
            <span style={{fontStyle: "italic"}}>{this.props.PDICardLine.substr(0,7)}</span>
            <span>{this.props.PDICardLine.substr(7, this.props.PDICardLine.indexOf('.')-5)}</span><br></br>
            <span style={{fontStyle: "italic"}}>{this.props.PDICardLine.substr(0,7)}</span>
            <span>{this.props.PDICardLine.substr(this.props.PDICardLine.indexOf('.')+9,this.props.PDICardLine.length)}</span>
            </div>
          ) : (
            <div>
            <span style={{fontStyle: "normal"}}>WE</span><br></br>
            <span style={{fontStyle: "italic"}}>{this.props.PDICardLine.substr(0,6)}</span>
            <span>{this.props.PDICardLine.substr(6, this.props.PDICardLine.indexOf('.')-4)}</span><br></br>
            <span style={{fontStyle: "italic"}}>{this.props.PDICardLine.substr(0,6)}</span>
            <span>{this.props.PDICardLine.substr(this.props.PDICardLine.indexOf('.')+8,this.props.PDICardLine.length)}</span>
            </div>
          )
          )}
        </div>
        <div className="MobileFooterContainer">
          <div className="MobileProductCounterContainerV2">
          <div className="MobileProductCounterContainerV2Row1">
            {this.props.PDITitle == "I" ? <img style={{height: "34px", width: "17px", marginLeft: "4px", marginRight:"4px"}} src= "I_trans.png" onClick={() => this.productClick("Product")}/> : ""}
            {this.props.PDITitle == "WE" ? <img style={{height: "34px", width: "17px", marginLeft: "4px", marginRight:"4px"}} src= "We_trans.png" onClick={() => this.productClick("Product")}/> : ""}
            {this.props.PDITitle == "YOU" ? <img style={{height: "34px", width: "17px", marginLeft: "4px", marginRight:"4px"}} src= "You_trans.png" onClick={() => this.productClick("Product")}/> : ""}
            <span style={{borderBottom: this.props.bodyUnderline, marginLeft: "4%", marginRight: "10%"}} onClick={() => this.productClick("Product")}>
              {this.props.PDITitle == "I" ? "33ml €1,415" : ""}
              {this.props.PDITitle == "WE" ? "33ml €1,385" : ""}
              {this.props.PDITitle == "YOU" ? "33ml €1,485" : ""}
            </span>
            {this.props.PDITitle == "I" ? <img style={{height: "31px", width: "25px", marginLeft: "1px", marginRight:"1px"}} src= "ISmallNoChain.png" onClick={() => this.productClick("ProductSmall")}/> : ""}
            {this.props.PDITitle == "WE" ? <img style={{height: "31px", width: "25px", marginLeft: "1px", marginRight:"1px"}} src= "WeSmallNoChain.png" onClick={() => this.productClick("ProductSmall")}/> : ""}
            {this.props.PDITitle == "YOU" ? <img style={{height: "31px", width: "25px", marginLeft: "1px", marginRight:"1px"}} src= "YouSmallNoChain.png" onClick={() => this.productClick("ProductSmall")}/> : ""}
            <span style={{borderBottom: this.props.bodySmallUnderline, marginLeft: "4%", marginRight: "10%"}} onClick={() => this.productClick("ProductSmall")}>
              {this.props.PDITitle == "I" ? "5ml €565" : ""}
              {this.props.PDITitle == "WE" ? "5ml €535" : ""}
              {this.props.PDITitle == "YOU" ? "5ml €635" : ""}
            </span>
            <div style={{height: "25px", width: "20vw"}}></div>
          </div>
          <div className="MobileProductCounterContainerV2Row2">
            <img style={{height: "25px", width: "25px"}} src= "Incorp-ToolObsidian.png" onClick={() => this.productClick("ObsidianTool")}/>
            <span style={{marginLeft: "4%", marginRight: "10%", borderBottom: this.props.toolObsidian}} onClick={() => this.productClick("ObsidianTool")}>
              €125
            </span>
            <img style={{height: "25px", width: "25px"}} src= "Incorp-ToolOpal.png" onClick={() => this.productClick("OpalTool")}/>
            <span style={{marginLeft: "4%", marginRight: "10%", borderBottom: this.props.toolOpal}} onClick={() => this.productClick("OpalTool")}>
              €145
            </span>
            <img style={{height: "25px", width: "25px"}} src= "Incorp-ToolTiger.png" onClick={() => this.productClick("TigerTool")}/>
            <span style={{marginLeft: "4%", marginRight: "10%", borderBottom: this.props.toolTiger}} onClick={() => this.productClick("TigerTool")}>
              €165
            </span>
          </div>
          {/* <div className="MobileCounterProduct">
            <button
              className="MobileBD"
              onClick={() => this.decreasecounterforbig()}
            >
              -
            </button>
            <span>{this.returncurrentcounter()}</span>
            <button
              className="MobileBI"
              onClick={() => this.increasecounterforbig()}
            >
              +
            </button>
          </div> */}
          {/* <div className="MobileCounterProduct">
            <button
             className="MobileBD"
             onClick={() => this.decreasecounterforsmall()}>
              -
            </button>
            <span> {this.returncurrentcounterforsmall()}</span>
            <button
             className="MobileBI"
             onClick={() => this.increasecounterforsmall()}>
              +
            </button>
          </div> */}
          {/* <div className="MobileCounterProduct">
            <button
              className="MobileBD"
              onClick={() => this.DecreaseCounterForobsedian()}
            >
              -
            </button>
            <span>{this.state.counterforobsidian}</span>
            <button
              className="MobileBI"
              onClick={() => this.IncreaseCounterForobsedian()}
            >
              +
            </button>
          </div> */}
          {/* <span>Tool</span>
          <div className="MobileCounterProduct" id="Mtool">
            Opal or Onyx
          </div> */}
          {/* <div className="MobileCounterProduct">
            <button
              className="MobileBD"
              onClick={() => this.DecreaseCounterForopal()}
            >
              -
            </button>
            <span>{this.state.counterforopal}</span>
            <button
              className="MobileBI"
              onClick={() => this.IncreaseCounterForopal()}
            >
              +
            </button>
          </div> */}
        </div>
        <div className="MobileAddCart">
            <button onClick={() => this.addtocart()}>Add to cart</button>
          </div>
        </div>
      </div>
    );
  }
}

export default MobileProductSwitcher;
