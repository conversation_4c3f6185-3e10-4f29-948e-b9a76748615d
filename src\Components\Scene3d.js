import React, { Component } from "react";

import {
  Scene,
  Engine,
  AssetsManager,
  ArcRotateCamera,
  CubicEase,
  Animation,
  Vector3,
  AbstractMesh,
  HemisphericLight,
  QuadraticEase,
  CircleEase,
  PhysicsImpostor,
  CannonJSPlugin,
  AmmoJSPlugin,
  BackEase,
  BounceEase,
  ElasticEase,
  ExponentialEase,
  PowerEase,
  QuarticEase,
  QuinticEase,
  SineEase,
  BezierCurveEase,
  SpotLight,
  PointLight,
  ShadowGenerator,
  FxaaPostProcess,
  PassPostProcess,
  ConvolutionPostProcess,
  TonemapPostProcess,
  TonemappingOperator,
  DefaultRenderingPipeline,
  ImageProcessingPostProcess,
  ParticleSystem,
  EasingFunction,
  Mesh,
  Material,
  SceneLoader,
  Layer,
  Color3,
  Color4,
  Tools,
  HighlightLayer,
  MeshBuilder,
  Texture,
  FreeCamera,
  DirectionalLight,
  PhotoDome,
  HDRCubeTexture,
  CubeTexture,
  StandardMaterial,
  PBRMaterial,
  PBRMetallicRoughnessMaterial,
  ReflectionProbe,
  FresnelParameters,
  RefractionTexture,
  BackgroundMaterial,
  MirrorTexture,
  Plane,
  Quaternion,
  ActionManager,
  SwitchBooleanAction,
  ExecuteCodeAction,
  PointerEventTypes,
  KeyboardEventTypes,
  Axis,
  GlowLayer,
  Space,
  Matrix,
  DefaultLoadingScreen,
  EnvironmentTextureTools,
  AxesViewer,
} from "babylonjs";
import "babylonjs-loaders";
import { TweenMax, Power2 } from "gsap";
import { multiply } from "lodash";

ArcRotateCamera.prototype.moveTargetTo2 = function (newPos, speed) {
  var ease = new CubicEase();
  // console.log(this.target, newPos);
  ease.setEasingMode(EasingFunction.EASINGMODE_EASEINOUT);
  // if (window.innerWidth <= 470) {
  //   this.target.y=0.04
  // }
  // this.target.z=0.528
  Animation.CreateAndStartAnimation(
    "at5",
    this,
    "target",
    100,
    120,
    this.target,
    newPos,
    0,
    ease
  );
};

ArcRotateCamera.prototype.spinTo = function (whichprop, targetval, speed) {
  let ease = new CubicEase();
  ease.setEasingMode(EasingFunction.EASINGMODE_EASEINOUT);
  Animation.CreateAndStartAnimation(
    "at4",
    this,
    whichprop,
    speed,
    120,
    this[whichprop],
    targetval,
    0,
    ease
  );
};

ArcRotateCamera.prototype.spinToMouseMovment = function (
  whichprop,
  targetval,
  speed
) {
  // let ease = new CubicEase();
  // let ease = new CircleEase();

  // let ease = new ElasticEase();

  // let ease = new ExponentialEase();
  // let ease = new PowerEase();

  // let ease = new QuinticEase();

  // let ease = new SineEase();
  // let ease = new BezierCurveEase();

  let ease = new QuadraticEase();

  // let ease = new QuarticEase();

  // console.log(whichprop, targetval, speed)
  ease.setEasingMode(EasingFunction.EASINGMODE_EASEINOUT);
  Animation.CreateAndStartAnimation(
    "at4",
    this,
    whichprop,
    speed,
    200,
    this[whichprop],
    targetval,
    0,
    ease
  );
};

ArcRotateCamera.prototype.spinTo = function (whichprop, targetval, speed) {
  let ease = new CubicEase();
  ease.setEasingMode(EasingFunction.EASINGMODE_EASEINOUT);
  Animation.CreateAndStartAnimation(
    "at4",
    this,
    whichprop,
    speed,
    120,
    this[whichprop],
    targetval,
    0,
    ease
  );
};
ArcRotateCamera.prototype.moveTargetTo = function (newPos, speed) {
  var ease = new CubicEase();
  // console.log(this.target, newPos);
  ease.setEasingMode(EasingFunction.EASINGMODE_EASEINOUT);
  // if (window.innerWidth <= 470) {
  //   this.target.y=0.04
  // }
  Animation.CreateAndStartAnimation(
    "at5",
    this,
    "target",
    100,
    120,
    this.target,
    newPos,
    0,
    ease
  );
};
var bottlespositon = [1, 2, 3];
var counter = 1;

// Here we extend Reacts component class
class Scene3 extends Component {
  // Makes the canvas behave responsively

  onResizeWindow = () => {
    if (this.engine) {
      this.engine.resize();
    }
  };

  // Sets up our canvas tag for webGL scene
  setEngine = () => {
    this.engine = new Engine(this.stage, true, {
      antialias: true,
      stencil: true,
      preserveDrawingBuffer: true,
    });
    if (window.devicePixelRatio < 2) {
      this.engine.setHardwareScalingLevel(1 / 2);
    } else {
      this.engine.setHardwareScalingLevel(1 / window.devicePixelRatio);
    }
    this.stage.style.width = "100%";
    this.stage.style.height = "100vh";
    this.stage.style.outline = "none";
    this.stage.style.boxShadow = "none";
    this.engine.resize();
  };

  // Creates the scene graph
  setScene = () => {
    this.scene = new Scene(this.engine);
    // this.scene.clearColor = new Color4.FromInts(252, 248, 240, 255).toLinearSpace();
    // this.scene.clearColor = new Color4.FromInts(252, 248, 240, 255); //beige
    // this.scene.clearColor = new Color4.FromInts(240, 240, 240, 255); //gray
    this.scene.clearColor = new Color4.FromInts(236, 231, 231, 255); //gray warmer
    // this.scene.debugLayer.show();
    // var gravityVector = new Vector3(0,0, 0);
    // var physicsPlugin =  new CannonJSPlugin(true, 5, cannon);
    // this.scene.enablePhysics(gravityVector, physicsPlugin);
    // this.scene.enablePhysics();
    // this.scene.enablePhysics(new Vector3(0,-10,0), new AmmoJSPlugin());
    // const Axes = new AxesViewer(this.scene, 0.5);
  };

  setCamera = () => {
    this.camera = new ArcRotateCamera(
      "Camera",
      1.6918,
      1.5658,
      3.4,
      new Vector3(0.069, 1.65, 0.48),
      this.scene
    );
    this.camera.rotation.x = Tools.ToRadians(90);
    this.camera.upVector = new Vector3(0.176, 0, -1);
    this.camera.detachControl(this.stage, true);
    this.camera.panningSensibility = 0;
    this.camera.wheelPrecision = 250;
    this.camera.pinchPrecision = 80;
    this.camera.minZ = 0.01;
    // this.camera.fov = 0.5543;
    this.camera.fov = 0.5;
    this.camera.Plane = 10000.0;
    this.camera.lowerRadiusLimit = 2.2933;
    this.camera.upperRadiusLimit = 3.4;

    this.aboutCamera = new ArcRotateCamera(
      "aboutCamera",
      4.8902,
      1.5705,
      3.4,
      new Vector3(0.069, 7.385, 0.72),
      this.scene
    );
    this.aboutCamera.rotation.x = Tools.ToRadians(90);
    this.aboutCamera.upVector = new Vector3(0.176, 0, -1);
    this.aboutCamera.detachControl(this.stage, true);
    this.aboutCamera.panningSensibility = 0;
    this.aboutCamera.fov = 0.5;
    this.aboutCamera.Plane = 10000.0;
    this.aboutCamera.lowerRadiusLimit = 3.4;
    this.aboutCamera.upperRadiusLimit = 3.4;

    this.cameraStart = new ArcRotateCamera(
      "cameraStart",
      1.5707,
      1.2598,
      39.11,
      new Vector3(0.08, 0.55, -0.95),
      this.scene
    );
    this.cameraStart.detachControl(this.stage, true);
    this.cameraStart.panningSensibility = 0;
    this.cameraStart.wheelPrecision = 250;
    this.cameraStart.pinchPrecision = 80;
    this.cameraStart.minZ = 0.001;
    // this.cameraStart.fov = 0.5543;
    this.cameraStart.fov = 0.5;
    this.cameraStart.lowerRadiusLimit = 39.11;
    this.cameraStart.upperRadiusLimit = 5000;
    this.cameraStart.lowerAlphaLimit = -1.26;
    this.cameraStart.upperAlphaLimit = 4.4;
    this.cameraStart.inertia = 0.94;
    this.cameraStart.angularSensibilityX = 1500;
    this.cameraStart.angularSensibilityY = 1500;
  };

  constructor(props) {
    super(props);
    // We bind our event to keep the proper "this" context.
    this.state = {
      rotatable: true,
      rotating: false,
      galacticBottle: true,
      galacticTool: true,
      galacticSmall: true,
      currentModel: "body1",
      scenenow: "start",
      arrayofbottle: ["2", "1", "3"],
      leftbottle: "1",
      rightbottle: "3",
      positionRight: { x: -0.627, y: 1.686, z: 0.115 },
      positionLeft: { x: 0.843, y: 1.686, z: 0.395 },
      old_rotation: "",
      annotationstate: false,
      toolCurrentColor: "Obsidian",
    };
    this.loadModels = this.loadModels.bind(this);
    this.Startanimation = this.Startanimation.bind(this);
    this.bottlesetbesidecameraLeft = this.bottlesetbesidecameraLeft.bind(this);
    this.bottlesetbesidecameraRight =
      this.bottlesetbesidecameraRight.bind(this);
  }

  loadModels = async () => {
    var that = this;
    DefaultLoadingScreen.prototype.displayLoadingUI = function () {
      if (this._loadingDiv) {
        // Do not add a loading screen if there is already one

        return;
      }

      this._loadingDiv = document.createElement("div");
      this._loadingDiv.id = "babylonjsLoadingDiv";
      this._loadingDiv.style.opacity = "1";
      this._loadingDiv.style.transition = "opacity 1s ease";
      this._loadingDiv.style.pointerEvents = "none";
      this._loadingDiv.style.backgroundColor = "transparent";
      this._loadingDiv.style.color = "#000000";
      this._loadingDiv.style.display = "flex";
      this._loadingDiv.style.justifyContent = "center";
      // this._loadingDiv.style.width = "25px";
      this._loadingDiv.style.width = "325px";
      // this._loadingDiv.style.height = "50px";
      this._loadingDiv.style.height = "66px";
      this._loadingDiv.style.textAlign = "center";
      this._loadingDiv.style.position = "absolute";
      this._loadingDiv.style.top = "calc((100vh + 50px)/2)";
      this._loadingDiv.style.left = "calc((100vw - 325px)/2)";
      this._loadingDiv.style.fontSize = "16px";
      this._loadingDiv.style.fontWeight = "500";
      this._loadingDiv.style.font = "Caslon";
      document.body.appendChild(this._loadingDiv);
      this._parg = document.createElement("p");
      this._loadingDiv.appendChild(this._parg);
      this._parg.innerText = "0% \n \n \n \n 3D objects on this website are interactive and made to play with - click, grab, spin and turn!"
      this._parg.style.textAlign = "center";
      this._parg.style.fontStyle = 'italic';
      that.onLoading = (status) => {
        this._parg.innerText = `${status}% \n \n \n \n 3D objects on this website are interactive and made to play with - click, grab, spin and turn!`;
      };
    };

    this.scene.hoverCursor = "grab";

    //breakpoints
    if (window.innerWidth < 1110) {
      //perfect for 1060
      this.setState({
        positionRight: { x: -0.424, y: 1.686, z: 0.171 },
        positionLeft: { x: 0.642, y: 1.686, z: 0.376 },
      });
    } else if (window.innerWidth < 1210) {
      //perfect for 1160
      this.setState({
        positionRight: { x: -0.488, y: 1.686, z: 0.162 },
        positionLeft: { x: 0.694, y: 1.686, z: 0.386 },
      });
    } else if (window.innerWidth < 1310) {
      //perfect for 1260
      this.setState({
        positionRight: { x: -0.526, y: 1.686, z: 0.155 },
        positionLeft: { x: 0.74, y: 1.686, z: 0.395 },
      });
    } else if (window.innerWidth < 1410) {
      //perfect for 1360
      this.setState({
        positionRight: { x: -0.576, y: 1.686, z: 0.155 },
        positionLeft: { x: 0.78, y: 1.686, z: 0.397 },
      });
    } else if (window.innerWidth < 1520) {
      //perfect for 1460
      this.setState({
        positionRight: { x: -0.624, y: 1.686, z: 0.145 },
        positionLeft: { x: 0.823, y: 1.686, z: 0.414 },
      });
    } else {
      //greater than 1520px in width
      this.setState({
        positionRight: { x: -0.618, y: 1.686, z: 0.136 },
        positionLeft: { x: 0.815, y: 1.686, z: 0.423 },
      });
    }

    await SceneLoader.AppendAsync(
      "",
      "./HumanCamAnimation17BodyChange.glb",
      this.scene,
      (evt) => {
        let loadedPercentage = 0;
        if (evt.lengthComputable) {
          loadedPercentage = ((evt.loaded * 100) / evt.total).toFixed();
          } else {
          var count = evt.loaded / 15836036;
          // console.log(evt.loaded)
          loadedPercentage = Math.floor(count * 100);
          }
  
        that.onLoading(loadedPercentage);
      }
    );
    let Body1 = this.scene.getMeshByName("__root__");
    Body1.name = "body";
    Body1.freezeWorldMatrix();
    this.scene.getMeshById("Body").freezeWorldMatrix();
    this.scene.getMeshById("Body").material.freeze();
    this.scene.getCameraByName("VRayCam001").fov = 0.5543;

    var logo = this.scene.getMeshByName("KiraLogo001");
    logo.setParent(this.cameraStart);
    var logoMaterial = new PBRMaterial("logoMaterial", this.scene);
    logoMaterial.albedoColor = new Color3.FromInts(0, 0, 0).toLinearSpace();
    logoMaterial.roughness = 1;
    logoMaterial.metallic = 0;
    logo.material = logoMaterial;
    logoMaterial.alpha = 0;

    var rotatePlane = MeshBuilder.CreatePlane("rotatePlane", { height: 0.0944, width: 0.4 }, this.scene);
    var rotatePlaneMaterial = new StandardMaterial("rotatePlaneMaterial", this.scene);

    this.scene.activeCamera = this.cameraStart;
    this.cameraStart.radius = 5000;
    this.cameraStart.beta = 0;

    //points
    const mind1 = MeshBuilder.CreateSphere(
      "mind1",
      { diameter: 0.1 },
      this.scene
    );
    const mind1big = MeshBuilder.CreateSphere(
      "mind1big",
      { diameter: 1 },
      this.scene
    );
    mind1big.visibility = 0;
    mind1big.setEnabled(false);
    mind1.position = new Vector3(0.457, 7.612, 1.112);
    mind1big.position = new Vector3(0.457, 7.612, 1.112);
    mind1big.actionManager = new ActionManager(that.scene);
    mind1big.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPointerOverTrigger, function (
        mesh
      ) {
        that.props.BodyMapDisplaySwitcher("Mind");
      })
    );
    const mind2 = MeshBuilder.CreateSphere(
      "mind2",
      { diameter: 0.1 },
      this.scene
    );
    const mind2big = MeshBuilder.CreateSphere(
      "mind2big",
      { diameter: 1 },
      this.scene
    );
    mind2big.visibility = 0;
    mind2big.setEnabled(false);
    mind2.position = new Vector3(-0.16, 7.612, 1.112);
    mind2big.position = new Vector3(-0.16, 7.612, 1.112);
    mind2big.actionManager = new ActionManager(that.scene);
    mind2big.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPointerOverTrigger, function (
        mesh
      ) {
        that.props.BodyMapDisplaySwitcher("Mind");
      })
    );
    const truth = MeshBuilder.CreateSphere(
      "truth",
      { diameter: 0.1 },
      this.scene
    );
    const truthbig = MeshBuilder.CreateSphere(
      "truthbig",
      { diameter: 1 },
      this.scene
    );
    truthbig.visibility = 0;
    truthbig.setEnabled(false);
    truth.position = new Vector3(0.105, 5.709, 0.543);
    truthbig.position = new Vector3(0.105, 5.709, 0.543);
    truthbig.actionManager = new ActionManager(that.scene);
    truthbig.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPointerOverTrigger, function (
        mesh
      ) {
        that.props.BodyMapDisplaySwitcher("Truth");
      })
    );
    const heart = MeshBuilder.CreateSphere(
      "heart",
      { diameter: 0.1 },
      this.scene
    );
    const heartbig = MeshBuilder.CreateSphere(
      "heartbig",
      { diameter: 1 },
      this.scene
    );
    heartbig.visibility = 0;
    heartbig.setEnabled(false);
    heart.position = new Vector3(0.105, 4.404, 1.344);
    heartbig.position = new Vector3(0.105, 4.404, 1.344);
    heartbig.actionManager = new ActionManager(that.scene);
    heartbig.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPointerOverTrigger, function (
        mesh
      ) {
        that.props.BodyMapDisplaySwitcher("Heart");
      })
    );
    const center = MeshBuilder.CreateSphere(
      "center",
      { diameter: 0.1 },
      this.scene
    );
    const centerbig = MeshBuilder.CreateSphere(
      "centerbig",
      { diameter: 1 },
      this.scene
    );
    centerbig.visibility = 0;
    centerbig.setEnabled(false);
    center.position = new Vector3(0.105, 1.936, 1.344);
    centerbig.position = new Vector3(0.105, 1.936, 1.344);
    centerbig.actionManager = new ActionManager(that.scene);
    centerbig.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPointerOverTrigger, function (
        mesh
      ) {
        that.props.BodyMapDisplaySwitcher("Center");
      })
    );
    const relief1 = MeshBuilder.CreateSphere(
      "relief1",
      { diameter: 0.1 },
      this.scene
    );
    const relief1big = MeshBuilder.CreateSphere(
      "relief1big",
      { diameter: 1 },
      this.scene
    );
    relief1big.visibility = 0;
    relief1big.setEnabled(false);
    relief1.position = new Vector3(2.911, 0.824, 1.224);
    relief1big.position = new Vector3(2.911, 0.824, 1.224);
    relief1big.actionManager = new ActionManager(that.scene);
    relief1big.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPointerOverTrigger, function (
        mesh
      ) {
        that.props.BodyMapDisplaySwitcher("Relief");
      })
    );
    const relief2 = MeshBuilder.CreateSphere(
      "relief2",
      { diameter: 0.1 },
      this.scene
    );
    const relief2big = MeshBuilder.CreateSphere(
      "relief2big",
      { diameter: 1 },
      this.scene
    );
    relief2big.visibility = 0;
    relief2big.setEnabled(false);
    relief2.position = new Vector3(-2.786, 0.824, 1.224);
    relief2big.position = new Vector3(-2.786, 0.824, 1.224);
    relief2big.actionManager = new ActionManager(that.scene);
    relief2big.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPointerOverTrigger, function (
        mesh
      ) {
        that.props.BodyMapDisplaySwitcher("Relief");
      })
    );
    const energize1 = MeshBuilder.CreateSphere(
      "energize1",
      { diameter: 0.1 },
      this.scene
    );
    const energize1big = MeshBuilder.CreateSphere(
      "energize1big",
      { diameter: 1 },
      this.scene
    );
    energize1big.visibility = 0;
    energize1big.setEnabled(false);
    energize1.position = new Vector3(0.74, -3.208, 0.305);
    energize1big.position = new Vector3(0.74, -3.208, 0.305);
    energize1big.actionManager = new ActionManager(that.scene);
    energize1big.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPointerOverTrigger, function (
        mesh
      ) {
        that.props.BodyMapDisplaySwitcher("Energize");
      })
    );
    const energize2 = MeshBuilder.CreateSphere(
      "energize2",
      { diameter: 0.1 },
      this.scene
    );
    const energize2big = MeshBuilder.CreateSphere(
      "energize2big",
      { diameter: 1 },
      this.scene
    );
    energize2big.visibility = 0;
    energize2big.setEnabled(false);
    energize2.position = new Vector3(-0.476, -3.208, -0.014);
    energize2big.position = new Vector3(-0.476, -3.208, -0.014);
    energize2big.actionManager = new ActionManager(that.scene);
    energize2big.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPointerOverTrigger, function (
        mesh
      ) {
        that.props.BodyMapDisplaySwitcher("Energize");
      })
    );
    const ground1 = MeshBuilder.CreateSphere(
      "ground1",
      { diameter: 0.1 },
      this.scene
    );
    const ground1big = MeshBuilder.CreateSphere(
      "ground1big",
      { diameter: 1 },
      this.scene
    );
    ground1big.visibility = 0;
    ground1big.setEnabled(false);
    ground1.position = new Vector3(0.354, -6.429, 0.472);
    ground1big.position = new Vector3(0.354, -6.429, 0.472);
    ground1big.actionManager = new ActionManager(that.scene);
    ground1big.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPointerOverTrigger, function (
        mesh
      ) {
        that.props.BodyMapDisplaySwitcher("Ground");
      })
    );
    const ground2 = MeshBuilder.CreateSphere(
      "ground2",
      { diameter: 0.1 },
      this.scene
    );
    const ground2big = MeshBuilder.CreateSphere(
      "ground2big",
      { diameter: 1 },
      this.scene
    );
    ground2big.visibility = 0;
    ground2big.setEnabled(false);
    ground2.position = new Vector3(-0.61, -6.429, -0.243);
    ground2big.position = new Vector3(-0.61, -6.429, -0.243);
    ground2big.actionManager = new ActionManager(that.scene);
    ground2big.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPointerOverTrigger, function (
        mesh
      ) {
        that.props.BodyMapDisplaySwitcher("Ground");
      })
    );
    this.scene.getMeshByName("mind1").visibility = 0;
    this.scene.getMeshByName("mind2").visibility = 0;
    this.scene.getMeshByName("truth").visibility = 0;
    this.scene.getMeshByName("heart").visibility = 0;
    this.scene.getMeshByName("center").visibility = 0;
    this.scene.getMeshByName("relief1").visibility = 0;
    this.scene.getMeshByName("relief2").visibility = 0;
    this.scene.getMeshByName("energize1").visibility = 0;
    this.scene.getMeshByName("energize2").visibility = 0;
    this.scene.getMeshByName("ground1").visibility = 0;
    this.scene.getMeshByName("ground2").visibility = 0;

    //body fade in
    var Body = this.scene.getMeshByName("Body");
    var BodyMaterial = new PBRMaterial("BodyMaterial", this.scene);
    BodyMaterial.albedoColor = new Color3(0, 0, 0);
    BodyMaterial.metallic = 1;
    BodyMaterial.roughness = 1;
    Body.material = BodyMaterial;
    BodyMaterial.alpha = 1;

    // Body.actionManager = new ActionManager(this.scene);
    // Body.actionManager.registerAction(
    //   new ExecuteCodeAction(ActionManager.OnPickDownTrigger, function () {
    //     this.scene.hoverCursor = "grabbing";
    //   })
    // );

    this.scene.executeWhenReady(async function () {
      that.props.loadinglogostop();
      setTimeout(() => {
        TweenMax.to(that.cameraStart, 2.2, {
          radius: 42.61,
        });
        TweenMax.to(that.cameraStart, 3.7, {
          beta: 1.2598,
        });
        setTimeout(() => {
          TweenMax.to(that.cameraStart, 3, {
            radius: 39.11,
          });
          TweenMax.to(logoMaterial, 2, {
            alpha: 1,
          });
        }, 2200);
        // TweenMax.to(BodyMaterial, 3, {
        //   alpha: 1,
        // });
      }, 500);

      setTimeout(() => {
        that.cameraStart.attachControl(that.stage, true);
        TweenMax.to(that.scene.getMeshByName("mind1"), 1, {
          visibility: 1,
        });
        TweenMax.to(that.scene.getMeshByName("mind2"), 1, {
          visibility: 1,
        });
        TweenMax.to(that.scene.getMeshByName("truth"), 1, {
          visibility: 1,
        });
        TweenMax.to(that.scene.getMeshByName("heart"), 1, {
          visibility: 1,
        });
        TweenMax.to(that.scene.getMeshByName("center"), 1, {
          visibility: 1,
        });
        TweenMax.to(that.scene.getMeshByName("relief1"), 1, {
          visibility: 1,
        });
        TweenMax.to(that.scene.getMeshByName("relief2"), 1, {
          visibility: 1,
        });
        TweenMax.to(that.scene.getMeshByName("energize1"), 1, {
          visibility: 1,
        });
        TweenMax.to(that.scene.getMeshByName("energize2"), 1, {
          visibility: 1,
        });
        TweenMax.to(that.scene.getMeshByName("ground1"), 1, {
          visibility: 1,
        });
        TweenMax.to(that.scene.getMeshByName("ground2"), 1, {
          visibility: 1,
        });

        that.scene.getMeshByName("mind1big").setEnabled(true);
        that.scene.getMeshByName("mind2big").setEnabled(true);
        that.scene.getMeshByName("truthbig").setEnabled(true);
        that.scene.getMeshByName("heartbig").setEnabled(true);
        that.scene.getMeshByName("centerbig").setEnabled(true);
        that.scene.getMeshByName("relief1big").setEnabled(true);
        that.scene.getMeshByName("relief2big").setEnabled(true);
        that.scene.getMeshByName("energize1big").setEnabled(true);
        that.scene.getMeshByName("energize2big").setEnabled(true);
        that.scene.getMeshByName("ground1big").setEnabled(true);
        that.scene.getMeshByName("ground2big").setEnabled(true);
        setTimeout(() => {
          that.props.startdisplayfunction();
        }, 2200);
        setTimeout(() => {
          that.props.switchMenuButtonsDisplay();
        }, 3250);
        document.getElementsByClassName(
          "INCORPbottomend"
        )[0].childNodes[0].style.animationPlayState = "paused";
        setTimeout(() => {
          if (document.getElementsByClassName("INCORPbottomend").style != null){
            document.getElementsByClassName(
              "INCORPbottomend"
            ).style.display = "none";
          }
        }, 500);
      }, 350);
    });

    // Body.material.alpha = 0;

    setTimeout(() => {
      // this.cameraStart.attachControl(this.stage, true);
      // this.scene.getMeshByName("mind1").visibility = 1;
      // this.scene.getMeshByName("mind2").visibility = 1;
      // this.scene.getMeshByName("truth").visibility = 1;
      // this.scene.getMeshByName("heart").visibility = 1;
      // this.scene.getMeshByName("center").visibility = 1;
      // this.scene.getMeshByName("relief1").visibility = 1;
      // this.scene.getMeshByName("relief2").visibility = 1;
      // this.scene.getMeshByName("energize1").visibility = 1;
      // this.scene.getMeshByName("energize2").visibility = 1;
      // this.scene.getMeshByName("ground1").visibility = 1;
      // this.scene.getMeshByName("ground2").visibility = 1;
      // this.props.startdisplayfunction();
      document.getElementsByClassName(
        "INCORPbottomend"
      )[0].childNodes[0].style.animationPlayState = "paused";
      setTimeout(() => {
        document.getElementsByClassName(
          "INCORPbottomend"
        )[0].childNodes[0].style.animationPlayState = "paused";
      }, 1000);
    }, 6000);

    var animationcamera = this.scene.getCameraById("VRayCam001");
    animationcamera.fov = 0.5;

    Body1.doNotSyncBoundingInfo = true;
    this.scene.getMeshById("Body").doNotSyncBoundingInfo = true;
    var animation = this.scene.getAnimationGroupByName("All Animations");
    animation.stop();
    this.scene.getMaterialById("__GLTFLoader._default").albedoColor =
      new Color3.FromInts(0, 0, 0).toLinearSpace();
    await SceneLoader.AppendAsync("", "./I_V11_comp.glb", this.scene);

    var box1 = MeshBuilder.CreateBox("box", { size: 0.4 });
    box1.position.y = 1.666;
    box1.position.x = 0.123;
    box1.position.z = 0.529;
    box1.rotation.y = Tools.ToRadians(-9.2372);
    box1.isPickable = true;
    box1.visibility = 0;
    // box1
    box1.actionManager = new ActionManager(that.scene);
    box1.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
        // console.log("boxpressetd");
        //  that.props.ProductDisplayFunction()
        if (that.state.scenenow === "secondphase") {
          bottlesetbeside("1");
          counter = 1;
        }
        TweenMax.to(BodyMaterial, 1, {
          alpha: 0.25,
        });
        // document.getElementsByClassName("App")[0].style.cursor = "default";
        document.getElementsByClassName("INCORPbottomend")[0].style.cursor = "grab";
        setTimeout(() => {
          that.props.pictogram("show");
        }, 2500);
      })
    );

    let product1 = this.scene.getMeshByName("__root__");
    // box1.setParent(product1)
    product1.name = "product1";

    var body1 = this.scene.getMeshByName("body_B");
    body1.name = "body1";
    var cover1 = this.scene.getMeshByName("lid_B");
    cover1.name = "cover1";
    var liquid1 = this.scene.getMeshByName("liquid_B");
    liquid1.name = "liquid1";
    var helix1 = this.scene.getMeshByName("helix_B");
    helix1.name = "helix1";

    body1.visibility = 0;
    cover1.visibility = 0;
    helix1.visibility = 0;
    liquid1.visibility = 0;

    let HDR1 = new CubeTexture.CreateFromPrefilteredData(
      "./hdr/Perfume_HDRI.env",
      this.scene
    );
    this.scene.environmentTexture = HDR1;
    this.scene.environmentIntensity = 1.47;
    Matrix.RotationXToRef(
      Tools.ToRadians(90),
      HDR1.getReflectionTextureMatrix()
    );

    let GlassHDR = new CubeTexture.CreateFromPrefilteredData(
      "./hdr/Perfume_HDR.env",
      this.scene
    );
    Matrix.RotationXToRef(
      Tools.ToRadians(90),
      GlassHDR.getReflectionTextureMatrix()
    );

    // kira notes product 1
    // body volume ior 1.03
    // body alpha 0.9200
    // body transparency mode alpha test
    // body scatterig enabled
    // env intensity 1.57
    // liquid tint color #CEA76B
    // liquid clearcoat IOR 1.8000
    // liquid clearcoat at distance 4

    // kira notes product 2
    // bodyvolume ior 1.02
    // body alpha 0.9200
    // body transparency mode alpha test
    // body scatterig enabled
    // env intensity 1.57
    // liquid tint color #FAECB0
    // liquid clearcoat IOR 1.8000
    // liquid clearcoat at distance 2.8000

    // kira notes product 3
    // bodyvolume ior 1.04
    // body alpha 0.9200
    // body transparency mode alpha test
    // body scatterig enabled
    // env intensity 1.57
    // liquid tint color #EEDBBC
    // liquid clearcoat IOR 1.8000
    // liquid clearcoat at distance 0.6000

    product1.name = "product1";
    product1.scaling.x = 0.45;
    product1.scaling.y = 0.45;
    product1.scaling.z = 0.45;
    product1.rotation = new Vector3(
      Tools.ToRadians(-90),
      Tools.ToRadians(350.8),
      Tools.ToRadians(0)
    );
    // product1.position=new Vector3(0.606,1.536,0.864);
    product1.position = new Vector3(0.07, 1.65, 0.55);

    //annotations
    var that = this;
    var whitepbr = new PBRMaterial("annotation", this.scene);
    whitepbr.albedoColor = new Color3(1, 1, 1);

    this.scene.getMaterialByName("Body").name = "productBody1";
    let productBody = this.scene.getMaterialByName("productBody1");
    // productBody.albedoColor = new Color3.FromInts(255, 255, 255);
    // productBody.metallic = 0.85;
    // productBody.emissiveColor = new Color3.FromInts(255, 255, 255).toLinearSpace();
    // productBody.clearCoat.isEnabled = true;
    // productBody.roughness = 0.26;
    // productBody.clearCoat.roughness = 0;
    // productBody.clearCoat.intensity = 1;
    // productBody.clearCoat.indexOfRefraction = 3;
    // productBody.backFaceCulling = false;
    // productBody.metallicF0Factor = 1;
    // productBody.iridescence.isEnabled = false;
    // productBody.alpha = 0.98;
    // productBody.transparencyMode = 2;
    // productBody.alphaMode = 4;
    // productBody.emissiveTexture = new Texture("Hebox_Refraction_V2.jpg", this.scene, false, false);
    // productBody.subSurface.isTintEnabled = true;
    // productBody.subSurface.tintColor = new Color3.FromInts(255, 255, 255).toLinearSpace();
    productBody.albedoColor = new Color3.FromInts(255, 255, 255);
    productBody.metallic = 0.71;
    productBody.emissiveColor = new Color3.FromInts(83, 83, 83).toLinearSpace();
    productBody.clearCoat.isEnabled = true;
    productBody.roughness = 0.25;
    productBody.clearCoat.roughness = 0;
    productBody.clearCoat.intensity = 1;
    productBody.clearCoat.indexOfRefraction = 3;
    productBody.backFaceCulling = true;
    productBody.metallicF0Factor = 1;
    productBody.iridescence.isEnabled = false;
    productBody.alpha = 0.92;
    productBody.transparencyMode = 1;
    productBody.alphaMode = 0;
    productBody.emissiveTexture = new Texture(
      "Hebox_Refraction_V2.jpg",
      this.scene,
      false,
      false
    );
    productBody.metallicTexture = new Texture(
      "Hebox_Refraction_V2.jpg",
      this.scene,
      false,
      false
    );
    productBody.subSurface.isTintEnabled = true;
    productBody.subSurface.tintColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();
    productBody.subSurface.isRefractionEnabled = true;
    productBody.subSurface.refractionIntensity = 0.77;
    productBody.subSurface.indexOfRefraction = 1.04;
    productBody.subSurface.isScatteringEnabled = true;
    productBody.reflectionTexture = GlassHDR;

    cover1.material.albedoColor = new Color3.FromInts(
      154,
      154,
      154
    ).toLinearSpace();
    cover1.material.albedoTexture = new Texture(
      "Hebox_Cover_Albedo.jpg",
      this.scene,
      false,
      false
    );
    cover1.material.emissiveColor = new Color3.FromInts(0, 0, 0);
    cover1.material.metallic = 1;
    cover1.material.roughness = 0;
    cover1.material.metallicF0Factor = 1;
    cover1.material.backFaceCulling = false;
    cover1.material.iridescence.isEnabled = false;
    // cover1.material.iridescence.intensity = 1;
    // cover1.material.iridescence.indexOfRefraction = 1.3;
    // cover1.material.iridescence.maximumThickness = 350;
    // cover1.material.enableSpecularAntiAliasing = true;
    cover1.material.metallicF0Factor = 1;
    // var bumpTexture1 = new Texture("Cover_Normal.jpg", this.scene, false, false);
    // bumpTexture1.level = 0.2;
    // cover1.material.bumpTexture = bumpTexture1;
    // cover1.material.bumpTexture.intensity=0.2
    // productBody.enableSpecularAntiAliasing = true;

    var liq1 = liquid1.material;
    var heli1 = helix1.material;
    heli1.albedoColor = new Color3.FromInts(220, 220, 220).toLinearSpace();
    heli1.emissiveColor = new Color3.FromInts(0, 0, 0).toLinearSpace();
    heli1.alpha = 0.35;
    heli1.metallic = 0.64;
    heli1.roughness = 0.8;
    heli1.metallicF0Factor = 1;
    heli1.backFaceCulling = false;
    heli1.transparencyMode = 2;
    heli1.alphaMode = 2;
    heli1.enableSpecularAntiAliasing = true;

    liq1.albedoColor = new Color3.FromInts(255, 255, 255).toLinearSpace();

    liq1.clearCoat.isEnabled = true;
    liq1.roughness = 0.22;
    liq1.clearCoat.roughness = 0;
    liq1.clearCoat.indexOfRefraction = 1.5;
    liq1.clearCoat.isTintEnabled = true;
    liq1.clearCoat.intensity = 1;
    liq1.clearCoat.tintColor = new Color3.FromInts(
      206,
      167,
      107
    ).toLinearSpace();
    // liq1.clearCoat.tintColor = new Color3.FromInts(157, 92, 19).toLinearSpace();
    liq1.clearCoat.tintThickness = 3;
    liq1.clearCoat.tintColorAtDistance = 4;
    liq1.clearCoat.indexOfRefraction = 1.8;
    liq1.backFaceCulling = true;
    liq1.metallicF0Factor = 1;

    liq1.subSurface.isTintEnabled = true;
    liq1.subSurface.tintColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();
    liq1.metallic = 0;

    var refractionTexture = new RefractionTexture("th", 1024, this.scene);
    refractionTexture.renderList.push(liquid1);
    refractionTexture.renderList.push(helix1);
    refractionTexture.renderList.push(Body);
    refractionTexture.renderList.push(logo);

    refractionTexture.refractionPlane = new Plane(0, 0, 0, 0);

    body1.material.diffuseColor = new Color3(1, 1, 1);
    body1.material.refractionTexture = refractionTexture;

    liquid1.material.albedoColor = new Color3(
      0.945,
      0.757,
      0.502
    ).toLinearSpace();

    var refractionTexture2 = new RefractionTexture("th3", 1024, this.scene);
    refractionTexture2.renderList.push(cover1);

    refractionTexture2.refractionPlane = new Plane(0, 0, 0, 0);

    liquid1.material.diffuseColor = new Color3(1, 1, 1);
    liquid1.material.refractionTexture = refractionTexture2;

    body1.material.indexOfRefraction = 1.04;
    refractionTexture.depth = 1.2;

    liquid1.material.indexOfRefraction = 1.03;
    refractionTexture2.depth = 1.2;

    var that = this;
    for (var i = 0; i < product1._children.length; i++) {
      if (
        product1._children[i].id != "Annotation_01" ||
        product1._children[i].id != "Annotation_02" ||
        product1._children[i].id != "Disk001" ||
        product1._children[i].id != "Disk"
      ) {
        product1._children[i].isPickable = true;
        product1._children[i].actionManager = new ActionManager(that.scene);
        product1._children[i].actionManager.registerAction(
          new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
            if (that.state.scenenow === "thirdphase") {
              if (that.state.toolCurrentColor == "Opal"){
                that.customiseOpalInBackground()
              }
              // console.log(mesh.source, "firstModel");
              that.camera.detachControl(that.stage, true);
              // that.props.ProductDisplayFunction();
              // turnOnAnnotation(1);
              // that.props.ArrowsDisplayFunction("off");
              productsTurn("bottle");
              that.props.ProductDisplayBottleSwitch("Body", "close");
              that.props.ProductDisplayBottleSwitch("Cover", "close");
              that.props.ProductDisplayBottleSwitch("SmallBottle", "close");
              that.props.ProductDisplayBottleSwitch("Tool", "close");
            }
          })
        );
        product1._children[i].actionManager.registerAction(
          new ExecuteCodeAction(ActionManager.OnPointerOverTrigger, function () {
            if(that.state.rotating == false && that.props.currentModel == "body1"){
              TweenMax.to(rotatePlaneMaterial, 0.5, {
                alpha: 1
              });
            }
          })
        );
        product1._children[i].actionManager.registerAction(
          new ExecuteCodeAction(ActionManager.OnPointerOutTrigger, function () {
            TweenMax.to(rotatePlaneMaterial, 0.5, {
              alpha: 0
            });
          })
        );
      }
    }

    var disk1, disk2;
    disk1 = this.scene.getMeshByName("Disk001");
    disk2 = this.scene.getMeshByName("Disk");
    disk1.name = "product1disk1";
    disk2.name = "product1disk2";
    const torusforbody1 = this.scene.getMeshByName("Annotation_01");
    torusforbody1.material.albedoColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();
    torusforbody1.material.emissiveColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();
    torusforbody1.name = torusforbody1.name + "_1";
    torusforbody1.actionManager = new ActionManager(that.scene);
    torusforbody1.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
        // console.log(mesh.source)
        that.props.ProductDisplayBottleSwitch("Cover", "open");
        productsTurn("bottle");
      })
    );
    const torusforcover1 = this.scene.getMeshByName("Annotation_02");
    torusforcover1.name = torusforcover1.name + "_1";
    torusforcover1.material.albedoColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();
    torusforcover1.material.emissiveColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();

    torusforcover1.actionManager = new ActionManager(that.scene);
    torusforcover1.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
        that.props.ProductDisplayBottleSwitch("Body", "open");
        productsTurn("bottle");
      })
    );

    disk1.actionManager = new ActionManager(that.scene);
    disk1.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
        // console.log(mesh.source)
        that.props.ProductDisplayBottleSwitch("Body", "open");
        productsTurn("bottle");
      })
    );

    disk2.actionManager = new ActionManager(that.scene);
    disk2.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
        that.props.ProductDisplayBottleSwitch("Cover", "open");
        productsTurn("bottle");
      })
    );
    disk1.visibility= 0.1;
    disk2.visibility = 0.1;

    // const torusforsmallbottle1 = Mesh.CreatePlane("torusforsmallbottle1", 0.03, this.scene);
    // torusforsmallbottle1.actionManager = new ActionManager(that.scene);
    // torusforsmallbottle1.actionManager.registerAction(
    //   new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
    //     that.props.ProductDisplayBottleSwitch("SmallBottle", "open");
    //   })
    // );
    // const torusfortool1 = Mesh.CreatePlane("torusfortool1", 0.03, this.scene);
    // torusfortool1.actionManager = new ActionManager(that.scene);
    // torusfortool1.actionManager.registerAction(
    //   new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
    //     that.props.ProductDisplayBottleSwitch("Tool", "open");
    //   })
    // );
    //  0 0.028 -0.027
    disk1.rotationQuaternion = null;
    disk2.rotationQuaternion = null;
    torusforbody1.rotationQuaternion = null;
    torusforcover1.rotationQuaternion = null;
    torusforcover1.rotation.x = Math.PI / 2;
    torusforbody1.rotation.x = Math.PI / 2;
    disk2.rotation.x = Math.PI / 2;
    disk1.rotation.x = Math.PI / 2;
    torusforbody1.position = new Vector3(-0.024, 0.328, 0.225);
    torusforcover1.position = new Vector3(-0.074, -0.19, 0.23);
    disk1.position = new Vector3(-0.074, -0.19, 0.23);
    disk2.position = new Vector3(-0.024, 0.328, 0.225);
    // torusforsmallbottle1.position = new Vector3(-0.103, 1.57, 0.11);
    // torusfortool1.position = new Vector3(0.377, 1.494, 0.211);
    // var mat = new StandardMaterial("billboard", this.scene);
    // mat.emissiveColor = new Color3(1, 1, 1);
    // mat.diffuseTexture = new Texture("oval.png", this.scene);
    // mat.diffuseTexture.hasAlpha = true;
    // mat.backFaceCulling = false;
    // torusforbody1.material = mat;
    // torusforcover1.material = mat;
    // torusforsmallbottle1.material = mat;
    // torusfortool1.material = mat;
    // torusforbody1.billboardMode = AbstractMesh.BILLBOARDMODE_ALL;
    // torusforcover1.billboardMode = AbstractMesh.BILLBOARDMODE_ALL;
    // torusforsmallbottle1.billboardMode = AbstractMesh.BILLBOARDMODE_ALL;
    // torusfortool1.billboardMode = AbstractMesh.BILLBOARDMODE_ALL;
    torusforbody1.setEnabled(false);
    torusforcover1.setEnabled(false);
    disk1.setEnabled(false);
    disk2.setEnabled(false);
    // torusforsmallbottle1.setEnabled(false);
    // torusfortool1.setEnabled(false);

    // const  makeBoddyParent=(mesh)=>{
    //   // console.log(this.scene.meshes)
    //   this.scene.getMeshById("Body").setParent(this.camera)
    //   for(var i=0;i<this.scene.meshes.length;i++){
    //     if(this.scene.meshes[i]!=mesh  && this.scene.meshes[i]!=product1 && this.scene.meshes[i].name!="KiraLogo001"){
    //       this.scene.meshes[i].setParent(this.camera)

    //     }

    //   }
    // }

    await SceneLoader.AppendAsync("", "./5ml_Bottle_Chain_V10_comp.glb", this.scene);
    let product1small = this.scene.getMeshByName("__root__");
    // box1.setParent(product1)
    product1small.name = "product1small";
    product1small.setEnabled(false);
    var body1small = this.scene.getMeshByName("body_B");
    body1small.name = "body1small";
    var cover1small = this.scene.getMeshByName("lid_B001");
    cover1small.name = "cover1small";
    var ring = this.scene.getMaterialByName("Ring");
    var chain = this.scene.getMaterialByName("Chains");
    var liquid1small = this.scene.getMeshByName("liquid_B");
    liquid1small.name = "liquid1small";

    var productsmallannotation1 = this.scene.getMeshByName("Annotation_01");
    var productsmallannotation2 = this.scene.getMeshByName("Annotation_002");
    productsmallannotation1.material.albedoColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();
    productsmallannotation2.material.albedoColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();
    productsmallannotation2.material.emissiveColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();
    productsmallannotation1.material.emissiveColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();

    var productsmalldisk1 = this.scene.getMeshByName("Disk");
    var productsmalldisk2 = this.scene.getMeshByName("Disk001");
    productsmalldisk1.name = "productsmalldisk1";
    productsmalldisk2.name = "productsmalldisk2";
    productsmallannotation1.name = "productsmallannotation1";
    productsmallannotation2.name = "productsmallannotation2";
    productsmallannotation1.setEnabled(false);
    productsmallannotation2.setEnabled(false);
    productsmalldisk1.setEnabled(false);
    productsmalldisk2.setEnabled(false);
    // productsmallannotation
    productsmalldisk1.visibility = 0.1;
    productsmalldisk2.visibility = 0.1;
    productsmalldisk1.position = new Vector3(-0.016, 0.046, 0);
    productsmallannotation1.position = new Vector3(-0.015, 0.045, 0);
    for (var i = 0; i < product1small._children.length; i++) {
      if (
        product1small._children[i].id != "Annotation_01" ||
        product1small._children[i].id != "Annotation_002" ||
        product1small._children[i].id != "Disk001" ||
        product1small._children[i].id != "Disk"
      ) {
        product1small._children[i].isPickable = true;
        product1small._children[i].actionManager = new ActionManager(
          that.scene
        );
        product1small._children[i].actionManager.registerAction(
          new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
            // that.props.ProductDisplaySwitch("close");
            if (that.state.toolCurrentColor == "Opal"){
              that.customiseOpalInBackground()
            }
            productsTurn("smallBottle");
            that.props.ProductDisplayBottleSwitch("Body", "close");
            that.props.ProductDisplayBottleSwitch("Cover", "close");
            that.props.ProductDisplayBottleSwitch("SmallBottle", "close");
            that.props.ProductDisplayBottleSwitch("Tool", "close");
          })
        );
        product1small._children[i].actionManager.registerAction(
          new ExecuteCodeAction(ActionManager.OnPointerOverTrigger, function () {
            if(that.state.rotating == false && that.props.currentModel == "bodySmall"){
              TweenMax.to(rotatePlaneMaterial, 0.5, {
                alpha: 1
              });
            }
          })
        );
        product1small._children[i].actionManager.registerAction(
          new ExecuteCodeAction(ActionManager.OnPointerOutTrigger, function () {
            TweenMax.to(rotatePlaneMaterial, 0.5, {
              alpha: 0
            });
          })
        );
      }
    }
    productsmallannotation1.actionManager = new ActionManager(that.scene);
    productsmallannotation1.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
        that.props.ProductDisplayBottleSwitch("SmallBottle", "open");
        productsTurn("smallBottle");
      })
    );
    productsmalldisk1.actionManager = new ActionManager(that.scene);
    productsmalldisk1.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
        // console.log("actionnnnn")
        that.props.ProductDisplayBottleSwitch("SmallBottle", "open");
        productsTurn("smallBottle");
      })
    );

    productsmallannotation2.actionManager = new ActionManager(that.scene);
    productsmallannotation2.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
        that.props.ProductDisplayBottleSwitch("SmallBottleBody", "open");
        productsTurn("smallBottle");
      })
    );
    productsmalldisk2.actionManager = new ActionManager(that.scene);
    productsmalldisk2.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
        // console.log("actionnnnn")
        that.props.ProductDisplayBottleSwitch("SmallBottleBody", "open");
        productsTurn("smallBottle");
      })
    );

    // body1small.visibility = 0;
    // cover1small.visibility = 0;
    // liquid1small.visibility = 0;
    product1small.scaling.x = 2;
    product1small.scaling.y = 2;
    product1small.scaling.z = 2;
    product1small.rotation = new Vector3(
      Tools.ToRadians(175),
      Tools.ToRadians(260),
      Tools.ToRadians(90)
    );
    product1small.position = new Vector3(0.104, 1.486, 0.285);

    this.scene.getMaterialByName("Body").name = "productBody1small";
    let productBody1small = this.scene.getMaterialByName("productBody1small");
    // productBody1small.albedoColor = new Color3.FromInts(255, 255, 255);
    // productBody1small.emissiveTexture = new Texture("5mm_He_Refraction_V2.jpg", this.scene, false, false);
    // productBody1small.emissiveColor = new Color3.FromInts(255, 255, 255).toLinearSpace();
    // productBody1small.alpha = 0.95;
    // productBody1small.transparencyMode = 2;
    // productBody1small.alphaMode = 4;
    // productBody1small.subSurface.isTintEnabled = true;
    // productBody1small.subSurface.tintColor = new Color3.FromInts(255, 255, 255).toLinearSpace();
    // productBody1small.metallic = 0;
    // productBody1small.roughness = 0;
    // productBody1small.iridescence.isEnabled = false;
    // productBody1small.clearCoat.isEnabled = true;
    // productBody1small.clearCoat.intensity = 1;
    // productBody1small.clearCoat.roughness = 0;
    // productBody1small.clearCoat.indexOfRefraction = 3;
    // productBody1small.backFaceCulling = false;
    // productBody1small.metallicF0Factor = 1;
    productBody1small.albedoColor = new Color3.FromInts(255, 255, 255);
    productBody1small.metallic = 0.71;
    productBody1small.emissiveColor = new Color3.FromInts(
      83,
      83,
      83
    ).toLinearSpace();
    productBody1small.clearCoat.isEnabled = true;
    productBody1small.roughness = 0.25;
    productBody1small.clearCoat.roughness = 0;
    productBody1small.clearCoat.intensity = 1;
    productBody1small.clearCoat.indexOfRefraction = 3;
    productBody1small.backFaceCulling = true;
    productBody1small.metallicF0Factor = 1;
    productBody1small.iridescence.isEnabled = false;
    productBody1small.alpha = 0.92;
    productBody1small.transparencyMode = 1;
    productBody1small.alphaMode = 0;
    productBody1small.emissiveTexture = new Texture(
      "5mm_He_Refraction_V2.jpg",
      this.scene,
      false,
      false
    );
    productBody1small.metallicTexture = new Texture(
      "5mm_He_Refraction_V2.jpg",
      this.scene,
      false,
      false
    );
    productBody1small.subSurface.isTintEnabled = true;
    productBody1small.subSurface.tintColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();
    productBody1small.subSurface.isRefractionEnabled = true;
    productBody1small.subSurface.refractionIntensity = 0.77;
    productBody1small.subSurface.indexOfRefraction = 1.008;
    productBody1small.subSurface.isScatteringEnabled = true;
    productBody1small.reflectionTexture = GlassHDR;

    cover1small.material.albedoColor = new Color3.FromInts(
      35,
      35,
      35
    ).toLinearSpace();
    cover1small.material.albedoTexture = new Texture(
      "5mm_He_Cover.jpg",
      this.scene,
      false,
      false
    );
    cover1small.material.emissiveColor = new Color3.FromInts(0, 0, 0);
    cover1small.material.metallic = 1;
    cover1small.material.roughness = 0;
    cover1small.material.metallicF0Factor = 1;
    cover1small.material.backFaceCulling = false;
    cover1small.material.iridescence.isEnabled = false;
    // cover1small.material.iridescence.intensity = 1;
    // cover1small.material.iridescence.indexOfRefraction = 1.3;
    // cover1small.material.iridescence.maximumThickness = 350;
    cover1small.material.metallicF0Factor = 1;
    ring.albedoColor = new Color3.FromInts(35, 35, 35).toLinearSpace();
    ring.albedoTexture = new Texture(
      "5mm_He_Cover.jpg",
      this.scene,
      false,
      false
    );
    ring.emissiveColor = new Color3.FromInts(0, 0, 0);
    ring.metallic = 1;
    ring.roughness = 0;
    ring.metallicF0Factor = 1;
    ring.backFaceCulling = false;
    ring.iridescence.isEnabled = false;
    ring.metallicF0Factor = 1;
    chain.albedoColor = new Color3.FromInts(35, 35, 35).toLinearSpace();
    // chain.albedoTexture = new Texture("5mm_He_Cover.jpg", this.scene, false, false);
    chain.emissiveColor = new Color3.FromInts(0, 0, 0);
    chain.metallic = 1;
    chain.roughness = 0;
    chain.metallicF0Factor = 1;
    chain.backFaceCulling = false;
    chain.iridescence.isEnabled = false;
    chain.metallicF0Factor = 1;
    chain.transparencyMode = 3;

    liquid1small.material.albedoColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();
    liquid1small.material.clearCoat.isEnabled = true;
    liquid1small.material.roughness = 0.22;
    liquid1small.material.clearCoat.roughness = 0;
    liquid1small.material.clearCoat.indexOfRefraction = 1.8;
    liquid1small.material.clearCoat.isTintEnabled = true;
    liquid1small.material.clearCoat.intensity = 1;
    liquid1small.material.clearCoat.tintColor = new Color3.FromInts(
      206,
      167,
      107
    ).toLinearSpace();
    liquid1small.material.clearCoat.tintThickness = 3;
    liquid1small.material.clearCoat.tintColorAtDistance = 4;
    liquid1small.material.backFaceCulling = true;
    liquid1small.material.metallicF0Factor = 1;
    liquid1small.material.subSurface.isTintEnabled = true;
    liquid1small.material.subSurface.tintColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();
    liquid1small.material.metallic = 0;

    var refractionTexturesmall = new RefractionTexture(
      "thsmall",
      1024,
      this.scene
    );
    refractionTexturesmall.renderList.push(liquid1small);
    refractionTexturesmall.renderList.push(Body);
    refractionTexturesmall.renderList.push(logo);
    refractionTexturesmall.refractionPlane = new Plane(0, 0, 0, 0);
    body1small.material.diffuseColor = new Color3(1, 1, 1);
    body1small.material.refractionTexture = refractionTexturesmall;
    liquid1small.material.albedoColor = new Color3(
      0.945,
      0.757,
      0.502
    ).toLinearSpace();
    body1small.material.indexOfRefraction = 1.01;
    refractionTexturesmall.depth = 1.2;

    var refractionTexture2small = new RefractionTexture(
      "th3",
      1024,
      this.scene
    );
    refractionTexture2small.renderList.push(cover1small);
    refractionTexture2small.refractionPlane = new Plane(0, 0, 0, 0);
    liquid1small.material.diffuseColor = new Color3(1, 1, 1);
    liquid1small.material.refractionTexture = refractionTexture2small;
    liquid1small.material.indexOfRefraction = 1.03;
    refractionTexture2small.depth = 1.2;

    await SceneLoader.AppendAsync("", "./Tool_V12_draco.glb", this.scene);
    let tool = this.scene.getMeshByName("__root__");
    // box1.setParent(product1)
    tool.name = "tool";
    tool.scaling.x = 3.5;
    tool.scaling.y = -3.5;
    tool.scaling.z = 3.5;
    tool.rotation = new Vector3(
      Tools.ToRadians(270),
      Tools.ToRadians(0),
      Tools.ToRadians(170)
    );
    tool.position = new Vector3(0.186, 1.486, 0.282);
    tool.setEnabled(false);
    var toolbody = this.scene.getMeshByName("Group1");
    toolbody.name = "toolbody";
    toolbody.material.albedoColor = new Color3.FromInts(255, 255, 255);
    toolbody.material.albedoTexture = new Texture(
      "Black_onyx.jpg",
      this.scene,
      false,
      false
    );
    toolbody.material.metallic = 0;
    toolbody.material.roughness = 0.25;
    toolbody.material.iridescence.isEnabled = true;
    toolbody.material.iridescence.intensity = 0.63;
    toolbody.material.iridescence.indexOfRefraction = 1.3;
    toolbody.material.iridescence.maximumThickness = 240;
    toolbody.material.clearCoat.isEnabled = false;
    toolbody.material.backFaceCulling = false;
    toolbody.material.metallicF0Factor = 1;

    const torusfortool1 = this.scene.getMeshByName("Annotation_01");
    torusfortool1.name = "Annotation_01_tool";
    torusfortool1.material.albedoColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();
    torusfortool1.material.emissiveColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();

    // this.scene.getMeshByName("Annotation_02").name="Annotation_02_tool"
    var diskfortool = this.scene.getMeshByName("Disk");
    diskfortool.name = "Disk_tool";
    // torusfortool1.name=torusfortool1.name
    torusfortool1.actionManager = new ActionManager(that.scene);
    torusfortool1.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
        that.props.ProductDisplayBottleSwitch("Tool", "open");
        productsTurn("tool");
      })
    );
    diskfortool.actionManager = new ActionManager(that.scene);
    diskfortool.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
        that.props.ProductDisplayBottleSwitch("Tool", "open");
        productsTurn("tool");
      })
    );
    diskfortool.position = new Vector3(0.002, 0.026, 0.00);
    torusfortool1.position = new Vector3(0.002, 0.026, 0.0);
    diskfortool.visibility = 0.1;
    torusfortool1.setEnabled(false);
    diskfortool.setEnabled(false);
    await SceneLoader.AppendAsync("", "./You_V11_comp.glb", this.scene);

    var box2 = MeshBuilder.CreateBox("box2", { size: 0.4 });
    box2.position.y = 1.666;
    box2.position.x = 0.522;
    box2.position.z = 0.809;
    box2.rotation.y = Tools.ToRadians(-9.2372);
    box2.isPickable = true;
    box2.visibility = 0;
    box2.actionManager = new ActionManager(that.scene);
    box2.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
        // console.log("boxpressetd");
        //  that.props.ProductDisplayFunction()
        if (that.state.scenenow === "secondphase") {
          bottlesetbeside("2");
          counter = 0;
          that.customiseOpalInBackground();
          // that.customiseObsidian();
          this.scene.hoverCursor = "pointer";
        }
        TweenMax.to(BodyMaterial, 1, {
          alpha: 0.25,
        });
        // document.getElementsByClassName("App")[0].style.cursor = "default";
        document.getElementsByClassName("INCORPbottomend")[0].style.cursor = "grab";
        setTimeout(() => {
          that.props.pictogram("show");
        }, 2500);
      })
    );
    let product2 = this.scene.getMeshByName("__root__");
    product2.name = "product2";
    // product2.setEnabled(false);
    var body2 = this.scene.getMeshByName("body_B");
    body2.name = "body2";
    var cover2 = this.scene.getMeshByName("lid_B");
    cover2.name = "cover2";
    var liquid2 = this.scene.getMeshByName("liquid_B");
    liquid2.name = "liquid2";
    var helix2 = this.scene.getMeshByName("helix_B");
    helix2.name = "helix2";

    var liq2 = liquid2.material;
    var heli2 = helix2.material;

    body2.visibility = 0;
    cover2.visibility = 0;
    helix2.visibility = 0;
    liquid2.visibility = 0;

    product2.name = "product2";
    product2.scaling.x = 0.45;
    product2.scaling.y = 0.45;
    product2.scaling.z = 0.45;
    product2.rotation = new Vector3(
      Tools.ToRadians(-90),
      Tools.ToRadians(350.8),
      Tools.ToRadians(0)
    );

    product2.position = new Vector3(0.485, 1.686, 0.78);

    var product2annotation1 = this.scene.getMeshByName("Annotation_01");
    var product2annotation2 = this.scene.getMeshByName("Annotation_02");

    product2annotation1.material.albedoColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();
    product2annotation2.material.albedoColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();
    product2annotation1.material.emissiveColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();
    product2annotation2.material.emissiveColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();

    var product2disk1 = this.scene.getMeshByName("Disk");
    var product2disk2 = this.scene.getMeshByName("Disk001");
    product2annotation1.name = "product2annotation1";
    product2annotation2.name = "product2annotation2";
    product2disk1.name = "product2disk1";
    product2disk2.name = "product2disk2";
    product2annotation1.setEnabled(false);
    product2annotation2.setEnabled(false);
    product2disk1.setEnabled(true);
    product2disk2.setEnabled(true);

    product2disk1.rotationQuaternion = null;
    product2disk2.rotationQuaternion = null;
    product2annotation1.rotationQuaternion = null;
    product2annotation2.rotationQuaternion = null;
    product2annotation2.rotation.x = Math.PI / 2;
    product2annotation1.rotation.x = Math.PI / 2;
    product2disk2.rotation.x = Math.PI / 2;
    product2disk1.rotation.x = Math.PI / 2;
    product2annotation1.position = new Vector3(-0.024, 0.328, 0.225);
    product2annotation2.position = new Vector3(-0.074, -0.19, 0.217);
    product2disk1.position = new Vector3(-0.074, -0.19, 0.217);
    product2disk2.position = new Vector3(-0.024, 0.328, 0.225);

    product2disk1.rotationQuaternion = null;
    product2disk2.rotationQuaternion = null;
    product2annotation1.rotationQuaternion = null;
    product2annotation2.rotationQuaternion = null;
    product2annotation2.rotation.x = Math.PI / 2;
    product2annotation1.rotation.x = Math.PI / 2;
    product2disk2.rotation.x = Math.PI / 2;
    product2disk1.rotation.x = Math.PI / 2;
    product2annotation1.position = new Vector3(-0.024, 0.328, 0.225);
    product2annotation2.position = new Vector3(-0.074, -0.19, 0.251);
    product2disk1.position = new Vector3(-0.074, -0.19, 0.250);
    product2disk2.position = new Vector3(-0.024, 0.328, 0.224);

    product2disk1.setEnabled(false);
    product2disk2.setEnabled(false);
    // product2annotation
    product2disk1.visibility = 0.1;
    product2disk2.visibility = 0.1;

    this.scene.getMaterialByName("Body").name = "productBody2";
    let productBody2 = this.scene.getMaterialByName("productBody2");
    // productBody2.albedoColor = new Color3.FromInts(255, 255, 255);
    // productBody2.metallic = 0.85;
    // productBody2.emissiveColor = new Color3.FromInts(255, 255, 255);
    // productBody2.clearCoat.isEnabled = true;
    // productBody2.roughness = 0.26;
    // productBody2.clearCoat.roughness = 0;
    // productBody2.clearCoat.indexOfRefraction = 3;
    // productBody2.backFaceCulling = false;
    // productBody2.metallicF0Factor = 1;
    // productBody2.iridescence.isEnabled = false;
    // productBody2.alpha = 0.98;
    // productBody2.transparencyMode = 2;
    // productBody2.alphaMode = 4;
    // productBody2.emissiveTexture = new Texture("Shebox_Refraction_V2.jpg", this.scene, false, false);
    // productBody2.subSurface.isTintEnabled = true;
    // productBody2.subSurface.tintColor = new Color3.FromInts(255, 255, 255).toLinearSpace();
    productBody2.albedoColor = new Color3.FromInts(255, 255, 255);
    productBody2.metallic = 0.71;
    productBody2.emissiveColor = new Color3.FromInts(
      83,
      83,
      83
    ).toLinearSpace();
    productBody2.clearCoat.isEnabled = true;
    productBody2.roughness = 0.25;
    productBody2.clearCoat.roughness = 0;
    productBody2.clearCoat.intensity = 1;
    productBody2.clearCoat.indexOfRefraction = 3;
    productBody2.backFaceCulling = true;
    productBody2.metallicF0Factor = 1;
    productBody2.iridescence.isEnabled = false;
    productBody2.alpha = 0.92;
    productBody2.transparencyMode = 1;
    productBody2.alphaMode = 0;
    productBody2.emissiveTexture = new Texture(
      "Shebox_Refraction_V2.jpg",
      this.scene,
      false,
      false
    );
    productBody2.metallicTexture = new Texture(
      "Shebox_Refraction_V2.jpg",
      this.scene,
      false,
      false
    );
    productBody2.subSurface.isTintEnabled = true;
    productBody2.subSurface.tintColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();
    productBody2.subSurface.isRefractionEnabled = true;
    productBody2.subSurface.refractionIntensity = 0.77;
    productBody2.subSurface.indexOfRefraction = 1.04;
    productBody2.subSurface.isScatteringEnabled = true;
    productBody2.reflectionTexture = GlassHDR;

    var CoverHDR = new CubeTexture.CreateFromPrefilteredData(
      "./hdr/Cover_HDR.env",
      this.scene
    );
    Matrix.RotationXToRef(
      Tools.ToRadians(90),
      CoverHDR.getReflectionTextureMatrix()
    );
    cover2.material.albedoColor = new Color3.FromInts(
      224,
      184,
      123
    ).toLinearSpace();
    // cover2.material.albedoTexture = new Texture(
    //   "Hebox_Cover_Albedo.jpg",
    //   this.scene,
    //   false,
    //   false
    // );
    cover2.material.emissiveColor = new Color3.FromInts(0, 0, 0);
    cover2.material.metallic = 1;
    cover2.material.roughness = 0;
    cover2.material.metallicF0Factor = 1;
    cover2.material.backFaceCulling = false;
    cover2.material.reflectionTexture = CoverHDR;

    // cover2.material.iridescence.isEnabled = true;
    // cover2.material.iridescence.intensity=1
    // cover2.material.iridescence.indexOfRefraction=1.3
    // cover2.material.iridescence.maximumThickness=350
    // cover2.material.enableSpecularAntiAliasing = true;
    cover2.material.metallicF0Factor = 0;
    // var bumpTexture1 = new Texture("Cover_Normal.jpg", this.scene, false, false);
    // bumpTexture1.level = 0.2;
    // cover2.material.bumpTexture = bumpTexture1;
    // cover2.enableSpecularAntiAliasing = true;

    heli2.albedoColor = new Color3.FromInts(220, 220, 220).toLinearSpace();
    heli2.emissiveColor = new Color3.FromInts(0, 0, 0).toLinearSpace();
    heli2.alpha = 0.35;
    heli2.metallic = 0.64;
    heli2.roughness = 0.8;
    heli2.metallicF0Factor = 1;
    heli2.backFaceCulling = false;
    heli2.transparencyMode = 2;
    heli2.alphaMode = 2;
    heli2.enableSpecularAntiAliasing = true;

    liq2.albedoColor = new Color3.FromInts(255, 255, 255).toLinearSpace();

    liq2.clearCoat.isEnabled = true;
    liq2.roughness = 0.22;
    liq2.clearCoat.roughness = 0;
    liq2.clearCoat.indexOfRefraction = 1.8;
    liq2.clearCoat.isTintEnabled = true;
    liq2.clearCoat.tintColor = new Color3.FromInts(
      234,
      231,
      218
    ).toLinearSpace();
    liq2.clearCoat.tintThickness = 3;
    liq2.clearCoat.tintColorAtDistance = 2.8;
    liq2.backFaceCulling = true;
    liq2.metallicF0Factor = 1;
    // liq2.enableSpecularAntiAliasing = true;

    liq2.subSurface.isTintEnabled = true;
    liq2.subSurface.tintColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();
    liq2.metallic = 0;

    var refractionTexture3 = new RefractionTexture("th5", 1024, this.scene);
    refractionTexture3.renderList.push(liquid2);
    refractionTexture3.renderList.push(helix2);
    refractionTexture3.renderList.push(Body);
    refractionTexture3.renderList.push(logo);

    refractionTexture3.refractionPlane = new Plane(0, 0, 0, 0);

    body2.material.diffuseColor = new Color3(1, 1, 1);
    body2.material.refractionTexture = refractionTexture3;

    liquid2.material.albedoColor = new Color3(
      0.945,
      0.757,
      0.502
    ).toLinearSpace();

    var refractionTexture4 = new RefractionTexture("th4", 1024, this.scene);
    refractionTexture4.renderList.push(cover2);

    refractionTexture4.refractionPlane = new Plane(0, 0, 0, 0);

    liquid2.material.diffuseColor = new Color3(1, 1, 1);
    liquid2.material.refractionTexture = refractionTexture4;

    body2.material.indexOfRefraction = 1.04;
    refractionTexture.depth = 1.2;

    liquid2.material.indexOfRefraction = 1.03;
    refractionTexture4.depth = 1.2;

    var that = this;

    for (var i = 0; i < product2._children.length; i++) {
      if (
        product2._children[i].id != "Annotation_01" ||
        product2._children[i].id != "Annotation_02" ||
        product2._children[i].id != "Disk001" ||
        product2._children[i].id != "Disk"
      ) {
        product2._children[i].isPickable = true;
        product2._children[i].actionManager = new ActionManager(that.scene);
        product2._children[i].actionManager.registerAction(
          new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
            if (that.state.scenenow === "thirdphase") {
              if (that.state.toolCurrentColor == "Opal"){
                that.customiseOpalInBackground()
              }
              // console.log(mesh.source, "firstModel");
              that.camera.detachControl(that.stage, true);
              // that.props.ProductDisplayFunction();
              // turnOnAnnotation(1);
              // that.props.ArrowsDisplayFunction("off");
              productsTurn("bottle");
              that.props.ProductDisplayBottleSwitch("Body", "close");
              that.props.ProductDisplayBottleSwitch("Cover", "close");
              that.props.ProductDisplayBottleSwitch("SmallBottle", "close");
              that.props.ProductDisplayBottleSwitch("Tool", "close");
            }
          })
        );
        product2._children[i].actionManager.registerAction(
          new ExecuteCodeAction(ActionManager.OnPointerOverTrigger, function () {
            if(that.state.rotating == false && that.props.currentModel == "body2"){
              TweenMax.to(rotatePlaneMaterial, 0.5, {
                alpha: 1
              });
            }
          })
        );
        product2._children[i].actionManager.registerAction(
          new ExecuteCodeAction(ActionManager.OnPointerOutTrigger, function () {
            TweenMax.to(rotatePlaneMaterial, 0.5, {
              alpha: 0
            });
          })
        );
      }
    }

    product2disk1.actionManager = new ActionManager(that.scene);
    product2disk1.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
        console.log(mesh.source);
        that.props.ProductDisplayBottleSwitch("Body", "open");
        productsTurn("bottle");
      })
    );

    product2disk2.actionManager = new ActionManager(that.scene);
    product2disk2.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
        console.log(mesh.source);

        that.props.ProductDisplayBottleSwitch("Cover", "open");
        productsTurn("bottle");
      })
    );
    await SceneLoader.AppendAsync("", "./We_V11_comp.glb", this.scene);
    var box3 = MeshBuilder.CreateBox("box3", { size: 0.4 });
    box3.position.y = 1.666;
    box3.position.x = -0.405;
    box3.position.z = 0.665;
    box3.rotation.y = Tools.ToRadians(-9.2372);
    box3.isPickable = true;
    box3.visibility = 0;
    box3.actionManager = new ActionManager(that.scene);
    box3.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
        // console.log("boxpressetd");
        //  that.props.ProductDisplayFunction()
        if (that.state.scenenow === "secondphase") {
          bottlesetbeside("3");
          counter = 2;
          that.customiseTiger();
        }
        TweenMax.to(BodyMaterial, 1, {
          alpha: 0.25,
        });
        // document.getElementsByClassName("App")[0].style.cursor = "default";
        document.getElementsByClassName("INCORPbottomend")[0].style.cursor = "grab";
        setTimeout(() => {
          that.props.pictogram("show");
        }, 2500);
      })
    );

    let product3 = this.scene.getMeshByName("__root__");
    product3.name = "product3";
    // product3.setEnabled(false);
    var body3 = this.scene.getMeshByName("body_B");
    body3.name = "body3";
    var cover3 = this.scene.getMeshByName("lid_B");
    cover3.name = "cover3";
    var liquid3 = this.scene.getMeshByName("liquid_B");
    liquid3.name = "liquid3";
    var helix3 = this.scene.getMeshByName("helix_B");
    helix3.name = "helix3";

    var liq3 = liquid3.material;
    var heli3 = helix3.material;

    body3.visibility = 0;
    cover3.visibility = 0;
    helix3.visibility = 0;
    liquid3.visibility = 0;

    product3.name = "product3";
    product3.scaling.x = 0.45;
    product3.scaling.y = 0.45;
    product3.scaling.z = 0.45;
    product3.rotation = new Vector3(
      Tools.ToRadians(-90),
      Tools.ToRadians(350.8),
      Tools.ToRadians(0)
    );

    product3.position = new Vector3(-0.388, 1.686, 0.635);

    var product3annotation1 = this.scene.getMeshByName("Annotation_01");
    var product3annotation2 = this.scene.getMeshByName("Annotation_02");
    product3annotation1.material.albedoColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();
    product3annotation2.material.albedoColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();
    product3annotation1.material.emissiveColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();
    product3annotation2.material.emissiveColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();

    var product3disk1 = this.scene.getMeshByName("Disk");
    var product3disk2 = this.scene.getMeshByName("Disk001");
    product3annotation1.name = "product3annotation1";
    product3annotation2.name = "product3annotation2";
    product3disk1.name = "product3disk1";
    product3disk2.name = "product3disk2";

    product3annotation1.setEnabled(false);
    product3annotation2.setEnabled(false);
    product3disk1.setEnabled(false);
    product3disk2.setEnabled(false);
    // product2annotation
    product3disk1.visibility = 0.1;
    product3disk2.visibility = 0.1;

    product3disk1.rotationQuaternion = null;
    product3disk2.rotationQuaternion = null;
    product3annotation1.rotationQuaternion = null;
    product3annotation2.rotationQuaternion = null;
    product3annotation2.rotation.x = Math.PI / 2;
    product3annotation1.rotation.x = Math.PI / 2;
    product3disk2.rotation.x = Math.PI / 2;
    product3disk1.rotation.x = Math.PI / 2;
    product3annotation1.position = new Vector3(-0.024, 0.328, 0.202);
    product3annotation2.position = new Vector3(-0.074, -0.19, 0.186);
    product3disk1.position = new Vector3(-0.074, -0.19, 0.185);
    product3disk2.position = new Vector3(-0.024, 0.328, 0.201);

    this.scene.getMaterialByName("Body").name = "productBody3";
    let productBody3 = this.scene.getMaterialByName("productBody3");
    // productBody3.albedoColor = new Color3.FromInts(255, 255, 255);
    // productBody3.metallic = 0.85;
    // productBody3.emissiveColor = new Color3.FromInts(255, 255, 255);
    // productBody3.clearCoat.isEnabled = true;
    // productBody3.roughness = 0.26;
    // productBody3.clearCoat.roughness = 0;
    // productBody3.clearCoat.indexOfRefraction = 3;
    // productBody3.backFaceCulling = false;
    // productBody3.metallicF0Factor = 1;
    // productBody3.iridescence.isEnabled = false;
    // productBody3.alpha = 0.98;
    // productBody3.transparencyMode = 2;
    // productBody3.alphaMode = 4;
    // productBody3.emissiveTexture = new Texture("Theybox_Refraction_V2.jpg", this.scene, false, false);
    // productBody3.subSurface.isTintEnabled = true;
    // productBody3.subSurface.tintColor = new Color3.FromInts(255, 255, 255).toLinearSpace();
    productBody3.albedoColor = new Color3.FromInts(255, 255, 255);
    productBody3.metallic = 0.71;
    productBody3.emissiveColor = new Color3.FromInts(
      83,
      83,
      83
    ).toLinearSpace();
    productBody3.clearCoat.isEnabled = true;
    productBody3.roughness = 0.25;
    productBody3.clearCoat.roughness = 0;
    productBody3.clearCoat.intensity = 1;
    productBody3.clearCoat.indexOfRefraction = 3;
    productBody3.backFaceCulling = true;
    productBody3.metallicF0Factor = 1;
    productBody3.iridescence.isEnabled = false;
    productBody3.alpha = 0.92;
    productBody3.transparencyMode = 1;
    productBody3.alphaMode = 0;
    productBody3.emissiveTexture = new Texture(
      "Theybox_Refraction_V2.jpg",
      this.scene,
      false,
      false
    );
    productBody3.metallicTexture = new Texture(
      "Theybox_Refraction_V2.jpg",
      this.scene,
      false,
      false
    );
    productBody3.subSurface.isTintEnabled = true;
    productBody3.subSurface.tintColor = new Color3.FromInts(
      255,
      255,
      255
    ).toLinearSpace();
    productBody3.subSurface.isRefractionEnabled = true;
    productBody3.subSurface.refractionIntensity = 0.77;
    productBody3.subSurface.indexOfRefraction = 1.04;
    productBody3.subSurface.isScatteringEnabled = true;
    productBody3.reflectionTexture = GlassHDR;

    cover3.material.albedoColor = new Color3.FromInts(255, 255, 255);
    cover3.material.albedoTexture = new Texture(
      "Cover_Albedo.jpg",
      this.scene,
      false,
      false
    );
    cover3.material.albedoColor = new Color3.FromInts(
      255,
      212,
      190
    ).toLinearSpace();
    cover3.material.albedoTexture = new Texture(
      "Theybox_Cover_Albedo.jpg",
      this.scene,
      false,
      false
    );
    cover3.material.emissiveColor = new Color3.FromInts(0, 0, 0);
    cover3.material.metallic = 1;
    cover3.material.roughness = 0;
    cover3.material.metallicF0Factor = 1;
    cover3.material.backFaceCulling = false;

    // cover3.material.enableSpecularAntiAliasing = true;
    // cover3.material.metallicF0Factor = 1;
    // var bumpTexture1 = new Texture("Cover_Normal.jpg", this.scene, false, false);
    // bumpTexture1.level = 0.2;
    // cover3.material.bumpTexture = bumpTexture1;
    // cover3.material.bumpTexture.intensity=0.2
    cover3.enableSpecularAntiAliasing = true;

    heli3.albedoColor = new Color3.FromInts(220, 220, 220).toLinearSpace();
    heli3.emissiveColor = new Color3.FromInts(0, 0, 0).toLinearSpace();
    heli3.alpha = 0.35;
    heli3.metallic = 0.64;
    heli3.roughness = 0.8;
    heli3.metallicF0Factor = 1;
    heli3.backFaceCulling = false;
    heli3.transparencyMode = 2;
    heli3.alphaMode = 2;
    heli3.enableSpecularAntiAliasing = true;

    liq3.albedoColor = new Color3.FromInts(255, 255, 255).toLinearSpace();
    liq3.albedoColor = new Color3(1, 1, 1).toLinearSpace();
    liq3.clearCoat.isEnabled = true;
    liq3.roughness = 0.22;
    liq3.clearCoat.roughness = 0;
    liq3.clearCoat.indexOfRefraction = 1.8;
    liq3.clearCoat.isTintEnabled = true;
    liq3.clearCoat.tintColor = new Color3.FromInts(
      238,
      219,
      188
    ).toLinearSpace();
    liq3.clearCoat.tintColorAtDistance = 0.65;
    liq3.backFaceCulling = true;
    liq3.metallicF0Factor = 1;
    liq3.enableSpecularAntiAliasing = true;

    var refractionTexture6 = new RefractionTexture("th7", 1024, this.scene);
    refractionTexture6.renderList.push(liquid3);
    refractionTexture6.renderList.push(helix3);
    refractionTexture6.renderList.push(Body);
    refractionTexture6.renderList.push(logo);

    refractionTexture6.refractionPlane = new Plane(0, 0, 0, 0);

    body3.material.diffuseColor = new Color3(1, 1, 1);
    body3.material.refractionTexture = refractionTexture6;

    liquid3.material.albedoColor = new Color3(
      0.945,
      0.757,
      0.502
    ).toLinearSpace();

    var refractionTexture7 = new RefractionTexture("th8", 1024, this.scene);
    refractionTexture7.renderList.push(cover3);

    refractionTexture7.refractionPlane = new Plane(0, 0, 0, 0);

    liquid3.material.diffuseColor = new Color3(1, 1, 1);
    liquid3.material.refractionTexture = refractionTexture7;

    body3.material.indexOfRefraction = 1.04;
    refractionTexture6.depth = 1.2;

    liquid3.material.indexOfRefraction = 1.03;
    refractionTexture7.depth = 1.2;
    box1.isPickable = false;
    box2.isPickable = false;
    box3.isPickable = false;
    var that = this;

    for (var i = 0; i < product3._children.length; i++) {
      if (
        product1._children[i].id != "Annotation_01" ||
        product1._children[i].id != "Annotation_02" ||
        product1._children[i].id != "Disk001" ||
        product1._children[i].id != "Disk"
      ) {
        product3._children[i].isPickable = true;
        product3._children[i].actionManager = new ActionManager(that.scene);
        product3._children[i].actionManager.registerAction(
          new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
            if (that.state.toolCurrentColor == "Opal"){
              that.customiseOpalInBackground()
            }
            // console.log(mesh.source, "firstModel");
            if (that.state.scenenow === "thirdphase") {
              // that.props.ProductDisplayFunction();
              // turnOnAnnotation(3);
              // that.props.ArrowsDisplayFunction("off");
              productsTurn("bottle");
              that.props.ProductDisplayBottleSwitch("Body", "close");
              that.props.ProductDisplayBottleSwitch("Cover", "close");
              that.props.ProductDisplayBottleSwitch("SmallBottle", "close");
              that.props.ProductDisplayBottleSwitch("Tool", "close");
            }
          })
        );
        product3._children[i].actionManager.registerAction(
          new ExecuteCodeAction(ActionManager.OnPointerOverTrigger, function () {
            if(that.state.rotating == false && that.props.currentModel == "body3"){
              TweenMax.to(rotatePlaneMaterial, 0.5, {
                alpha: 1
              });
            }
          })
        );
        product3._children[i].actionManager.registerAction(
          new ExecuteCodeAction(ActionManager.OnPointerOutTrigger, function () {
            TweenMax.to(rotatePlaneMaterial, 0.5, {
              alpha: 0
            });
          })
        );
      }
    }
    product3disk1.actionManager = new ActionManager(that.scene);
    product3disk1.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
        // console.log(mesh.source)
        that.props.ProductDisplayBottleSwitch("Body", "open");
        productsTurn("bottle");
      })
    );

    product3disk2.actionManager = new ActionManager(that.scene);
    product3disk2.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
        that.props.ProductDisplayBottleSwitch("Cover", "open");
        productsTurn("bottle");
      })
    );
    toolbody.isPickable = true;
    toolbody.actionManager = new ActionManager(that.scene);
    toolbody.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPickTrigger, function (mesh) {
        if (that.state.toolCurrentColor == "Opal"){
          that.customiseOpal()
        }
        // that.props.ProductDisplaySwitch("close");
        productsTurn("tool");
        that.props.ProductDisplayBottleSwitch("Body", "close");
        that.props.ProductDisplayBottleSwitch("Cover", "close");
        that.props.ProductDisplayBottleSwitch("SmallBottle", "close");
        that.props.ProductDisplayBottleSwitch("Tool", "close");
      })
    );
    toolbody.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPointerOverTrigger, function () {
        if(that.state.rotating == false && that.props.currentModel.slice(0,4) == "tool"){
          TweenMax.to(rotatePlaneMaterial, 0.5, {
            alpha: 1
          });
        }
      })
    );
    toolbody.actionManager.registerAction(
      new ExecuteCodeAction(ActionManager.OnPointerOutTrigger, function () {
        TweenMax.to(rotatePlaneMaterial, 0.5, {
          alpha: 0
        });
      })
    );

    let bottlesetbeside = (prod) => {
      this.camera.detachControl(this.stage, true);
      box1.isPickable = false;
      box2.isPickable = false;
      box3.isPickable = false;
      this.props.ArrowsDisplayFunction("on");
      if (prod == "1") {
        this.props.ProductDetailsChanger("I");
        this.setState({ currentModel: "body1" });
        this.props.updateCurrentModel("body1");
        TweenMax.to(product1.position, 0.75, {
          x: 0.127,
          y: 1.65,
          z: 0.21,
        });
        TweenMax.to(product2.position, 0.75, {
          x: this.state.positionLeft.x,
          y: this.state.positionLeft.y,
          z: this.state.positionLeft.z,
        });
        TweenMax.to(product3.position, 0.75, {
          x: this.state.positionRight.x,
          y: this.state.positionRight.y,
          z: this.state.positionRight.z,
        });
        //small bottle and tool
        productBody1small.emissiveTexture = new Texture(
          "5mm_He_Refraction_V2.jpg",
          this.scene,
          false,
          false
        );
        cover1small.material.albedoTexture = new Texture(
          "5mm_He_Cover.jpg",
          this.scene,
          false,
          false
        );
        cover1small.material.albedoColor = new Color3.FromInts(
          35,
          35,
          35
        ).toLinearSpace();
        ring.albedoTexture = new Texture(
          "5mm_He_Cover.jpg",
          this.scene,
          false,
          false
        );
        ring.albedoColor = new Color3.FromInts(35, 35, 35).toLinearSpace();
        // chain.albedoTexture = new Texture("5mm_He_Cover.jpg", this.scene, false, false);
        chain.albedoColor = new Color3.FromInts(35, 35, 35).toLinearSpace();
        liquid1small.material.clearCoat.tintColor = new Color3.FromInts(
          206,
          167,
          107
        ).toLinearSpace();
        liquid1small.material.clearCoat.tintColorAtDistance = 4;
        liquid1small.material.clearCoat.tintThickness = 3;
        this.scene.getMeshByName("product1small").setEnabled(true);
        tool.setEnabled(true);
        TweenMax.to(product1small.position, 0.75, {
          x: -0.173,
          y: 0.502,
          z: 0.179,
        });
        TweenMax.to(tool.position, 0.75, {
          x: 0.474,
          y: 0.4,
          z: 0.325,
        });
        // TweenMax.to(product1small.position, 0.75, {
        //   x: -0.119,
        //   y: 1.507,
        //   z: 0.192,
        // });
        // TweenMax.to(tool.position, 0.75, {
        //   x: 0.386,
        //   y: 1.461,
        //   z: 0.310,
        // });
      } else if (prod == "2") {
        this.props.ProductDetailsChanger("YOU");
        this.setState({ currentModel: "body2" });
        this.props.updateCurrentModel("body2");
        TweenMax.to(product2.position, 0.75, {
          x: 0.127,
          y: 1.65,
          z: 0.21,
        });
        TweenMax.to(product1.position, 0.75, {
          x: this.state.positionRight.x,
          y: this.state.positionRight.y,
          z: this.state.positionRight.z,
        });
        TweenMax.to(product3.position, 0.75, {
          x: this.state.positionLeft.x,
          y: this.state.positionLeft.y,
          z: this.state.positionLeft.z,
        });
        //small bottle and tool
        this.scene.getMeshByName("product1small").setEnabled(true);
        productBody1small.emissiveTexture = new Texture(
          "5mm_She and They_Refraction_V2.jpg",
          this.scene,
          false,
          false
        );
        liquid1small.material.clearCoat.tintColor = new Color3.FromInts(
          234,
          231,
          218
        ).toLinearSpace();
        liquid1small.material.clearCoat.tintColorAtDistance = 2.8;
        liquid1small.material.clearCoat.tintThickness = 3;
        cover1small.material.albedoTexture = null;
        cover1small.material.albedoColor = new Color3.FromInts(
          224,
          184,
          123
        ).toLinearSpace();
        ring.albedoColor = new Color3.FromInts(224, 184, 123).toLinearSpace();
        ring.albedoTexture = null;
        chain.albedoColor = new Color3.FromInts(224, 184, 123).toLinearSpace();
        // chain.albedoTexture = null;
        tool.setEnabled(true);
        TweenMax.to(product1small.position, 0.75, {
          x: -0.173,
          y: 0.502,
          z: 0.179,
        });
        TweenMax.to(tool.position, 0.75, {
          x: 0.474,
          y: 0.4,
          z: 0.325,
        });
        // TweenMax.to(product1small.position, 0.75, {
        //   x: -0.119,
        //   y: 1.507,
        //   z: 0.192,
        // });
        // TweenMax.to(tool.position, 0.75, {
        //   x: 0.386,
        //   y: 1.461,
        //   z: 0.310,
        // });
      } else if (prod == "3") {
        this.props.ProductDetailsChanger("WE");
        this.setState({ currentModel: "body3" });
        this.props.updateCurrentModel("body3");
        TweenMax.to(product3.position, 0.75, {
          x: 0.127,
          y: 1.65,
          z: 0.21,
        });
        TweenMax.to(product2.position, 0.75, {
          x: this.state.positionRight.x,
          y: this.state.positionRight.y,
          z: this.state.positionRight.z,
        });
        TweenMax.to(product1.position, 0.75, {
          x: this.state.positionLeft.x,
          y: this.state.positionLeft.y,
          z: this.state.positionLeft.z,
        });
        //small bottle and tool
        this.scene.getMeshByName("product1small").setEnabled(true);
        productBody1small.emissiveTexture = new Texture(
          "5mm_She and They_Refraction_V2.jpg",
          this.scene,
          false,
          false
        );
        cover1small.material.albedoTexture = new Texture(
          "5mm_They_Cover.jpg",
          this.scene,
          false,
          false
        );
        cover1small.material.albedoColor = new Color3.FromInts(
          255,
          212,
          190
        ).toLinearSpace();
        ring.albedoTexture = new Texture(
          "5mm_They_Cover.jpg",
          this.scene,
          false,
          false
        );
        ring.albedoColor = new Color3.FromInts(255, 212, 190).toLinearSpace();
        // chain.albedoTexture = new Texture("5mm_They_Cover.jpg", this.scene, false, false);
        chain.albedoColor = new Color3.FromInts(150, 84, 42).toLinearSpace();
        liquid1small.material.clearCoat.tintColor = new Color3.FromInts(
          238,
          219,
          188
        ).toLinearSpace();
        liquid1small.material.clearCoat.tintColorAtDistance = 0.65;
        liquid1small.material.clearCoat.tintThickness = 1;
        tool.setEnabled(true);
        TweenMax.to(product1small.position, 0.75, {
          x: -0.173,
          y: 0.502,
          z: 0.179,
        });
        TweenMax.to(tool.position, 0.75, {
          x: 0.474,
          y: 0.4,
          z: 0.325,
        });
        // TweenMax.to(product1small.position, 0.75, {
        //   x: -0.119,
        //   y: 1.507,
        //   z: 0.192,
        // });
        // TweenMax.to(tool.position, 0.75, {
        //   x: 0.386,
        //   y: 1.461,
        //   z: 0.310,
        // });
      }
      TweenMax.to(this.camera, 1.5, {
        radius: 1.55,
        alpha: 1.7654,
        beta: 1.5773,
        lowerRadiusLimit: 1.55,
        upperRadiusLimit: 1.55,
      });
      TweenMax.to(this.camera.target, 0.75, {
        x: 0.124,
        z: 0.2,
      });
      // this.camera.position=new Vector3(this.camera.position.x,this.camera.position.y,0.4)
      // this.camera.lowerRadiusLimit= 1.3
      // this.camera.upperRadiusLimit= 1.3902;
      this.camera.upperAlphaLimit = 2.7;
      this.camera.lowerAlphaLimit = 0.82;
      this.camera.lowerBetaLimit = 0.65;
      this.camera.upperBetaLimit = 2.05;
      this.camera.minZ = 0.01;
      
      rotatePlane = this.scene.getMeshByName("rotatePlane");
      rotatePlane.setEnabled(true);
      rotatePlane.position = new Vector3(0.07, 1.65, 0.429);
      rotatePlane.rotation.x = Tools.ToRadians(90);
      rotatePlane.rotation.y = Tools.ToRadians(168.85);
      rotatePlaneMaterial = this.scene.getMaterialByName("rotatePlaneMaterial");
      rotatePlane.material = rotatePlaneMaterial;
      rotatePlaneMaterial.alpha = 0;
      let rotateTexture = new Texture("./oval HR.png", this.scene);
      rotatePlaneMaterial.emissiveTexture = rotateTexture;
      rotatePlaneMaterial.opacityTexture = rotateTexture;

      setTimeout(() => {
        turnOnAnnotation(1); // turn on for all
      }, 1000);
      setTimeout(() => {
        that.props.ProductDisplayFunction();
      }, 500);

      // setTimeout(() => {
      //   TweenMax.to(rotatePlaneMaterial, 1, {
      //     alpha: 1
      //   });
      // }, 3500);

      this.setState({ scenenow: "thirdphase" });

      //Products Move Automatically
      // setTimeout(() => {
      //   productsTurn("smallBottle");
      //   setTimeout(() => {
      //     productsTurn("tool");
      //     setTimeout(() => {
      //       productsTurn("bottle");
      //     }, 3000);
      //   }, 3000);
      // }, 5000);

      // setTimeout(() => {
      //   TweenMax.to(product1.rotation, 0.2, {
      //     z: Math.PI/15,
      //   });
      //   setTimeout(() => {
      //     TweenMax.to(product1.rotation, 0.2, {
      //       z: -Math.PI/15,
      //     });
      //     setTimeout(() => {
      //       TweenMax.to(product1.rotation, 0.2, {
      //         z: Math.PI/15,
      //       });
      //       setTimeout(() => {
      //         TweenMax.to(product1.rotation, 0.2, {
      //           z: 0,
      //         });
      //       }, 200);
      //     }, 200);
      //   }, 200);
      // }, 3500);

      //Galactic Spin
      // TweenMax.to(product1.rotation, 120, {
      //   x: Math.PI, // x-axis
      //   y: 2*Math.PI, // z-axis
      //   z: -Math.PI, // y-axis
      // });

      // let ease = new QuadraticEase();
      // ease.setEasingMode(EasingFunction.EASINGMODE_EASEINOUT);
      // var rotationQuaternion = new Quaternion.RotationAxis(
      //   new Vector3(1, +0.176, 0),
      //   Math.PI
      // );
      // console.log(self.scene.getMeshByName("product1").rotationQuaternion)
      // Animation.CreateAndStartAnimation(
      //   "rotationQuaternion",
      //   self.scene.getMeshByName("product1"),
      //   "rotationQuaternion",
      //   30,
      //   30,
      //   self.scene.getMeshByName("product1")
      //     .rotationQuaternion,
      //   rotationQuaternion,
      //   0,
      //   ease
      // );
    };

    function animationreturn(that) {
      console.log(that.scene.getMeshByName(that.state.currentModel).parent.name)
      let ease = new QuadraticEase();
      ease.setEasingMode(EasingFunction.EASINGMODE_EASEINOUT);
      var rotationQuaternionsmall;
      if (
        that.scene.getMeshByName(that.state.currentModel).parent.name ==
        "product1small"
      ) {
        rotationQuaternionsmall = new Quaternion.RotationAxis(
          new Vector3(
            Tools.ToRadians(145),
            Tools.ToRadians(-100),
            Tools.ToRadians(105)
          ),
          -Math.PI / 2
        );
        Animation.CreateAndStartAnimation(
          "rotationQuaternion",
          that.scene.getMeshByName(that.state.currentModel).parent,
          "rotationQuaternion",
          30,
          30,
          that.scene.getMeshByName(that.state.currentModel).parent
            .rotationQuaternion,
          rotationQuaternionsmall,
          0,
          ease
        );
      } else if (
        that.scene.getMeshByName(that.state.currentModel).parent.name ==
        "tool"
      ) {
        rotationQuaternionsmall = new Quaternion.RotationAxis(
          new Vector3(0, 1, 1),
          -Math.PI
        );
        Animation.CreateAndStartAnimation(
          "rotationQuaternion",
          that.scene.getMeshByName(that.state.currentModel).parent,
          "rotationQuaternion",
          30,
          30,
          that.scene.getMeshByName(that.state.currentModel).parent
            .rotationQuaternion,
          rotationQuaternionsmall,
          0,
          ease
        );
      } else if (
        that.scene.getMeshByName(that.state.currentModel).parent.name ==
          "product1" ||
        that.scene.getMeshByName(that.state.currentModel).parent.name ==
          "product3" ||
        that.scene.getMeshByName(that.state.currentModel).parent.name ==
          "product2"
      ) {
        rotationQuaternionsmall = new Quaternion.RotationAxis(
          new Vector3(1, +0.176, 0),
          -Math.PI / 2
        );
        Animation.CreateAndStartAnimation(
          "rotationQuaternion",
          that.scene.getMeshByName(that.state.currentModel).parent,
          "rotationQuaternion",
          30,
          30,
          that.scene.getMeshByName(that.state.currentModel).parent
            .rotationQuaternion,
          rotationQuaternionsmall,
          0,
          ease
        );
      }
    }

    let productsTurn = (prod) => {
      this.setState({
        galacticBottle: true,
        galacticSmall: true,
        galacticTool: true,
      });
      // setTimeout(() => {
      //   animationreturn(this);
      // }, 1500);
      // product1.rotation = new Vector3(
      //   Tools.ToRadians(-90),
      //   Tools.ToRadians(350.8),
      //   Tools.ToRadians(0)
      // );
      // // product2.rotation = new Vector3(Tools.ToRadians(36.0660), Tools.ToRadians(-104.3192), Tools.ToRadians(83.2523));
      // product2.rotation = new Vector3(
      //   Tools.ToRadians(-90),
      //   Tools.ToRadians(350.8),
      //   Tools.ToRadians(0)
      // );
      // product3.rotation = new Vector3(
      //   Tools.ToRadians(-90),
      //   Tools.ToRadians(350.8),
      //   Tools.ToRadians(0)
      // );
      // product1small.rotation = new Vector3(
      //   Tools.ToRadians(175),
      //   Tools.ToRadians(260),
      //   Tools.ToRadians(90)
      // );
      // tool.rotation = new Vector3(Tools.ToRadians(-74.0407), Tools.ToRadians(-56.3869), Tools.ToRadians(-132.6720));

      var product;
      if (counter == 1) {
        product = product1;
        this.setState({ currentModel: "body1" });
        this.props.updateCurrentModel("body1");
      } else if (counter == 0) {
        product = product2;
        this.setState({ currentModel: "body2" });
        this.props.updateCurrentModel("body2");
      } else if (counter == 2) {
        product = product3;
        this.setState({ currentModel: "body3" });
        this.props.updateCurrentModel("body3");
      }

      if (prod != "tool") {
        if (this.state.currentModel == "body1") {
          this.props.ProductDetailsChanger("I");
        } else if (this.state.currentModel == "body2") {
          this.props.ProductDetailsChanger("YOU");
        } else if (this.state.currentModel == "body3") {
          this.props.ProductDetailsChanger("WE");
        }
      }

      if (prod == "tool") {
        this.setState({ currentModel: "toolbody" });
        if (this.state.toolCurrentColor === "Obsidian") {
          this.props.updateCurrentModel("toolObsidian");
          this.props.ProductDetailsChanger("toolObsidian");
        } else if (this.state.toolCurrentColor === "Opal"){
          this.props.updateCurrentModel("toolOpal");
          this.props.ProductDetailsChanger("toolOpal");
        } else if (this.state.toolCurrentColor === "Tiger"){
          this.props.updateCurrentModel("toolTiger");
          this.props.ProductDetailsChanger("toolTiger");
        }
        TweenMax.to(tool.position, 1, {
          x: 0.127,
          y: 1.65,
          z: 0.281,
        });
        TweenMax.to(product.position, 1, {
          x: -0.175,
          y: 0.2,
          z: 0.247,
        });
        TweenMax.to(product1small.position, 1, {
          x: 0.423,
          y: 0.499,
          z: 0.366,
        });
      } else if (prod == "smallBottle") {
        this.setState({ currentModel: "body1small" });
        this.props.updateCurrentModel("bodySmall");
        TweenMax.to(product1small.position, 1, {
          x: 0.119,
          y: 1.65,
          z: 0.257,
        });
        TweenMax.to(tool.position, 1, {
          x: -0.124,
          y: 0.3,
          z: 0.256,
        });
        TweenMax.to(product.position, 1, {
          x: 0.464,
          y: 0.2,
          z: 0.345,
        });
      } else if (prod == "bottle") {
        TweenMax.to(product.position, 1, {
          x: 0.127,
          y: 1.65,
          z: 0.21,
        });
        TweenMax.to(product1small.position, 1, {
          x: -0.173,
          y: 0.502,
          z: 0.179,
        });
        TweenMax.to(tool.position, 1, {
          x: 0.474,
          y: 0.4,
          z: 0.325,
        });
      }
    };

    let turnOnAnnotation = (product) => {
      this.setState({ rotatable: true, rotating: false });
      // TweenMax.to(this.camera, 0.5, {
      //   radius: 1.8,
      //   alpha: 1.7654,
      //   beta: 1.5773,
      //   lowerRadiusLimit: 1.8,
      //   upperRadiusLimit: 1.8,
      // });
      setTimeout(() => {
        this.camera.lowerAlphaLimit = 1.66;
        this.camera.upperAlphaLimit = 1.86;
        this.camera.lowerBetaLimit = 1.47;
        this.camera.upperBetaLimit = 1.67;
        if (product == 1 || product == 2 || product == 3) {
          // torusforbody1.setEnabled(true);
          // torusforcover1.setEnabled(true);
          // disk1.setEnabled(true);
          // disk2.setEnabled(true);
          // productsmallannotation1.setEnabled(true)
          // // productsmallannotation2.setEnabled(true)
          // productsmalldisk1.setEnabled(true)
          // // productsmalldisk2.setEnabled(true)
          // torusfortool1.setEnabled(true)
          // diskfortool.setEnabled(true)
          // // torusforsmallbottle1.setEnabled(true);
          // // torusfortool1.setEnabled(true);
          // product2annotation1.setEnabled(true)
          // product2annotation2.setEnabled(true)
          // product2disk1.setEnabled(true)
          // product2disk1.setEnabled(true)
          // product3annotation1.setEnabled(true)
          // product3annotation2.setEnabled(true)
          // product3disk1.setEnabled(true)
          // product3disk2.setEnabled(true)
          // this.setState({annotationstate: true})
        }
      }, 510);
    };

    // this.scene.onPointerObservable.add((pointerInfo) => {
    //   if (this.props.animationEnd && this.state.scenenow == "secondphase") {
    //     switch (pointerInfo.type) {
    //       case PointerEventTypes.POINTERDOWN:
    //         break;
    //       case PointerEventTypes.POINTERUP:
    //         console.log("POINTER UP");
    //         break;
    //       case PointerEventTypes.POINTERMOVE:
    //         var alpha = 1.49 + (this.scene.pointerX / window.innerWidth) * (2 - 1.49);
    //         var beta = 1.4 + (this.scene.pointerY / window.innerHeight) * (1.74 - 1.4);
    //         this.camera.spinToMouseMovment("alpha", alpha, 200);
    //         this.camera.spinToMouseMovment("beta", beta, 200);

    //         break;
    //       case PointerEventTypes.POINTERWHEEL:
    //         console.log("POINTER WHEEL");
    //         break;
    //       case PointerEventTypes.POINTERPICK:
    //         console.log("POINTER PICK");
    //         break;
    //       case PointerEventTypes.POINTERTAP:
    //         console.log("POINTER TAP");
    //         break;
    //       case PointerEventTypes.POINTERDOUBLETAP:
    //         console.log("POINTER DOUBLE-TAP");
    //         break;
    //     }
    //   }
    // });

    this.scene.onPointerObservable.add((pointerInfo) => {
      switch (pointerInfo.type) {
        case PointerEventTypes.POINTERDOWN:
          if (this.state.scenenow == "secondphase") {
            this.scene.hoverCursor = "pointer";
          } else {
            this.scene.hoverCursor = "grabbing";
          }
          break;

        case PointerEventTypes.POINTERMOVE:
          if (this.state.scenenow == "secondphase") {
            this.scene.hoverCursor = "pointer";
          }
          break;

        case PointerEventTypes.POINTERUP:
          if (this.state.scenenow == "secondphase") {
            this.scene.hoverCursor = "pointer";
          } else {
            this.scene.hoverCursor = "grab";
          }
          break;
      }
    });

    // this.scene.onPointerObservable.add((pointerInfo) => {
    //   switch (pointerInfo.type) {
    //     case PointerEventTypes.POINTERDOWN:
    //       clearTimeout(myfunc);
    //       speed = 0;
    //       break;
    //     case PointerEventTypes.POINTERUP:
    //       myfunc = setTimeout(Func, 1500);
    //       break;
    //   }
    // });

    var turningCounter = 1;
    var turning = true;
    var that = this;
    var speedClockwise = 0.0007;
    var speedAntiClockwise = -0.0007;
    var skipper = 0;
    this.scene.registerBeforeRender(function () {
      //Your code here
      if (that.props.animationEnd && that.state.scenenow == "secondphase") {
        var alpha =
          1.71 + (that.scene.pointerX / window.innerWidth) * (1.78 - 1.71);
        var beta =
          1.51 + (that.scene.pointerY / window.innerHeight) * (1.63 - 1.51);

        // console.log(alpha);
        if (that.camera.alpha == alpha) {
          that.camera.alpha = that.camera.alpha;
        } else if (
          that.camera.alpha - alpha > 0.002 &&
          that.camera.alpha > alpha
        ) {
          that.camera.alpha -= 0.002;
        } else if (that.camera.alpha - alpha < 0 && that.camera.alpha < alpha) {
          that.camera.alpha += 0.002;
        }

        if (that.camera.beta == beta) {
          that.camera.beta = that.camera.beta;
        } else if (
          that.camera.beta - beta > 0.0015 &&
          that.camera.beta > beta
        ) {
          that.camera.beta -= 0.0015;
        } else if (that.camera.beta - beta < 0 && that.camera.beta < beta) {
          that.camera.beta += 0.0015;
        }
      }
      if (that.props.animationEnd && that.state.scenenow == "thirdphase") {
        //bottles and Galactic Spin
        if (skipper == 0){
        if(that.state.galacticSmall){
        product1small.addRotation(speedAntiClockwise, speedClockwise, speedClockwise);}
        if(that.state.galacticTool){
        tool.addRotation(speedAntiClockwise, speedClockwise, speedAntiClockwise);}
        // TweenMax.to(product1small.rotation, 120, {
        //   x: Math.PI, // x-axis
        //   y: 2*Math.PI, // z-axis
        //   z: Math.PI, // y-axis
        // });
        // TweenMax.to(tool.rotation, 120, {
        //   x: -Math.PI, // x-axis
        //   y: 2*Math.PI, // z-axis
        //   z: Math.PI, // y-axis
        // });
        // To do later fix check from Pd sound to making it a state for checking current bottle theme
        if (that.props.PDSound.substring(0, that.props.PDSound.indexOf(" ")) == "I"){
          // product2.rotation = new Vector3(
          //   Tools.ToRadians(-90),
          //   Tools.ToRadians(350.8),
          //   Tools.ToRadians(0)
          // );
          // product3.rotation = new Vector3(
          //   Tools.ToRadians(-90),
          //   Tools.ToRadians(350.8),
          //   Tools.ToRadians(0)
          // );
          if (that.state.galacticBottle){
          product1.addRotation(speedAntiClockwise, speedClockwise, speedAntiClockwise);}
          product2.addRotation(0.0, speedClockwise, 0.0);
          product3.addRotation(0.0, speedAntiClockwise, 0.0);}
        else if (that.props.PDSound.substring(0, that.props.PDSound.indexOf(" ")) == "You2"){
          // product1.rotation = new Vector3(
          //   Tools.ToRadians(-90),
          //   Tools.ToRadians(350.8),
          //   Tools.ToRadians(0)
          // );
          // product3.rotation = new Vector3(
          //   Tools.ToRadians(-90),
          //   Tools.ToRadians(350.8),
          //   Tools.ToRadians(0)
          // );
          product1.addRotation(0.0, speedAntiClockwise, 0.0);
          if (that.state.galacticBottle){
          product2.addRotation(speedAntiClockwise, speedClockwise, speedClockwise);}
          product3.addRotation(0.0, speedClockwise, 0.0);}
        else if (that.props.PDSound.substring(0, that.props.PDSound.indexOf(" ")) == "we"){
          // product1.rotation = new Vector3(
          //   Tools.ToRadians(-90),
          //   Tools.ToRadians(350.8),
          //   Tools.ToRadians(0)
          // );
          // product2.rotation = new Vector3(
          //   Tools.ToRadians(-90),
          //   Tools.ToRadians(350.8),
          //   Tools.ToRadians(0)
          // );
          product1.addRotation(0.0, speedClockwise, 0.0);
          product2.addRotation(0.0, speedAntiClockwise, 0.0);
          if (that.state.galacticBottle){
          product3.addRotation(speedAntiClockwise, speedClockwise, speedClockwise);}}
          skipper = 3
        } else {
          skipper = skipper-1
        }

        //camera
        var alpha =
          1.665 + (that.scene.pointerX / window.innerWidth) * (1.865 - 1.665);
        var beta =
          1.477 + (that.scene.pointerY / window.innerHeight) * (1.677 - 1.477);

        if (that.camera.alpha == alpha) {
          that.camera.alpha = that.camera.alpha;
        } else if (
          that.camera.alpha - alpha > 0.001 &&
          that.camera.alpha > alpha
        ) {
          that.camera.alpha -= 0.001;
        } else if (that.camera.alpha - alpha < 0 && that.camera.alpha < alpha) {
          that.camera.alpha += 0.001;
        }

        if (that.camera.beta == beta) {
          that.camera.beta = that.camera.beta;
        } else if (that.camera.beta - beta > 0.001 && that.camera.beta > beta) {
          that.camera.beta -= 0.001;
        } else if (that.camera.beta - beta < 0 && that.camera.beta < beta) {
          that.camera.beta += 0.001;
        }
      }
      if (that.state.scenenow == "heart") {
        // console.log(that.props.windowX,that.props.windowY)
        var alpha =
          4.84 + (that.props.windowX / window.innerWidth) * (4.94 - 4.84);
        var beta =
          1.55 + (that.props.windowY / window.innerHeight) * (1.59 - 1.55);

        // console.log(alpha);
        if (that.aboutCamera.alpha == alpha) {
          that.aboutCamera.alpha = that.aboutCamera.alpha;
        } else if (
          that.aboutCamera.alpha - alpha > 0.002 &&
          that.aboutCamera.alpha > alpha
        ) {
          that.aboutCamera.alpha -= 0.002;
        } else if (
          that.aboutCamera.alpha - alpha < 0 &&
          that.aboutCamera.alpha < alpha
        ) {
          that.aboutCamera.alpha += 0.002;
        }

        if (that.aboutCamera.beta == beta) {
          that.aboutCamera.beta = that.aboutCamera.beta;
        } else if (
          that.aboutCamera.beta - beta > 0.001 &&
          that.aboutCamera.beta > beta
        ) {
          that.aboutCamera.beta -= 0.001;
        } else if (
          that.aboutCamera.beta - beta < 0 &&
          that.aboutCamera.beta < beta
        ) {
          that.aboutCamera.beta += 0.001;
        }
      }
      // if (that.state.scenenow === "thirdphase"){
      //   if (turningCounter === 1){
      //     setTimeout(() => {
      //       productsTurn("smallBottle");
      //     }, 2000);
      //     turningCounter = 2;
      //   }
      //   if (turningCounter === 2){
      //     setTimeout(() => {
      //       productsTurn("tool");
      //     }, 2000);
      //     turningCounter = 3;
      //   }
      //   if (turningCounter === 3){
      //     setTimeout(() => {
      //       productsTurn("bottle");
      //     }, 2000);
      //     turningCounter = 1;
      //   }
      // }
      // if (that.state.scenenow === "thirdphase" && turning === true){
      //   turning = false;
      //     setTimeout(() => {
      //       productsTurn("smallBottle");
      //     }, 2000);
      //     setTimeout(() => {
      //       productsTurn("tool");
      //     }, 2000);
      //     setTimeout(() => {
      //       productsTurn("bottle");
      //     }, 2000);
      // }
    });

    // this.camera.detachControl()
    // this.camera.attachControl(this.stage, false);

    const rightDir = new Vector3();
    const upDir = new Vector3();
    const sensitivity = 0.009;

    this.scene.onPointerObservable.add((pointerInfo) => {
      var old_rotation = 0;
      function animationreturn(that) {
        let ease = new QuadraticEase();
        ease.setEasingMode(EasingFunction.EASINGMODE_EASEINOUT);
        var rotationQuaternionsmall;
        if (
          that.scene.getMeshByName(that.state.currentModel).parent.name ==
          "product1small"
        ) {
          rotationQuaternionsmall = new Quaternion.RotationAxis(
            new Vector3(
              Tools.ToRadians(145),
              Tools.ToRadians(-100),
              Tools.ToRadians(105)
            ),
            -Math.PI / 2
          );
          Animation.CreateAndStartAnimation(
            "rotationQuaternion",
            that.scene.getMeshByName(that.state.currentModel).parent,
            "rotationQuaternion",
            30,
            30,
            that.scene.getMeshByName(that.state.currentModel).parent
              .rotationQuaternion,
            rotationQuaternionsmall,
            0,
            ease
          );
        } else if (
          that.scene.getMeshByName(that.state.currentModel).parent.name ==
          "tool"
        ) {
          rotationQuaternionsmall = new Quaternion.RotationAxis(
            new Vector3(0, 1, 1),
            -Math.PI
          );
          Animation.CreateAndStartAnimation(
            "rotationQuaternion",
            that.scene.getMeshByName(that.state.currentModel).parent,
            "rotationQuaternion",
            30,
            30,
            that.scene.getMeshByName(that.state.currentModel).parent
              .rotationQuaternion,
            rotationQuaternionsmall,
            0,
            ease
          );
        } else if (
          that.scene.getMeshByName(that.state.currentModel).parent.name ==
            "product1" ||
          that.scene.getMeshByName(that.state.currentModel).parent.name ==
            "product3" ||
          that.scene.getMeshByName(that.state.currentModel).parent.name ==
            "product2"
        ) {
          rotationQuaternionsmall = new Quaternion.RotationAxis(
            new Vector3(1, +0.176, 0),
            -Math.PI / 2
          );
          Animation.CreateAndStartAnimation(
            "rotationQuaternion",
            that.scene.getMeshByName(that.state.currentModel).parent,
            "rotationQuaternion",
            30,
            30,
            that.scene.getMeshByName(that.state.currentModel).parent
              .rotationQuaternion,
            rotationQuaternionsmall,
            0,
            ease
          );
        }
      }

      var that = this;
      const returnanimation = () => {
        const timer = setTimeout(() => {
          animationreturn(that);
        }, 1500);
        return timer;
      };

      if (
        this.state.scenenow === "thirdphase" &&
        this.state.rotatable === true
      ) {
        if (pointerInfo.type === 1) {
          //pointer down
          TweenMax.to(rotatePlaneMaterial, 0.5, {
            alpha: 0
          });
          this.setState({ rotating: true });
          this.props.pictogram("hide");
          // console.log(returnanimation());
          var id = returnanimation();
          while (id--) {
            window.clearTimeout(id); // will do nothing if no timeout with id is present
          }
        } else if (pointerInfo.type === 2 && this.state.rotating) {
          //pointer up
          returnanimation();
          this.setState({ rotating: false});
          if (this.state.currentModel == "body1" || this.state.currentModel == "body2" || this.state.currentModel == "body3"){
            this.setState({galacticBottle: false });
            setTimeout(() => {
              this.setState({galacticBottle: true });
            }, 7000);
          } else if (this.state.currentModel == "body1small"){
            this.setState({galacticSmall: false });
            setTimeout(() => {
              this.setState({galacticSmall: true });
            }, 7000);
          } else if (this.state.currentModel == "toolbody"){
            this.setState({galacticTool: false });
            setTimeout(() => {
              this.setState({galacticTool: true });
            }, 7000);
          }

          var timeout = 1500;
          // var returnanimation() = setTimeout(() => {}, timeout);
        } else if (pointerInfo.type === 4 && this.state.rotating) {
          //pointer move
          var id = returnanimation();
          while (id--) {
            window.clearTimeout(id); // will do nothing if no timeout with id is present
          }
          // clearTimeout(returnanimation());
          // window.clearTimeout(returnanimation());
          const cameramatrix = this.camera.getWorldMatrix();
          const modelmatrix = this.scene
            .getMeshByName(this.state.currentModel)
            .getWorldMatrix();
          rightDir.copyFromFloats(
            cameramatrix.m[0],
            cameramatrix.m[1],
            cameramatrix.m[2]
          );
          upDir.copyFromFloats(
            modelmatrix.m[4],
            modelmatrix.m[5],
            modelmatrix.m[6]
          );
          // this.scene.getMeshByName(this.state.currentModel).parent.physicsImpostor =
          // new PhysicsImpostor(this.scene.getMeshByName(this.state.currentModel).parent, PhysicsImpostor.SphereImpostor, { mass: 0.0, friction: 0, restitution: 0 }, this.scene);
          // console.log(this.scene.getMeshByName(this.state.currentModel).parent.rotation)
          // if( this.scene.getMeshByName(this.state.currentModel).parent.rotation._x!=0){
          //   this.setState({old_rotation: this.scene.getMeshByName(this.state.currentModel).parent.rotation});
          //   // old_rotation=this.scene.getMeshByName(this.state.currentModel).parent.rotation
          // }
          this.scene
            .getMeshByName(this.state.currentModel)
            .parent.rotateAround(
              this.scene.getMeshByName(this.state.currentModel).parent.position,
              rightDir,
              pointerInfo.event.movementY * -1 * sensitivity
            );
          this.scene
            .getMeshByName(this.state.currentModel)
            .parent.rotateAround(
              this.scene.getMeshByName(this.state.currentModel).parent.position,
              upDir,
              pointerInfo.event.movementX * -1 * sensitivity
            );

          // console.log(this.scene.getMeshByName(this.state.currentModel).parent.rotation)
          // console.log(this.scene.getMeshByName(this.state.currentModel).parent.rotation)
        }
      }
    });

    var ToolFlag = false;

    this.engine.runRenderLoop(() => {
      this.scene.render();

      if (this.state.scenenow !== "secondphase") {
        box1.isPickable = false;
        box2.isPickable = false;
        box3.isPickable = false;
      } else {
        box1.isPickable = true;
        box2.isPickable = true;
        box3.isPickable = true;
      }
      if (tool.isEnabled() == true && ToolFlag == false) {
        this.setState({ scenenow: "thirdphase" });
        setTimeout(() => {
          ToolFlag = true;
        }, 2000);
      }

      

      // if (this.props.animationEnd && this.state.scenenow == "secondphase") {
      //   this.camera.alpha = 1.345 + (this.scene.pointerX/window.innerWidth)*(2.145 - 1.345);
      //   this.camera.beta = 1.27 + (this.scene.pointerY/window.innerHeight)*(1.87- 1.27);
      // }

      this.scene
        .getMeshByName("Annotation_01_1")
        .setEnabled(this.state.annotationstate);
      this.scene
        .getMeshByName("Annotation_02_1")
        .setEnabled(this.state.annotationstate);
      this.scene
        .getMeshByName("Annotation_01_tool")
        .setEnabled(this.state.annotationstate);
      this.scene
        .getMeshByName("product1disk1")
        .setEnabled(this.state.annotationstate);
      this.scene
        .getMeshByName("product1disk2")
        .setEnabled(this.state.annotationstate);
      this.scene
        .getMeshByName("productsmallannotation1")
        .setEnabled(this.state.annotationstate);
        this.scene
        .getMeshByName("productsmallannotation2")
        .setEnabled(this.state.annotationstate);
      this.scene
        .getMeshByName("productsmalldisk1")
        .setEnabled(this.state.annotationstate);
        this.scene
        .getMeshByName("productsmalldisk2")
        .setEnabled(this.state.annotationstate);
      this.scene
        .getMeshByName("Disk_tool")
        .setEnabled(this.state.annotationstate);
      this.scene
        .getMeshByName("product2annotation1")
        .setEnabled(this.state.annotationstate);
      this.scene
        .getMeshByName("product2annotation2")
        .setEnabled(this.state.annotationstate);
      this.scene
        .getMeshByName("product2disk1")
        .setEnabled(this.state.annotationstate);
      this.scene
        .getMeshByName("product2disk2")
        .setEnabled(this.state.annotationstate);
      this.scene
        .getMeshByName("product3annotation1")
        .setEnabled(this.state.annotationstate);
      this.scene
        .getMeshByName("product3annotation2")
        .setEnabled(this.state.annotationstate);
      this.scene
        .getMeshByName("product3disk1")
        .setEnabled(this.state.annotationstate);
      this.scene
        .getMeshByName("product3disk2")
        .setEnabled(this.state.annotationstate);
    });
  };

  Startanimation = () => {
    var animationcamera = this.scene.getCameraById("VRayCam001");
    var animation = this.scene.getAnimationGroupByName("All Animations");
    var logo = this.scene.getMeshByName("KiraLogo001");
    var body = this.scene.getMeshByName("body");

    var product1 = this.scene.getMeshByName("product1");
    var product2 = this.scene.getMeshByName("product2");
    var product3 = this.scene.getMeshByName("product3");
    var product1small = this.scene.getMeshByName("product1small");
    var tool = this.scene.getMeshByName("tool");

    this.cameraStart.spinTo("alpha", 1.5707, 100);
    this.cameraStart.spinTo("beta", 1.2598, 100);

    TweenMax.to(this.scene.getMeshByName("mind1"), 1, {
      visibility: 0,
    });
    TweenMax.to(this.scene.getMeshByName("mind2"), 1, {
      visibility: 0,
    });
    TweenMax.to(this.scene.getMeshByName("truth"), 1, {
      visibility: 0,
    });
    TweenMax.to(this.scene.getMeshByName("heart"), 1, {
      visibility: 0,
    });
    TweenMax.to(this.scene.getMeshByName("center"), 1, {
      visibility: 0,
    });
    TweenMax.to(this.scene.getMeshByName("relief1"), 1, {
      visibility: 0,
    });
    TweenMax.to(this.scene.getMeshByName("relief2"), 1, {
      visibility: 0,
    });
    TweenMax.to(this.scene.getMeshByName("energize1"), 1, {
      visibility: 0,
    });
    TweenMax.to(this.scene.getMeshByName("energize2"), 1, {
      visibility: 0,
    });
    TweenMax.to(this.scene.getMeshByName("ground1"), 1, {
      visibility: 0,
    });
    TweenMax.to(this.scene.getMeshByName("ground2"), 1, {
      visibility: 0,
    });
    this.scene.getMeshByName("mind1big").setEnabled(false);
    this.scene.getMeshByName("mind2big").setEnabled(false);
    this.scene.getMeshByName("truthbig").setEnabled(false);
    this.scene.getMeshByName("heartbig").setEnabled(false);
    this.scene.getMeshByName("centerbig").setEnabled(false);
    this.scene.getMeshByName("relief1big").setEnabled(false);
    this.scene.getMeshByName("relief2big").setEnabled(false);
    this.scene.getMeshByName("energize1big").setEnabled(false);
    this.scene.getMeshByName("energize2big").setEnabled(false);
    this.scene.getMeshByName("ground1big").setEnabled(false);
    this.scene.getMeshByName("ground2big").setEnabled(false);

    var atAnimationEnd = async function () {
      // console.log("animation ended");
      this.setState({
        scenenow: "secondphase",
      });
      document.getElementsByClassName("App")[0].style.cursor = "grab";

      this.scene.getMeshByName("box").isPickable = true;
      this.scene.getMeshByName("box2").isPickable = true;
      this.scene.getMeshByName("box3").isPickable = true;

      this.camera.minZ = 0.01;
      this.camera.fov = 0.5;
      this.camera.Plane = 10000.0;
      this.camera.lowerRadiusLimit = 3.75;
      this.camera.upperRadiusLimit = 3.75;
      this.camera.upperAlphaLimit = 2.0;
      this.camera.lowerAlphaLimit = 1.49;
      this.camera.lowerBetaLimit = 1.4;
      this.camera.upperBetaLimit = 1.74;
      this.camera.angularSensibilityX = 5000;
      this.camera.angularSensibilityY = 5000;
      this.camera.speed = 1;
      this.camera.radius = 3.75;
      this.camera.alpha = 1.7453;
      this.camera.beta = 1.5711;
      this.scene.activeCamera = this.camera;
      this.camera.attachControl(this.stage, true);
      this.scene.getMeshByName("product1").setEnabled(true);
      this.scene.getMeshByName("product2").setEnabled(true);
      this.scene.getMeshByName("product3").setEnabled(true);

      let animationsvisib = await Animation.ParseFromFileAsync(
        null,
        "./lid_visibilty.json"
      );
  
      // var body_B1 = this.scene.getMeshByName("body1");
      // var helix1 = this.scene.getMeshByName("helix1");
      // var lid1 = this.scene.getMeshByName("cover1");
      // var liquid1 = this.scene.getMeshByName("liquid1");

      // body_B1.animations = animationsvisib;
      // helix1.animations = animationsvisib;
      // lid1.animations = animationsvisib;
      // liquid1.animations = animationsvisib;

      // this.scene.beginAnimation(body_B1, 0, 100, false);
      // this.scene.beginAnimation(helix1, 0, 100, false);
      // this.scene.beginAnimation(lid1, 0, 100, false);
      // this.scene.beginAnimation(liquid1, 0, 100, false);

      // var body_B2 = this.scene.getMeshByName("body2");
      // var helix2 = this.scene.getMeshByName("helix2");
      // var lid2 = this.scene.getMeshByName("cover2");
      // var liquid2 = this.scene.getMeshByName("liquid2");

      // body_B2.animations = animationsvisib;
      // helix2.animations = animationsvisib;
      // lid2.animations = animationsvisib;
      // liquid2.animations = animationsvisib;

      // this.scene.beginAnimation(body_B2, 0, 100, false);
      // this.scene.beginAnimation(helix2, 0, 100, false);
      // this.scene.beginAnimation(lid2, 0, 100, false);
      // this.scene.beginAnimation(liquid2, 0, 100, false);

      // var body_B3 = this.scene.getMeshByName("body3");
      // var helix3 = this.scene.getMeshByName("helix3");
      // var lid3 = this.scene.getMeshByName("cover3");
      // var liquid3 = this.scene.getMeshByName("liquid3");

      // body_B3.animations = animationsvisib;
      // helix3.animations = animationsvisib;
      // lid3.animations = animationsvisib;
      // liquid3.animations = animationsvisib;

      // this.scene.beginAnimation(body_B3, 0, 100, false);
      // this.scene.beginAnimation(helix3, 0, 100, false);
      // this.scene.beginAnimation(lid3, 0, 100, false);
      // this.scene.beginAnimation(liquid3, 0, 100, false);

      this.camera.attachControl(this.stage, true);

      this.props.animationEndFunction();

      //rotate everything
      // this.scene.getMeshByName("product1").rotation.x = Tools.ToRadians(0);
      // this.scene.getMeshByName("product2").rotation.x = Tools.ToRadians(0);
      // this.scene.getMeshByName("product3").rotation.x = Tools.ToRadians(0);
      // logo.rotationQuaternion = new Quaternion.RotationAxis(new Vector3(0, 1, 0), Math.PI / 2);
      // body.rotation.x = Tools.ToRadians(0);
      // this.camera.upVector = new Vector3(-0.176, 1, 0);

      var width = this.scene.pointerX / window.innerWidth;
      var height = this.scene.pointerY / window.innerHeight;
      var positionalpha = 1.49 + width * (2.145 - 1.49);
      var positionbeta = 1.4 + height * (1.74 - 1.4);
      this.camera.spinTo("alpha", positionalpha, 200);
      this.camera.spinTo("beta", positionbeta, 200);
      setTimeout(() => {
        this.setState({
          scenenow: "secondphase",
        });
      }, 200);
    };
    atAnimationEnd = atAnimationEnd.bind(this);
    var timeout;
    if (
      this.cameraStart.alpha > 1.4 &&
      this.cameraStart.alpha < 1.75 &&
      this.cameraStart.beta > 1.1 &&
      this.cameraStart.beta < 1.5
    ) {
      timeout = 400;
    } else {
      timeout = 1500;
    }
    setTimeout(() => {
      this.cameraStart.detachControl(this.stage, true);
      this.scene.activeCamera = animationcamera;
      animationcamera.detachControl(this.stage, true);
      logo.setParent(body);
      animation.start(false, 1.42);
      // animation.animatables[1].onAnimationEnd = atAnimationEnd;
      setTimeout(() => {
        this.scene.getMeshByName("body1").visibility = 1;
        this.scene.getMeshByName("cover1").visibility = 1;
        this.scene.getMeshByName("helix1").visibility = 1;
        this.scene.getMeshByName("liquid1").visibility = 1;
        this.scene.getMeshByName("body2").visibility = 1;
        this.scene.getMeshByName("cover2").visibility = 1;
        this.scene.getMeshByName("helix2").visibility = 1;
        this.scene.getMeshByName("liquid2").visibility = 1;
        this.scene.getMeshByName("body3").visibility = 1;
        this.scene.getMeshByName("cover3").visibility = 1;
        this.scene.getMeshByName("helix3").visibility = 1;
        this.scene.getMeshByName("liquid3").visibility = 1;
        setTimeout(() => {
          // this.scene.getMaterialByName("BodyMaterial").fillMode = Material.PointListDrawMode;
          TweenMax.to(this.scene.getMaterialByName("BodyMaterial"), 1, {
            alpha: 0.5,
          });
        }, 1000);
      }, 4000);
      setTimeout(() => {
        atAnimationEnd();
      }, 7100);
    }, timeout);
  };

  Aboutanimation = async () => {
    var product1 = this.scene.getMeshByName("product1");
    var product2 = this.scene.getMeshByName("product2");
    var product3 = this.scene.getMeshByName("product3");
    var product1small = this.scene.getMeshByName("product1small");
    var tool = this.scene.getMeshByName("tool");
    var logo = this.scene.getMeshByName("KiraLogo001");
    var body = this.scene.getMeshByName("body");
    var animationcamera = this.scene.getCameraById("VRayCam001");
    var animation = this.scene.getAnimationGroupByName("All Animations");
    let heartAnimation = await Animation.ParseFromFileAsync(
      null,
      "./HumanHeartCamAnimationWithRewind.json"
    );
    let heartAnimationFromBottles = await Animation.ParseFromFileAsync(
      null,
      "./HumanHeartAnimationsZoomedIn.json"
    );
    var box1 = this.scene.getMeshByName("box");
    var box2 = this.scene.getMeshByName("box2");
    var box3 = this.scene.getMeshByName("box3");

    if (this.state.scenenow == "start") {
      TweenMax.to(this.scene.getMeshByName("mind1"), 1, {
        visibility: 0,
      });
      TweenMax.to(this.scene.getMeshByName("mind2"), 1, {
        visibility: 0,
      });
      TweenMax.to(this.scene.getMeshByName("truth"), 1, {
        visibility: 0,
      });
      TweenMax.to(this.scene.getMeshByName("heart"), 1, {
        visibility: 0,
      });
      TweenMax.to(this.scene.getMeshByName("center"), 1, {
        visibility: 0,
      });
      TweenMax.to(this.scene.getMeshByName("relief1"), 1, {
        visibility: 0,
      });
      TweenMax.to(this.scene.getMeshByName("relief2"), 1, {
        visibility: 0,
      });
      TweenMax.to(this.scene.getMeshByName("energize1"), 1, {
        visibility: 0,
      });
      TweenMax.to(this.scene.getMeshByName("energize2"), 1, {
        visibility: 0,
      });
      TweenMax.to(this.scene.getMeshByName("ground1"), 1, {
        visibility: 0,
      });
      TweenMax.to(this.scene.getMeshByName("ground2"), 1, {
        visibility: 0,
      });
      this.scene.getMeshByName("mind1big").setEnabled(false);
      this.scene.getMeshByName("mind2big").setEnabled(false);
      this.scene.getMeshByName("truthbig").setEnabled(false);
      this.scene.getMeshByName("heartbig").setEnabled(false);
      this.scene.getMeshByName("centerbig").setEnabled(false);
      this.scene.getMeshByName("relief1big").setEnabled(false);
      this.scene.getMeshByName("relief2big").setEnabled(false);
      this.scene.getMeshByName("energize1big").setEnabled(false);
      this.scene.getMeshByName("energize2big").setEnabled(false);
      this.scene.getMeshByName("ground1big").setEnabled(false);
      this.scene.getMeshByName("ground2big").setEnabled(false);

      this.cameraStart.detachControl(this.stage, true);
      this.cameraStart.spinTo("alpha", 1.5707, 100);
      this.cameraStart.spinTo("beta", 1.2598, 100);

      var timeout;
      if (
        this.cameraStart.alpha > 1.4 &&
        this.cameraStart.alpha < 1.75 &&
        this.cameraStart.beta > 1.1 &&
        this.cameraStart.beta < 1.5
      ) {
        timeout = 750;
      } else {
        timeout = 1500;
      }
      this.props.cameraAnimationEndTimeoutFunction(timeout + 4550);
      setTimeout(() => {
        // this.cameraStart.moveTargetTo(new Vector3(-0.375, 4.584, 0.67), 50);
        // this.cameraStart.lowerRadiusLimit = 0;
        // this.cameraStart.spinTo("radius", 0.6, 50);
        // this.props.cameraAnimationEndFunction();

        this.cameraStart.detachControl(this.stage, true);
        this.scene.activeCamera = animationcamera;
        animationcamera.detachControl(this.stage, true);
        logo.setParent(body);
        animation.start(false, 3);
        // animation.animatables[1].onAnimationEnd = atAnimationEnd;
        setTimeout(() => {
          this.scene.getMeshByName("body1").visibility = 1;
          this.scene.getMeshByName("cover1").visibility = 1;
          this.scene.getMeshByName("helix1").visibility = 1;
          this.scene.getMeshByName("liquid1").visibility = 1;
          this.scene.getMeshByName("body2").visibility = 1;
          this.scene.getMeshByName("cover2").visibility = 1;
          this.scene.getMeshByName("helix2").visibility = 1;
          this.scene.getMeshByName("liquid2").visibility = 1;
          this.scene.getMeshByName("body3").visibility = 1;
          this.scene.getMeshByName("cover3").visibility = 1;
          this.scene.getMeshByName("helix3").visibility = 1;
          this.scene.getMeshByName("liquid3").visibility = 1;
          TweenMax.to(this.scene.getMaterialByName("BodyMaterial"), 1, {
            alpha: 0.5,
          });
        }, 1500);
        setTimeout(() => {
          this.setState({ scenenow: "heart" });
          animationcamera.parent.animations = heartAnimation;
          this.scene.beginAnimation(animationcamera.parent, 0, 375, false, 2.5);
          setTimeout(() => {
            this.scene.activeCamera = this.aboutCamera;
          }, 2000);
        }, 3250);
      }, timeout);
    } else if (this.state.scenenow == "thirdphase") {
      this.props.ProductDisplaySwitch("close");
      this.props.pictogram("hide");
      TweenMax.to(this.scene.getMaterialByName("rotatePlaneMaterial"), 0.5, {
        alpha: 0
      });
      this.camera.detachControl(this.stage, true);
      this.setState({ scenenow: "heart" });
      this.camera.lowerRadiusLimit = 0;
      this.camera.upperRadiusLimit = 40;
      this.camera.upperAlphaLimit = 5;
      this.camera.lowerAlphaLimit = 0;
      this.camera.lowerBetaLimit = 0;
      this.camera.upperBetaLimit = 5;
      TweenMax.to(this.camera.target, 0.75, {
        x: 0.14,
        z: 0.196,
      });
      this.camera.spinTo("alpha", 1.7454, 200);
      this.camera.spinTo("beta", 1.5705, 200);
      this.camera.spinTo("radius", 1.79, 200);

      setTimeout(() => {
        animationcamera.parent.animations = heartAnimationFromBottles;
        this.scene.activeCamera = animationcamera;
        this.scene.beginAnimation(animationcamera.parent, 0, 375, false);

        setTimeout(() => {
          product1.position = new Vector3(0.07, 1.65, 0.55);
          product2.position = new Vector3(0.485, 1.686, 0.78);
          product3.position = new Vector3(-0.388, 1.686, 0.635);
          product1small.position = new Vector3(0.104, 1.486, 0.285);
          tool.position = new Vector3(0.186, 1.486, 0.282);
          product1small.setEnabled(false);
          tool.setEnabled(false);
          // product1.rotation = new Vector3(Tools.ToRadians(-90), Tools.ToRadians(350.8), Tools.ToRadians(0));
          // product2.rotation = new Vector3(Tools.ToRadians(36.0660), Tools.ToRadians(-104.3192), Tools.ToRadians(83.2523));
          // product3.rotation = new Vector3(Tools.ToRadians(-90), Tools.ToRadians(350.8), Tools.ToRadians(0));
          // product1small.rotation = new Vector3(Tools.ToRadians(175), Tools.ToRadians(260), Tools.ToRadians(90));
          // tool.rotation = new Vector3(Tools.ToRadians(270), Tools.ToRadians(180), Tools.ToRadians(0));

          TweenMax.to(this.camera.target, 0.75, {
            x: 0.069,
            z: 0.48,
          });

          box1.isPickable = true;
          box2.isPickable = true;
          box3.isPickable = true;
        }, 5000);

        setTimeout(() => {
          this.scene.activeCamera = this.aboutCamera;
        }, 6300);
      }, 1000);
    } else if (this.state.scenenow == "secondphase") {
      //second phase
      // this.Homeanimation();

      // setTimeout(() => {
      //   this.cameraStart.detachControl(this.stage, true);
      //   this.props.cameraAnimationEndTimeoutFunction(750 + 1800);
      //   this.props.startdisplayOfffunction();
      //   this.cameraStart.moveTargetTo(new Vector3(-0.375, 4.584, 0.67), 50);
      //   this.cameraStart.lowerRadiusLimit = 0;
      //   this.cameraStart.spinTo("radius", 0.6, 50);
      //   this.props.cameraAnimationEndFunction();
      // }, 750 + 5000);

      // document.getElementsByClassName("App")[0].style.cursor = "default";
      document.getElementsByClassName("INCORPbottomend")[0].style.cursor = "grab";
      this.props.ProductDisplaySwitch("close");
      this.camera.detachControl(this.stage, true);
      this.setState({ scenenow: "heart" });
      this.camera.lowerRadiusLimit = 0;
      this.camera.upperRadiusLimit = 40;
      this.camera.upperAlphaLimit = 5;
      this.camera.lowerAlphaLimit = 0;
      this.camera.lowerBetaLimit = 0;
      this.camera.upperBetaLimit = 5;
      this.camera.moveTargetTo(new Vector3(0.069, 1.65, 0.48), 200);
      this.camera.spinTo("alpha", 1.7453, 200);
      this.camera.spinTo("beta", 1.5711, 200);
      this.camera.spinTo("radius", 3.75, 200);

      setTimeout(() => {
        animationcamera.parent.animations = heartAnimation;
        this.scene.activeCamera = animationcamera;
        this.scene.beginAnimation(animationcamera.parent, 0, 375, false);

        setTimeout(() => {
          product1.position = new Vector3(0.07, 1.65, 0.55);
          product2.position = new Vector3(0.485, 1.686, 0.78);
          product3.position = new Vector3(-0.388, 1.686, 0.635);
          product1small.position = new Vector3(0.104, 1.486, 0.285);
          tool.position = new Vector3(0.186, 1.486, 0.282);
          product1small.setEnabled(false);
          tool.setEnabled(false);
          // product1.rotation = new Vector3(Tools.ToRadians(-90), Tools.ToRadians(350.8), Tools.ToRadians(0));
          // product2.rotation = new Vector3(Tools.ToRadians(36.0660), Tools.ToRadians(-104.3192), Tools.ToRadians(83.2523));
          // product3.rotation = new Vector3(Tools.ToRadians(-90), Tools.ToRadians(350.8), Tools.ToRadians(0));
          // product1small.rotation = new Vector3(Tools.ToRadians(175), Tools.ToRadians(260), Tools.ToRadians(90));
          // tool.rotation = new Vector3(Tools.ToRadians(270), Tools.ToRadians(180), Tools.ToRadians(0));

          box1.isPickable = true;
          box2.isPickable = true;
          box3.isPickable = true;
        }, 5000);

        setTimeout(() => {
          this.scene.activeCamera = this.aboutCamera;
        }, 6300);
      }, 500);
    }
  };

  Heartback = async () => {
    var animationcamera = this.scene.getCameraById("VRayCam001");
    let heartAnimation = await Animation.ParseFromFileAsync(
      null,
      "./HumanHeartCamAnimationWithRewind.json"
    );

    this.camera.lowerRadiusLimit = 3.75;
    this.camera.upperRadiusLimit = 3.75;
    this.camera.upperAlphaLimit = 2.0;
    this.camera.lowerAlphaLimit = 1.49;
    this.camera.lowerBetaLimit = 1.4;
    this.camera.upperBetaLimit = 1.74;

    animationcamera.parent.animations = heartAnimation;
    this.scene.activeCamera = animationcamera;
    this.scene.beginAnimation(animationcamera.parent, 375, 500, false);
    setTimeout(() => {
      this.props.animationEndFunction();
      this.scene.activeCamera = this.camera;
      var width = this.scene.pointerX / window.innerWidth;
      var height = this.scene.pointerY / window.innerHeight;
      var positionalpha = 1.49 + width * (2.145 - 1.49);
      var positionbeta = 1.4 + height * (1.74 - 1.4);
      this.camera.spinTo("alpha", positionalpha, 200);
      this.camera.spinTo("beta", positionbeta, 200);
      setTimeout(() => {
        this.setState({
          scenenow: "secondphase",
        });
      }, 200);
    }, 1000);
  };

  Shopanimation = () => {
    var logo = this.scene.getMeshByName("KiraLogo001");
    var body = this.scene.getMeshByName("body");

    this.cameraStart.detachControl(this.stage, true);
    this.cameraStart.spinTo("alpha", 1.5707, 100);
    this.cameraStart.spinTo("beta", 1.2598, 100);

    var timeout;
    if (
      this.cameraStart.alpha > 1.4 &&
      this.cameraStart.alpha < 1.75 &&
      this.cameraStart.beta > 1.1 &&
      this.cameraStart.beta < 1.5
    ) {
      timeout = 750;
    } else {
      timeout = 1500;
    }
    this.props.cameraAnimationEndTimeoutFunction(timeout + 1800);
    setTimeout(() => {
      logo.setParent(body);
      this.cameraStart.moveTargetTo(new Vector3(0.145, 7.584, 0.42), 30);
      this.cameraStart.lowerRadiusLimit = 0;
      this.cameraStart.spinTo("radius", 0.6, 50);
      this.props.cameraAnimationEndFunction();
    }, timeout);
  };

  Homeanimation = async () => {
    if (this.state.scenenow == "secondphase") {
      var product1 = this.scene.getMeshByName("product1");
      var product2 = this.scene.getMeshByName("product2");
      var product3 = this.scene.getMeshByName("product3");
      var product1small = this.scene.getMeshByName("product1small");
      var tool = this.scene.getMeshByName("tool");
      var animationcamera = this.scene.getCameraById("VRayCam001");
      let heartAnimation = await Animation.ParseFromFileAsync(
        null,
        "./HumanHeartCamAnimationWithRewind.json"
      );

      // document.getElementsByClassName("App")[0].style.cursor = "default";
      document.getElementsByClassName("INCORPbottomend")[0].style.cursor = "grab";
      TweenMax.to(this.scene.getMaterialByName("BodyMaterial"), 5, {
        alpha: 1,
      });

      this.props.updateSceneStatus();
      var animation = this.scene.getAnimationGroupByName("All Animations");
      var animationcamera = this.scene.getCameraById("VRayCam001");
      var logo = this.scene.getMeshByName("KiraLogo001");
      if (this.state.scenenow == "start") {
        this.cameraStart.moveTargetTo(new Vector3(0.08, 0.55, -0.95), 100);
        this.cameraStart.spinTo("alpha", 1.5707, 100);
        this.cameraStart.spinTo("beta", 1.2598, 100);
        this.cameraStart.spinTo("radius", 39.11, 100);
        setTimeout(() => {
          this.cameraStart.lowerRadiusLimit = 39.11;
          this.cameraStart.attachControl(this.stage, true);
          this.props.startdisplayfunction();
        }, 1200);
      } else {
        if (this.state.scenenow === "heart") {
          animationcamera.parent.animations = heartAnimation;
          this.scene.activeCamera = animationcamera;
          this.scene.beginAnimation(animationcamera.parent, 375, 500, false);
        } else {
          this.props.ProductDisplaySwitch("close");
          this.camera.lowerRadiusLimit = 0;
          this.camera.upperRadiusLimit = 40;
          this.camera.upperAlphaLimit = 5;
          this.camera.lowerAlphaLimit = 0;
          this.camera.lowerBetaLimit = 0;
          this.camera.upperBetaLimit = 5;
          this.camera.moveTargetTo(new Vector3(0.069, 1.65, 0.48), 200);
          this.camera.spinTo("alpha", 1.7453, 200);
          this.camera.spinTo("beta", 1.5711, 200);
          this.camera.spinTo("radius", 3.75, 200);
        }

        this.setState({ scenenow: "start" });
        this.camera.detachControl(this.stage, true);

        setTimeout(() => {
          this.cameraStart.alpha = 1.5707;
          this.cameraStart.beta = 1.2598;
          this.cameraStart.radius = 39.11;
          this.cameraStart.target = new Vector3(0.08, 0.55, -0.95);
          this.cameraStart.fov = 0.5;
          this.scene.activeCamera = animationcamera;
          animation.start(false, 3, 600, 0);
          setTimeout(() => {
            product1.position = new Vector3(0.07, 1.65, 0.55);
            product2.position = new Vector3(0.485, 1.686, 0.78);
            product3.position = new Vector3(-0.388, 1.686, 0.635);
            product1small.position = new Vector3(0.104, 1.486, 0.285);
            tool.position = new Vector3(0.186, 1.486, 0.282);
            this.scene.getMeshByName("body1").visibility = 0;
            this.scene.getMeshByName("cover1").visibility = 0;
            this.scene.getMeshByName("helix1").visibility = 0;
            this.scene.getMeshByName("liquid1").visibility = 0;
            this.scene.getMeshByName("body2").visibility = 0;
            this.scene.getMeshByName("cover2").visibility = 0;
            this.scene.getMeshByName("helix2").visibility = 0;
            this.scene.getMeshByName("liquid2").visibility = 0;
            this.scene.getMeshByName("body3").visibility = 0;
            this.scene.getMeshByName("cover3").visibility = 0;
            this.scene.getMeshByName("helix3").visibility = 0;
            this.scene.getMeshByName("liquid3").visibility = 0;
            product1small.setEnabled(false);
            tool.setEnabled(false);
            this.props.ArrowsDisplayFunction("off");

            product1.rotation = new Vector3(
              Tools.ToRadians(-90),
              Tools.ToRadians(350.8),
              Tools.ToRadians(0)
            );
            product2.rotation = new Vector3(
              Tools.ToRadians(-90),
              Tools.ToRadians(350.8),
              Tools.ToRadians(0)
            );
            // product2.rotation = new Vector3(Tools.ToRadians(36.0660), Tools.ToRadians(-104.3192), Tools.ToRadians(83.2523));
            // product3.rotation = new Vector3(Tools.ToRadians(-90), Tools.ToRadians(350.8), Tools.ToRadians(0));
            product1small.rotation = new Vector3(
              Tools.ToRadians(175),
              Tools.ToRadians(260),
              Tools.ToRadians(90)
            );
            // tool.rotation = new Vector3(Tools.ToRadians(90), Tools.ToRadians(170), Tools.ToRadians(170));
          }, 1500);
          setTimeout(() => {
            TweenMax.to(this.scene.getMeshByName("mind1"), 1, {
              visibility: 1,
            });
            TweenMax.to(this.scene.getMeshByName("mind2"), 1, {
              visibility: 1,
            });
            TweenMax.to(this.scene.getMeshByName("truth"), 1, {
              visibility: 1,
            });
            TweenMax.to(this.scene.getMeshByName("heart"), 1, {
              visibility: 1,
            });
            TweenMax.to(this.scene.getMeshByName("center"), 1, {
              visibility: 1,
            });
            TweenMax.to(this.scene.getMeshByName("relief1"), 1, {
              visibility: 1,
            });
            TweenMax.to(this.scene.getMeshByName("relief2"), 1, {
              visibility: 1,
            });
            TweenMax.to(this.scene.getMeshByName("energize1"), 1, {
              visibility: 1,
            });
            TweenMax.to(this.scene.getMeshByName("energize2"), 1, {
              visibility: 1,
            });
            TweenMax.to(this.scene.getMeshByName("ground1"), 1, {
              visibility: 1,
            });
            TweenMax.to(this.scene.getMeshByName("ground2"), 1, {
              visibility: 1,
            });
            this.scene.getMeshByName("mind1big").setEnabled(true);
            this.scene.getMeshByName("mind2big").setEnabled(true);
            this.scene.getMeshByName("truthbig").setEnabled(true);
            this.scene.getMeshByName("heartbig").setEnabled(true);
            this.scene.getMeshByName("centerbig").setEnabled(true);
            this.scene.getMeshByName("relief1big").setEnabled(true);
            this.scene.getMeshByName("relief2big").setEnabled(true);
            this.scene.getMeshByName("energize1big").setEnabled(true);
            this.scene.getMeshByName("energize2big").setEnabled(true);
            this.scene.getMeshByName("ground1big").setEnabled(true);
            this.scene.getMeshByName("ground2big").setEnabled(true);

            this.cameraStart.attachControl(this.stage, true);
            logo.setParent(this.cameraStart);
            this.scene.activeCamera = this.cameraStart;
            this.props.startdisplayfunction();
          }, 3600);
        }, 1510);
      }
    } else {
      TweenMax.to(this.scene.getMaterialByName("BodyMaterial"), 1, {
        alpha: 0.5,
      });
      this.Productsanimation();
      document.getElementsByClassName("App")[0].style.cursor = "grab";
      let BodyMaterial = this.scene.getMaterialByName("BodyMaterial");
      // TweenMax.to(BodyMaterial, 1, {
      //     alpha: 1,
      //   });
    }
  };

  Productsanimation = async (input) => {
    var product1 = this.scene.getMeshByName("product1");
    var product2 = this.scene.getMeshByName("product2");
    var product3 = this.scene.getMeshByName("product3");
    var product1small = this.scene.getMeshByName("product1small");
    var tool = this.scene.getMeshByName("tool");

    this.props.ProductInfoSwitcherOff();
    TweenMax.to(this.scene.getMaterialByName("rotatePlaneMaterial"), 0.75, {
      alpha: 0
    });
    if (this.state.scenenow == "heart") {
      this.Heartback();
    } else if (this.state.scenenow == "thirdphase") {
      this.props.pictogram("hide");
      this.props.ProductDisplaySwitch("close");
      this.camera.detachControl(this.stage, true);
      this.scene.activeCamera = this.camera;
      this.camera.lowerRadiusLimit = 0;
      this.camera.upperRadiusLimit = 40;
      this.camera.upperAlphaLimit = 5;
      this.camera.lowerAlphaLimit = 0;
      this.camera.lowerBetaLimit = 0;
      this.camera.upperBetaLimit = 5;
      this.camera.moveTargetTo(new Vector3(0.069, 1.65, 0.48), 200);
      this.camera.spinTo("alpha", 1.7453, 200);
      this.camera.spinTo("beta", 1.5711, 200);
      this.camera.spinTo("radius", 3.75, 200);
      this.setState({ scenenow: "secondphase" });
      this.customiseObsidian()

      this.props.ArrowsDisplayFunction("off");
      TweenMax.to(product1.position, 0.75, {
        x: 0.07,
        y: 1.65,
        z: 0.55,
      });
      TweenMax.to(product2.position, 0.75, {
        x: 0.485,
        y: 1.686,
        z: 0.78,
      });
      TweenMax.to(product3.position, 0.75, {
        x: -0.388,
        y: 1.686,
        z: 0.635,
      });
      TweenMax.to(product1small.position, 0.75, {
        x: 0.07,
        y: 1.486,
        z: 0.55,
      });
      TweenMax.to(tool.position, 0.75, {
        x: 0.07,
        y: 1.486,
        z: 0.55,
      });
      TweenMax.to(this.scene.getMaterialByName("Chains"), 0.7, {
        alpha: 0,
      });

      setTimeout(() => {
        product1small.setEnabled(false);
        tool.setEnabled(false);
        this.scene.getMaterialByName("Chains").alpha = 1;
      }, 750);
    } else if (this.state.scenenow == "secondphase") {
      //second phase
      // this.Homeanimation();
    }
  };

  Contactanimation = async (info) => {
    if (info.detail.action === "open") {
      if (
        this.state.scenenow == "heart" ||
        this.state.scenenow == "thirdphase"
      ) {
        this.Productsanimation();
      }
      setTimeout(() => {
        this.setState({
          scenenow: "contact",
        });
        this.camera.detachControl(this.stage, true);
        setTimeout(() => {
          // document.getElementsByClassName("App")[0].style.cursor = "default";
          document.getElementsByClassName("INCORPbottomend")[0].style.cursor = "grab";
          this.camera.lowerRadiusLimit = 0;
          this.camera.upperRadiusLimit = 40;
          this.camera.upperAlphaLimit = 5;
          this.camera.lowerAlphaLimit = 0;
          this.camera.lowerBetaLimit = 0;
          this.camera.upperBetaLimit = 5;
          this.camera.moveTargetTo(new Vector3(0.069, 2.65, 0.48), 200);
          this.camera.spinTo("alpha", 1.7414, 200);
          this.camera.spinTo("beta", 3.4415, 200);
          this.camera.spinTo("radius", 1.1173, 200);
          this.setState({
            scenenow: "contact",
          });
        }, 500);
      }, 1000);
    } else if (info.detail.action === "close") {
      this.camera.moveTargetTo(new Vector3(0.069, 1.65, 0.48), 200);
      this.camera.spinTo("alpha", 1.7453, 200);
      this.camera.spinTo("beta", 1.5711, 200);
      this.camera.spinTo("radius", 3.75, 200);

      setTimeout(() => {
        document.getElementsByClassName("App")[0].style.cursor = "grab";
        this.camera.lowerRadiusLimit = 3.75;
        this.camera.upperRadiusLimit = 3.75;
        this.camera.upperAlphaLimit = 2.0;
        this.camera.lowerAlphaLimit = 1.49;
        this.camera.lowerBetaLimit = 1.4;
        this.camera.upperBetaLimit = 1.74;
        this.setState({
          scenenow: "secondphase",
        });
        this.camera.attachControl(this.stage, true);
      }, 500);
    }
  };

  TurnOffAnnotations = (info) => {
    // console.log("Off Annotations")
    this.setState({ rotatable: true, rotating: false });
    this.camera.upperAlphaLimit = 2.7;
    this.camera.lowerAlphaLimit = 0.82;
    this.camera.lowerBetaLimit = 0.65;
    this.camera.upperBetaLimit = 2.05;
    if (
      info.detail.product == 1 ||
      info.detail.product == 2 ||
      info.detail.product == 3
    ) {
      this.scene.getMeshByName("Annotation_01_1").setEnabled(false);
      this.scene.getMeshByName("Annotation_02_1").setEnabled(false);
      this.scene.getMeshByName("Annotation_01_tool").setEnabled(false);

      this.scene.getMeshByName("product1disk1").setEnabled(false);
      this.scene.getMeshByName("product1disk2").setEnabled(false);

      this.scene.getMeshByName("productsmallannotation1").setEnabled(false);
      this.scene.getMeshByName("productsmallannotation2").setEnabled(false);
      this.scene.getMeshByName("productsmalldisk1").setEnabled(false);

      this.scene.getMeshByName("Disk_tool").setEnabled(false);
      this.scene.getMeshByName("product2annotation1").setEnabled(false);
      this.scene.getMeshByName("product2annotation2").setEnabled(false);
      this.scene.getMeshByName("product2disk1").setEnabled(false);
      this.scene.getMeshByName("product2disk1").setEnabled(false);
      this.scene.getMeshByName("product3annotation1").setEnabled(false);
      this.scene.getMeshByName("product3annotation2").setEnabled(false);
      this.scene.getMeshByName("product3disk1").setEnabled(false);
      this.scene.getMeshByName("product3disk2").setEnabled(false);
      this.setState({ annotationstate: false });
    }
  };

  annotationToggle = () => {
    if (this.state.annotationstate === false) {
      this.setState({ annotationstate: true });
    } else if (this.state.annotationstate === true) {
      this.setState({ annotationstate: false });
      this.props.ProductannotationsSwitchOff();
    }
  };

  bottlesetbesidecameraLeft = (prod) => {
    counter = prod;
    if (prod == -1) {
      counter = 2;
    }
    if (prod == 3) {
      counter = 0;
    }

    this.setState({ annotationstate: false });
    this.props.ProductInfoSwitcherOff();
    this.props.ProductannotationsSwitchOff();

    this.props.ProductDisplayBottleSwitch("Body", "close");
    this.props.ProductDisplayBottleSwitch("Cover", "close");
    this.props.ProductDisplayBottleSwitch("SmallBottle", "close");
    this.props.ProductDisplayBottleSwitch("Tool", "close");

    var product1 = this.scene.getMeshByName("product1");
    var product2 = this.scene.getMeshByName("product2");
    var product3 = this.scene.getMeshByName("product3");
    var productBody1small = this.scene.getMaterialByName("productBody1small");
    var product1small = this.scene.getMeshByName("product1small");
    var tool = this.scene.getMeshByName("tool");
    var cover1small = this.scene.getMeshByName("cover1small");
    var ring = this.scene.getMaterialByName("Ring");
    var chain = this.scene.getMaterialByName("Chains");
    var liquid1small = this.scene.getMeshByName("liquid1small");

    product1.rotation = new Vector3(
      Tools.ToRadians(-90),
      Tools.ToRadians(350.8),
      Tools.ToRadians(0)
    );
    // product2.rotation = new Vector3(Tools.ToRadians(36.0660), Tools.ToRadians(-104.3192), Tools.ToRadians(83.2523));
    product2.rotation = new Vector3(
      Tools.ToRadians(-90),
      Tools.ToRadians(350.8),
      Tools.ToRadians(0)
    );
    product3.rotation = new Vector3(
      Tools.ToRadians(-90),
      Tools.ToRadians(350.8),
      Tools.ToRadians(0)
    );
    // product1small.rotation = new Vector3(
    //   Tools.ToRadians(175),
    //   Tools.ToRadians(260),
    //   Tools.ToRadians(90)
    // );
    // tool.rotation = new Vector3(Tools.ToRadians(270), Tools.ToRadians(180), Tools.ToRadians(0));

    var product;
    if (counter == 1) {
      product = product1;
    } else if (counter == 0) {
      product = product2;
    } else if (counter == 2) {
      product = product3;
    }
    TweenMax.to(product.position, 0.5, {
      x: 0.127,
      y: 1.65,
      z: 0.21,
    });
    // TweenMax.to(product1small.position, 0.5, {
    //   x: -0.119,
    //   y: 1.507,
    //   z: 0.192,
    // });
    // TweenMax.to(tool.position, 0.5, {
    //   x: 0.386,
    //   y: 1.461,
    //   z: 0.310,
    // });
    TweenMax.to(product1small.position, 1, {
      x: -0.173,
      y: 0.502,
      z: 0.179,
    });
    TweenMax.to(tool.position, 1, {
      x: 0.474,
      y: 0.4,
      z: 0.325,
    });

    setTimeout(() => {
      if (counter == 1) {
        // console.log(counter)
        this.props.updateCurrentModel("body1");
        this.customiseObsidian()
        this.props.ProductDetailsChanger("I");
        this.setState({ currentModel: "body1" });
        productBody1small.emissiveTexture = new Texture(
          "5mm_He_Refraction_V2.jpg",
          this.scene,
          false,
          false
        );
        cover1small.material.albedoTexture = new Texture(
          "5mm_He_Cover.jpg",
          this.scene,
          false,
          false
        );
        cover1small.material.albedoColor = new Color3.FromInts(
          35,
          35,
          35
        ).toLinearSpace();
        ring.albedoTexture = new Texture(
          "5mm_He_Cover.jpg",
          this.scene,
          false,
          false
        );
        ring.albedoColor = new Color3.FromInts(35, 35, 35).toLinearSpace();
        // chain.albedoTexture = new Texture("5mm_He_Cover.jpg", this.scene, false, false);
        chain.albedoColor = new Color3.FromInts(35, 35, 35).toLinearSpace();
        liquid1small.material.clearCoat.tintColor = new Color3.FromInts(
          206,
          167,
          107
        ).toLinearSpace();
        liquid1small.material.clearCoat.tintColorAtDistance = 4;
        liquid1small.material.clearCoat.tintThickness = 3;
        TweenMax.to(product1.position, 1, {
          x: 0.127,
          y: 1.65,
          z: 0.21,
        });
        TweenMax.to(product3.position, 1, {
          x: this.state.positionRight.x,
          y: this.state.positionRight.y,
          z: this.state.positionRight.z,
        });
        TweenMax.to(product2.position, 0.5, {
          x: this.state.positionLeft.x - 2,
          y: this.state.positionLeft.y,
          z: this.state.positionLeft.z + 0.485,
        });
        setTimeout(() => {
          product2.position = new Vector3(0.95, 1.686, 0.6);
          TweenMax.to(product2.position, 0.5, {
            x: this.state.positionLeft.x,
            y: this.state.positionLeft.y,
            z: this.state.positionLeft.z,
          });
        }, 500);
      } else if (counter == 0) {
        // console.log(counter)
        this.props.updateCurrentModel("body2");
        this.customiseOpalInBackground()
        // this.customiseObsidian()
        this.props.ProductDetailsChanger("YOU");
        this.setState({ currentModel: "body2" });
        productBody1small.emissiveTexture = new Texture(
          "5mm_She and They_Refraction_V2.jpg",
          this.scene,
          false,
          false
        );
        liquid1small.material.clearCoat.tintColor = new Color3.FromInts(
          234,
          231,
          218
        ).toLinearSpace();
        liquid1small.material.clearCoat.tintColorAtDistance = 2.8;
        liquid1small.material.clearCoat.tintThickness = 3;
        cover1small.material.albedoTexture = null;
        cover1small.material.albedoColor = new Color3.FromInts(
          224,
          184,
          123
        ).toLinearSpace();
        ring.albedoColor = new Color3.FromInts(224, 184, 123).toLinearSpace();
        ring.albedoTexture = null;
        chain.albedoColor = new Color3.FromInts(224, 184, 123).toLinearSpace();
        // chain.albedoTexture = null;
        TweenMax.to(product2.position, 1, {
          x: 0.127,
          y: 1.65,
          z: 0.21,
        });
        TweenMax.to(product1.position, 1, {
          x: this.state.positionRight.x,
          y: this.state.positionRight.y,
          z: this.state.positionRight.z,
        });
        TweenMax.to(product3.position, 0.5, {
          x: this.state.positionLeft.x - 2,
          y: this.state.positionLeft.y,
          z: this.state.positionLeft.z + 0.485,
        });
        setTimeout(() => {
          product3.position = new Vector3(0.95, 1.686, 0.6);
          TweenMax.to(product3.position, 0.5, {
            x: this.state.positionLeft.x,
            y: this.state.positionLeft.y,
            z: this.state.positionLeft.z,
          });
        }, 500);
      } else if (counter == 2) {
        // console.log(counter)
        this.props.updateCurrentModel("body3");
        this.customiseTiger()
        this.props.ProductDetailsChanger("WE");
        this.setState({ currentModel: "body3" });
        productBody1small.emissiveTexture = new Texture(
          "5mm_She and They_Refraction_V2.jpg",
          this.scene,
          false,
          false
        );
        cover1small.material.albedoTexture = new Texture(
          "5mm_They_Cover.jpg",
          this.scene,
          false,
          false
        );
        cover1small.material.albedoColor = new Color3.FromInts(
          255,
          212,
          190
        ).toLinearSpace();
        ring.albedoTexture = new Texture(
          "5mm_They_Cover.jpg",
          this.scene,
          false,
          false
        );
        ring.albedoColor = new Color3.FromInts(255, 212, 190).toLinearSpace();
        // chain.albedoTexture = new Texture("5mm_They_Cover.jpg", this.scene, false, false);
        chain.albedoColor = new Color3.FromInts(150, 84, 42).toLinearSpace();
        liquid1small.material.clearCoat.tintColor = new Color3.FromInts(
          238,
          219,
          188
        ).toLinearSpace();
        liquid1small.material.clearCoat.tintColorAtDistance = 0.65;
        liquid1small.material.clearCoat.tintThickness = 1;
        TweenMax.to(product3.position, 1, {
          x: 0.127,
          y: 1.65,
          z: 0.21,
        });
        TweenMax.to(product2.position, 1, {
          x: this.state.positionRight.x,
          y: this.state.positionRight.y,
          z: this.state.positionRight.z,
        });
        TweenMax.to(product1.position, 0.5, {
          x: this.state.positionLeft.x - 2,
          y: this.state.positionLeft.y,
          z: this.state.positionLeft.z + 0.485,
        });
        setTimeout(() => {
          product1.position = new Vector3(0.95, 1.686, 0.6);
          TweenMax.to(product1.position, 0.5, {
            x: this.state.positionLeft.x,
            y: this.state.positionLeft.y,
            z: this.state.positionLeft.z,
          });
        }, 500);
      }
    }, 10);
  };

  bottlesetbesidecameraRight = (prod) => {
    counter = prod;
    if (prod == -1) {
      counter = 2;
    }
    if (prod == 3) {
      counter = 0;
    }

    this.setState({ annotationstate: false });
    this.props.ProductInfoSwitcherOff();
    this.props.ProductannotationsSwitchOff();

    this.props.ProductDisplayBottleSwitch("Body", "close");
    this.props.ProductDisplayBottleSwitch("Cover", "close");
    this.props.ProductDisplayBottleSwitch("SmallBottle", "close");
    this.props.ProductDisplayBottleSwitch("Tool", "close");

    var product1 = this.scene.getMeshByName("product1");
    var product2 = this.scene.getMeshByName("product2");
    var product3 = this.scene.getMeshByName("product3");
    var productBody1small = this.scene.getMaterialByName("productBody1small");
    var product1small = this.scene.getMeshByName("product1small");
    var tool = this.scene.getMeshByName("tool");
    var cover1small = this.scene.getMeshByName("cover1small");
    var ring = this.scene.getMaterialByName("Ring");
    var chain = this.scene.getMaterialByName("Chains");
    var liquid1small = this.scene.getMeshByName("liquid1small");

    product1.rotation = new Vector3(
      Tools.ToRadians(-90),
      Tools.ToRadians(350.8),
      Tools.ToRadians(0)
    );
    // product2.rotation = new Vector3(Tools.ToRadians(36.0660), Tools.ToRadians(-104.3192), Tools.ToRadians(83.2523));
    product2.rotation = new Vector3(
      Tools.ToRadians(-90),
      Tools.ToRadians(350.8),
      Tools.ToRadians(0)
    );
    product3.rotation = new Vector3(
      Tools.ToRadians(-90),
      Tools.ToRadians(350.8),
      Tools.ToRadians(0)
    );

    // product1small.rotation = new Vector3(
    //   Tools.ToRadians(175),
    //   Tools.ToRadians(260),
    //   Tools.ToRadians(90)
    // );
    // tool.rotation = new Vector3(Tools.ToRadians(270), Tools.ToRadians(180), Tools.ToRadians(0));

    var product;
    if (counter == 1) {
      product = product1;
    } else if (counter == 0) {
      product = product2;
    } else if (counter == 2) {
      product = product3;
    }
    TweenMax.to(product.position, 0.5, {
      x: 0.127,
      y: 1.65,
      z: 0.21,
    });
    // TweenMax.to(product1small.position, 0.5, {
    //   x: -0.119,
    //   y: 1.507,
    //   z: 0.192,
    // });
    // TweenMax.to(tool.position, 0.5, {
    //   x: 0.386,
    //   y: 1.461,
    //   z: 0.310,
    // });
    TweenMax.to(product1small.position, 1, {
      x: -0.173,
      y: 0.502,
      z: 0.179,
    });
    TweenMax.to(tool.position, 1, {
      x: 0.474,
      y: 0.4,
      z: 0.325,
    });

    setTimeout(() => {
      if (counter == 1) {
        // console.log(counter)
        this.props.updateCurrentModel("body1");
        this.customiseObsidian()
        this.props.ProductDetailsChanger("I");
        this.setState({ currentModel: "body1" });
        productBody1small.emissiveTexture = new Texture(
          "5mm_He_Refraction_V2.jpg",
          this.scene,
          false,
          false
        );
        cover1small.material.albedoTexture = new Texture(
          "5mm_He_Cover.jpg",
          this.scene,
          false,
          false
        );
        cover1small.material.albedoColor = new Color3.FromInts(
          35,
          35,
          35
        ).toLinearSpace();
        ring.albedoTexture = new Texture(
          "5mm_He_Cover.jpg",
          this.scene,
          false,
          false
        );
        ring.albedoColor = new Color3.FromInts(35, 35, 35).toLinearSpace();
        // chain.albedoTexture = new Texture("5mm_He_Cover.jpg", this.scene, false, false);
        chain.albedoColor = new Color3.FromInts(35, 35, 35).toLinearSpace();
        liquid1small.material.clearCoat.tintColor = new Color3.FromInts(
          206,
          167,
          107
        ).toLinearSpace();
        liquid1small.material.clearCoat.tintColorAtDistance = 4;
        liquid1small.material.clearCoat.tintThickness = 3;
        TweenMax.to(product1.position, 1, {
          x: 0.127,
          y: 1.65,
          z: 0.21,
        });
        TweenMax.to(product3.position, 0.5, {
          x: this.state.positionRight.x + 2.257,
          y: this.state.positionRight.y,
          z: this.state.positionRight.z + 0.885,
        });
        TweenMax.to(product2.position, 1, {
          x: this.state.positionLeft.x,
          y: this.state.positionLeft.y,
          z: this.state.positionLeft.z,
        });
        setTimeout(() => {
          product3.position = new Vector3(-0.8, 1.686, 0.32);
          TweenMax.to(product3.position, 0.5, {
            x: this.state.positionRight.x,
            y: this.state.positionRight.y,
            z: this.state.positionRight.z,
          });
        }, 500);
      } else if (counter == 0) {
        // console.log(counter)
        this.props.updateCurrentModel("body2");
        this.customiseOpalInBackground()
        // this.customiseObsidian()
        this.props.ProductDetailsChanger("YOU");
        this.setState({ currentModel: "body2" });
        productBody1small.emissiveTexture = new Texture(
          "5mm_She and They_Refraction_V2.jpg",
          this.scene,
          false,
          false
        );
        liquid1small.material.clearCoat.tintColor = new Color3.FromInts(
          234,
          231,
          218
        ).toLinearSpace();
        liquid1small.material.clearCoat.tintColorAtDistance = 2.8;
        liquid1small.material.clearCoat.tintThickness = 3;
        cover1small.material.albedoTexture = null;
        cover1small.material.albedoColor = new Color3.FromInts(
          224,
          184,
          123
        ).toLinearSpace();
        ring.albedoColor = new Color3.FromInts(224, 184, 123).toLinearSpace();
        ring.albedoTexture = null;
        chain.albedoColor = new Color3.FromInts(224, 184, 123).toLinearSpace();
        // chain.albedoTexture = null;
        TweenMax.to(product2.position, 1, {
          x: 0.127,
          y: 1.65,
          z: 0.21,
        });
        TweenMax.to(product1.position, 0.5, {
          x: this.state.positionRight.x + 2.257,
          y: this.state.positionRight.y,
          z: this.state.positionRight.z + 0.885,
        });
        TweenMax.to(product3.position, 1, {
          x: this.state.positionLeft.x,
          y: this.state.positionLeft.y,
          z: this.state.positionLeft.z,
        });
        setTimeout(() => {
          product1.position = new Vector3(-0.8, 1.686, 0.32);
          TweenMax.to(product1.position, 0.5, {
            x: this.state.positionRight.x,
            y: this.state.positionRight.y,
            z: this.state.positionRight.z,
          });
        }, 500);
      } else if (counter == 2) {
        // console.log(counter)
        this.props.updateCurrentModel("body3");
        this.customiseTiger()
        this.props.ProductDetailsChanger("WE");
        this.setState({ currentModel: "body3" });
        productBody1small.emissiveTexture = new Texture(
          "5mm_She and They_Refraction_V2.jpg",
          this.scene,
          false,
          false
        );
        cover1small.material.albedoTexture = new Texture(
          "5mm_They_Cover.jpg",
          this.scene,
          false,
          false
        );
        cover1small.material.albedoColor = new Color3.FromInts(
          255,
          212,
          190
        ).toLinearSpace();
        ring.albedoTexture = new Texture(
          "5mm_They_Cover.jpg",
          this.scene,
          false,
          false
        );
        ring.albedoColor = new Color3.FromInts(255, 212, 190).toLinearSpace();
        // chain.albedoTexture = new Texture("5mm_They_Cover.jpg", this.scene, false, false);
        chain.albedoColor = new Color3.FromInts(150, 84, 42).toLinearSpace();
        liquid1small.material.clearCoat.tintColor = new Color3.FromInts(
          238,
          219,
          188
        ).toLinearSpace();
        liquid1small.material.clearCoat.tintColorAtDistance = 0.65;
        liquid1small.material.clearCoat.tintThickness = 1;
        TweenMax.to(product3.position, 1, {
          x: 0.127,
          y: 1.65,
          z: 0.21,
        });
        TweenMax.to(product2.position, 0.5, {
          x: this.state.positionRight.x + 2.257,
          y: this.state.positionRight.y,
          z: this.state.positionRight.z + 0.885,
        });
        TweenMax.to(product1.position, 1, {
          x: this.state.positionLeft.x,
          y: this.state.positionLeft.y,
          z: this.state.positionLeft.z,
        });
        setTimeout(() => {
          product2.position = new Vector3(-0.8, 1.686, 0.32);
          TweenMax.to(product2.position, 0.5, {
            x: this.state.positionRight.x,
            y: this.state.positionRight.y,
            z: this.state.positionRight.z,
          });
        }, 500);
      }
    }, 10);
  };

  customiseObsidian = () => {
    this.setState({toolCurrentColor: "Obsidian"})
    if (this.props.currentModel === "toolObsidian" ||  this.props.currentModel === "toolOpal" || this.props.currentModel === "toolTiger"){
      this.props.updateCurrentModel("toolObsidian");
      this.props.ProductDetailsChanger("toolObsidian");
    }  
    // console.log("Obsidian")
    var toolBodyMaterial = this.scene.getMeshByName("toolbody").material
    toolBodyMaterial.albedoColor = new Color3.FromInts(255, 255, 255);
    toolBodyMaterial.emissiveColor = new Color3.FromInts(0,0,0);
    toolBodyMaterial.albedoTexture = new Texture(
      "Black_onyx.jpg",
      this.scene,
      false,
      false
    );
    toolBodyMaterial.refractionTexture = null;
    toolBodyMaterial.metallic = 0;
    toolBodyMaterial.roughness = 0;
    toolBodyMaterial.iridescence.isEnabled = true;
    toolBodyMaterial.iridescence.intensity = 0.63;
    toolBodyMaterial.iridescence.indexOfRefraction = 1.3;
    toolBodyMaterial.iridescence.minimumThickness = 100;
    toolBodyMaterial.iridescence.maximumThickness = 240;
    toolBodyMaterial.clearCoat.isEnabled = false;
    toolBodyMaterial.backFaceCulling = false;
    toolBodyMaterial.metallicF0Factor = 1;

    toolBodyMaterial.alpha = 1;
    toolBodyMaterial.transparencyMode = 0;
    toolBodyMaterial.alphaMode = 0;
    toolBodyMaterial.indexOfRefraction = 1.5;
    toolBodyMaterial.metallicReflectanceColor = new Color3.FromInts(255, 255, 255).toLinearSpace();
    toolBodyMaterial.subSurface.minimumThickness = 0;
    toolBodyMaterial.subSurface.maximumThickness = 1;
    toolBodyMaterial.subSurface.isTintEnabled = false;
    toolBodyMaterial.subSurface.isRefractionEnabled = false;
    toolBodyMaterial.subSurface.isTranslucencyEnabled = false;
  }

  customiseOpal = () => {
    this.setState({toolCurrentColor: "Opal"})
    if (this.props.currentModel === "toolObsidian" ||  this.props.currentModel === "toolOpal" || this.props.currentModel === "toolTiger"){
      this.props.updateCurrentModel("toolOpal");
      this.props.ProductDetailsChanger("toolOpal");
    }

    var toolBodyMaterial = this.scene.getMeshByName("toolbody").material
    // toolBodyMaterial.albedoColor = new Color3.FromInts(255, 255, 255).toLinearSpace();
    toolBodyMaterial.albedoColor = new Color3.FromInts(209, 169, 15).toLinearSpace();
    toolBodyMaterial.emissiveColor = new Color3.FromInts(0, 0, 0).toLinearSpace();
    toolBodyMaterial.albedoTexture = null;
    toolBodyMaterial.refractionTexture = new Texture(
      "OpalTexture.jpg",
      this.scene,
      false,
      false
    );
    toolBodyMaterial.metallic = 0.11;
    // toolBodyMaterial.roughness = 0.28;
    toolBodyMaterial.roughness = 0.2;
    toolBodyMaterial.iridescence.isEnabled = false;
    toolBodyMaterial.clearCoat.isEnabled = false;
    toolBodyMaterial.backFaceCulling = false;
    toolBodyMaterial.metallicF0Factor = 1;

    toolBodyMaterial.alpha = 0.7;
    toolBodyMaterial.transparencyMode = 2;
    toolBodyMaterial.alphaMode = 7;
    toolBodyMaterial.separateCullingPass = true;
    toolBodyMaterial.indexOfRefraction = 1.5;
    // toolBodyMaterial.metallicReflectanceColor = new Color3.FromInts(255, 255, 255).toLinearSpace();
    // toolBodyMaterial.metallicReflectanceColor = new Color3.FromInts(161, 143, 25).toLinearSpace();
    toolBodyMaterial.metallicReflectanceColor = new Color3.FromInts(205, 148, 26).toLinearSpace();
    toolBodyMaterial.subSurface.minimumThickness = 0;
    toolBodyMaterial.subSurface.maximumThickness = 1;
    toolBodyMaterial.subSurface.isTintEnabled = false;
    toolBodyMaterial.subSurface.isRefractionEnabled = true;
    toolBodyMaterial.subSurface.refractionIntensity = 0.78;
    toolBodyMaterial.subSurface.indexOfRefraction = 3;
    toolBodyMaterial.subSurface.tintColorAtDistance = 1;
    toolBodyMaterial.subSurface.useAlbedoToTintTranslucency = true;
    toolBodyMaterial.subSurface.isTranslucencyEnabled = true;
    toolBodyMaterial.subSurface.translucencyIntensity = 1;
    // toolBodyMaterial.subSurface.tintColor = new Color3.FromInts(249, 255, 254).toLinearSpace();
    toolBodyMaterial.subSurface.tintColor = new Color3.FromInts(232, 242, 243).toLinearSpace();
  }

  customiseOpalInBackground = () => {
    this.setState({toolCurrentColor: "Opal"})
    if (this.props.currentModel === "toolObsidian" || this.props.currentModel === "toolOpal" || this.props.currentModel === "toolTiger"){
      this.props.updateCurrentModel("toolOpal");
    }

    var toolBodyMaterial = this.scene.getMeshByName("toolbody").material
    // toolBodyMaterial.albedoColor = new Color3.FromInts(255, 255, 255).toLinearSpace();
    toolBodyMaterial.albedoColor = new Color3.FromInts(209, 169, 15).toLinearSpace();
    toolBodyMaterial.emissiveColor = new Color3.FromInts(0, 0, 0).toLinearSpace();
    toolBodyMaterial.albedoTexture = null;
    toolBodyMaterial.refractionTexture = new Texture(
      "OpalTexture.jpg",
      this.scene,
      false,
      false
    );
    toolBodyMaterial.metallic = 0.11;
    // toolBodyMaterial.roughness = 0.28;
    toolBodyMaterial.roughness = 0.2;
    toolBodyMaterial.iridescence.isEnabled = false;
    toolBodyMaterial.clearCoat.isEnabled = false;
    toolBodyMaterial.backFaceCulling = false;
    toolBodyMaterial.metallicF0Factor = 1;

    toolBodyMaterial.alpha = 1;
    toolBodyMaterial.transparencyMode = 2;
    toolBodyMaterial.alphaMode = 7;
    toolBodyMaterial.separateCullingPass = true;
    toolBodyMaterial.indexOfRefraction = 1.5;
    // toolBodyMaterial.metallicReflectanceColor = new Color3.FromInts(255, 255, 255).toLinearSpace();
    // toolBodyMaterial.metallicReflectanceColor = new Color3.FromInts(161, 143, 25).toLinearSpace();
    toolBodyMaterial.metallicReflectanceColor = new Color3.FromInts(205, 148, 26).toLinearSpace();
    toolBodyMaterial.subSurface.minimumThickness = 0;
    toolBodyMaterial.subSurface.maximumThickness = 1;
    toolBodyMaterial.subSurface.isTintEnabled = false;
    toolBodyMaterial.subSurface.isRefractionEnabled = true;
    toolBodyMaterial.subSurface.refractionIntensity = 0.78;
    toolBodyMaterial.subSurface.indexOfRefraction = 3;
    toolBodyMaterial.subSurface.tintColorAtDistance = 1;
    toolBodyMaterial.subSurface.useAlbedoToTintTranslucency = true;
    toolBodyMaterial.subSurface.isTranslucencyEnabled = true;
    toolBodyMaterial.subSurface.translucencyIntensity = 1;
    // toolBodyMaterial.subSurface.tintColor = new Color3.FromInts(249, 255, 254).toLinearSpace();
    toolBodyMaterial.subSurface.tintColor = new Color3.FromInts(232, 242, 243).toLinearSpace();
  }

  customiseTiger = () => {
    this.setState({toolCurrentColor: "Tiger"})
    if (this.props.currentModel === "toolObsidian" || this.props.currentModel === "toolOpal" || this.props.currentModel === "toolTiger"){
      this.props.updateCurrentModel("toolTiger");
      this.props.ProductDetailsChanger("toolTiger");
    }

    var toolBodyMaterial = this.scene.getMeshByName("toolbody").material
    toolBodyMaterial.albedoColor = new Color3.FromInts(255, 255, 255).toLinearSpace();
    // toolBodyMaterial.emissiveColor = new Color3.FromInts(193, 184, 150).toLinearSpace();
    toolBodyMaterial.emissiveColor = new Color3.FromInts(0, 0, 0).toLinearSpace();
    toolBodyMaterial.albedoTexture = new Texture(
      "TigerEyeTexture.png",
      this.scene,
      false,
      false
    );
    toolBodyMaterial.albedoTexture.uScale = 1;
    toolBodyMaterial.albedoTexture.vScale = 1;
    toolBodyMaterial.albedoTexture.vAng = Tools.ToRadians(0);
    toolBodyMaterial.albedoTexture.wAng = Tools.ToRadians(0);
    toolBodyMaterial.refractionTexture = null
    toolBodyMaterial.metallic = 0.4;
    toolBodyMaterial.roughness = 0;
    toolBodyMaterial.iridescence.isEnabled = false;
    toolBodyMaterial.clearCoat.isEnabled = true;
    toolBodyMaterial.clearCoat.intensity = 0.4;
    toolBodyMaterial.backFaceCulling = false;
    toolBodyMaterial.metallicF0Factor = 1;

    toolBodyMaterial.alpha = 1;
    toolBodyMaterial.transparencyMode = 0;
    toolBodyMaterial.alphaMode = 0;
    toolBodyMaterial.indexOfRefraction = 1.5;
    toolBodyMaterial.metallicReflectanceColor = new Color3.FromInts(255, 255, 255).toLinearSpace();
    toolBodyMaterial.subSurface.minimumThickness = 0;
    toolBodyMaterial.subSurface.maximumThickness = 1;
    toolBodyMaterial.subSurface.isTintEnabled = false;
    toolBodyMaterial.subSurface.isRefractionEnabled = false;
    toolBodyMaterial.subSurface.isTranslucencyEnabled = false;


  }

   gotoSpecificProduct = (prod) => {
    var product1 = this.scene.getMeshByName("product1");
    var product2 = this.scene.getMeshByName("product2");
    var product3 = this.scene.getMeshByName("product3");
    var product1small = this.scene.getMeshByName("product1small");
    var tool = this.scene.getMeshByName("tool");
    var box1 = this.scene.getMeshByName("box");
    var box2 = this.scene.getMeshByName("box2");
    var box3 = this.scene.getMeshByName("box3");
    var cover1small = this.scene.getMeshByName("cover1small");
    var ring = this.scene.getMaterialByName("Ring");
    var chain = this.scene.getMaterialByName("Chains");
    var liquid1small = this.scene.getMeshByName("liquid1small");
    var productBody1small = this.scene.getMaterialByName("productBody1small");

    this.camera.detachControl(this.stage, true);
    box1.isPickable = false;
    box2.isPickable = false;
    box3.isPickable = false;
    this.props.ArrowsDisplayFunction("on");
    if (prod == "1") {
      this.props.ProductDetailsChanger("I");
      this.setState({ currentModel: "body1" });
      this.props.updateCurrentModel("body1");
      TweenMax.to(product1.position, 0.75, {
        x: 0.127,
        y: 1.65,
        z: 0.21,
      });
      TweenMax.to(product2.position, 0.75, {
        x: this.state.positionLeft.x,
        y: this.state.positionLeft.y,
        z: this.state.positionLeft.z,
      });
      TweenMax.to(product3.position, 0.75, {
        x: this.state.positionRight.x,
        y: this.state.positionRight.y,
        z: this.state.positionRight.z,
      });
      //small bottle and tool
      productBody1small.emissiveTexture = new Texture(
        "5mm_He_Refraction_V2.jpg",
        this.scene,
        false,
        false
      );
      cover1small.material.albedoTexture = new Texture(
        "5mm_He_Cover.jpg",
        this.scene,
        false,
        false
      );
      cover1small.material.albedoColor = new Color3.FromInts(
        35,
        35,
        35
      ).toLinearSpace();
      ring.albedoTexture = new Texture(
        "5mm_He_Cover.jpg",
        this.scene,
        false,
        false
      );
      ring.albedoColor = new Color3.FromInts(35, 35, 35).toLinearSpace();
      // chain.albedoTexture = new Texture("5mm_He_Cover.jpg", this.scene, false, false);
      chain.albedoColor = new Color3.FromInts(35, 35, 35).toLinearSpace();
      liquid1small.material.clearCoat.tintColor = new Color3.FromInts(
        206,
        167,
        107
      ).toLinearSpace();
      liquid1small.material.clearCoat.tintColorAtDistance = 4;
      liquid1small.material.clearCoat.tintThickness = 3;
      this.scene.getMeshByName("product1small").setEnabled(true);
      tool.setEnabled(true);
      TweenMax.to(product1small.position, 0.75, {
        x: -0.173,
        y: 0.502,
        z: 0.179,
      });
      TweenMax.to(tool.position, 0.75, {
        x: 0.474,
        y: 0.4,
        z: 0.325,
      });
    } else if (prod == "2") {
      this.props.ProductDetailsChanger("YOU");
      this.setState({ currentModel: "body2" });
      this.props.updateCurrentModel("body2");
      TweenMax.to(product2.position, 0.75, {
        x: 0.127,
        y: 1.65,
        z: 0.21,
      });
      TweenMax.to(product1.position, 0.75, {
        x: this.state.positionRight.x,
        y: this.state.positionRight.y,
        z: this.state.positionRight.z,
      });
      TweenMax.to(product3.position, 0.75, {
        x: this.state.positionLeft.x,
        y: this.state.positionLeft.y,
        z: this.state.positionLeft.z,
      });
      //small bottle and tool
      this.scene.getMeshByName("product1small").setEnabled(true);
      productBody1small.emissiveTexture = new Texture(
        "5mm_She and They_Refraction_V2.jpg",
        this.scene,
        false,
        false
      );
      liquid1small.material.clearCoat.tintColor = new Color3.FromInts(
        234,
        231,
        218
      ).toLinearSpace();
      liquid1small.material.clearCoat.tintColorAtDistance = 2.8;
      liquid1small.material.clearCoat.tintThickness = 3;
      cover1small.material.albedoTexture = null;
      cover1small.material.albedoColor = new Color3.FromInts(
        224,
        184,
        123
      ).toLinearSpace();
      ring.albedoColor = new Color3.FromInts(224, 184, 123).toLinearSpace();
      ring.albedoTexture = null;
      chain.albedoColor = new Color3.FromInts(224, 184, 123).toLinearSpace();
      // chain.albedoTexture = null;
      tool.setEnabled(true);
      TweenMax.to(product1small.position, 0.75, {
        x: -0.173,
        y: 0.502,
        z: 0.179,
      });
      TweenMax.to(tool.position, 0.75, {
        x: 0.474,
        y: 0.4,
        z: 0.325,
      });
    } else if (prod == "3") {
      this.props.ProductDetailsChanger("WE");
      this.setState({ currentModel: "body3" });
      this.props.updateCurrentModel("body3");
      TweenMax.to(product3.position, 0.75, {
        x: 0.127,
        y: 1.65,
        z: 0.21,
      });
      TweenMax.to(product2.position, 0.75, {
        x: this.state.positionRight.x,
        y: this.state.positionRight.y,
        z: this.state.positionRight.z,
      });
      TweenMax.to(product1.position, 0.75, {
        x: this.state.positionLeft.x,
        y: this.state.positionLeft.y,
        z: this.state.positionLeft.z,
      });
      //small bottle and tool
      this.scene.getMeshByName("product1small").setEnabled(true);
      productBody1small.emissiveTexture = new Texture(
        "5mm_She and They_Refraction_V2.jpg",
        this.scene,
        false,
        false
      );
      cover1small.material.albedoTexture = new Texture(
        "5mm_They_Cover.jpg",
        this.scene,
        false,
        false
      );
      cover1small.material.albedoColor = new Color3.FromInts(
        255,
        212,
        190
      ).toLinearSpace();
      ring.albedoTexture = new Texture(
        "5mm_They_Cover.jpg",
        this.scene,
        false,
        false
      );
      ring.albedoColor = new Color3.FromInts(255, 212, 190).toLinearSpace();
      // chain.albedoTexture = new Texture("5mm_They_Cover.jpg", this.scene, false, false);
      chain.albedoColor = new Color3.FromInts(150, 84, 42).toLinearSpace();
      liquid1small.material.clearCoat.tintColor = new Color3.FromInts(
        238,
        219,
        188
      ).toLinearSpace();
      liquid1small.material.clearCoat.tintColorAtDistance = 0.65;
      liquid1small.material.clearCoat.tintThickness = 1;
      tool.setEnabled(true);
      TweenMax.to(product1small.position, 0.75, {
        x: -0.173,
        y: 0.502,
        z: 0.179,
      });
      TweenMax.to(tool.position, 0.75, {
        x: 0.474,
        y: 0.4,
        z: 0.325,
      });
    }
    TweenMax.to(this.camera, 1.5, {
      radius: 1.55,
      alpha: 1.7654,
      beta: 1.5773,
      lowerRadiusLimit: 1.55,
      upperRadiusLimit: 1.55,
    });
    TweenMax.to(this.camera.target, 0.75, {
      x: 0.124,
      z: 0.2,
    });
    this.camera.upperAlphaLimit = 2.7;
    this.camera.lowerAlphaLimit = 0.82;
    this.camera.lowerBetaLimit = 0.65;
    this.camera.upperBetaLimit = 2.05;
    this.camera.minZ = 0.01;

    setTimeout(() => {
      this.setState({ rotatable: true, rotating: false });
      setTimeout(() => {
        this.camera.lowerAlphaLimit = 1.66;
        this.camera.upperAlphaLimit = 1.86;
        this.camera.lowerBetaLimit = 1.47;
        this.camera.upperBetaLimit = 1.67;
      }, 510);
    }, 1000);
    setTimeout(() => {
      this.props.ProductDisplayFunction();
    }, 500);

    this.setState({ scenenow: "thirdphase" });
  };

  turnToSpecificProduct = (prod, counter) => {
    var product1 = this.scene.getMeshByName("product1");
    var product2 = this.scene.getMeshByName("product2");
    var product3 = this.scene.getMeshByName("product3");
    var product1small = this.scene.getMeshByName("product1small");
    var tool = this.scene.getMeshByName("tool");
    product1.rotation = new Vector3(
      Tools.ToRadians(-90),
      Tools.ToRadians(350.8),
      Tools.ToRadians(0)
    );
    // product2.rotation = new Vector3(Tools.ToRadians(36.0660), Tools.ToRadians(-104.3192), Tools.ToRadians(83.2523));
    product2.rotation = new Vector3(
      Tools.ToRadians(-90),
      Tools.ToRadians(350.8),
      Tools.ToRadians(0)
    );
    product3.rotation = new Vector3(
      Tools.ToRadians(-90),
      Tools.ToRadians(350.8),
      Tools.ToRadians(0)
    );
    product1small.rotation = new Vector3(
      Tools.ToRadians(175),
      Tools.ToRadians(260),
      Tools.ToRadians(90)
    );
    tool.rotation = new Vector3(Tools.ToRadians(-74.0407), Tools.ToRadians(-56.3869), Tools.ToRadians(-132.6720));

    var product;
    if (counter == 1) {
      product = product1;
      this.setState({ currentModel: "body1" });
      this.props.updateCurrentModel("body1");
    } else if (counter == 0) {
      product = product2;
      this.setState({ currentModel: "body2" });
      this.props.updateCurrentModel("body2");
    } else if (counter == 2) {
      product = product3;
      this.setState({ currentModel: "body3" });
      this.props.updateCurrentModel("body3");
    }

    if (prod != "tool") {
      if (this.state.currentModel == "body1") {
        this.props.ProductDetailsChanger("I");
      } else if (this.state.currentModel == "body2") {
        this.props.ProductDetailsChanger("YOU");
      } else if (this.state.currentModel == "body3") {
        this.props.ProductDetailsChanger("WE");
      }
    }

    if (prod == "tool") {
      this.setState({ currentModel: "toolbody" });
      if (this.state.toolCurrentColor === "Obsidian") {
        this.props.updateCurrentModel("toolObsidian");
        this.props.ProductDetailsChanger("toolObsidian");
      } else if (this.state.toolCurrentColor === "Opal"){
        this.props.updateCurrentModel("toolOpal");
        this.props.ProductDetailsChanger("toolOpal");
      } else if (this.state.toolCurrentColor === "Tiger"){
        this.props.updateCurrentModel("toolTiger");
        this.props.ProductDetailsChanger("toolTiger");
      }
      TweenMax.to(tool.position, 1, {
        x: 0.127,
        y: 1.65,
        z: 0.281,
      });
      TweenMax.to(product.position, 1, {
        x: -0.175,
        y: 0.2,
        z: 0.247,
      });
      TweenMax.to(product1small.position, 1, {
        x: 0.423,
        y: 0.499,
        z: 0.366,
      });
    } else if (prod == "smallBottle") {
      this.setState({ currentModel: "body1small" });
      this.props.updateCurrentModel("bodySmall");
      TweenMax.to(product1small.position, 1, {
        x: 0.119,
        y: 1.65,
        z: 0.257,
      });
      TweenMax.to(tool.position, 1, {
        x: -0.124,
        y: 0.3,
        z: 0.256,
      });
      TweenMax.to(product.position, 1, {
        x: 0.464,
        y: 0.2,
        z: 0.345,
      });
    } else if (prod == "bottle") {
      TweenMax.to(product.position, 1, {
        x: 0.127,
        y: 1.65,
        z: 0.21,
      });
      TweenMax.to(product1small.position, 1, {
        x: -0.173,
        y: 0.502,
        z: 0.179,
      });
      TweenMax.to(tool.position, 1, {
        x: 0.474,
        y: 0.4,
        z: 0.325,
      });
    }
  };

  goToProductFromInside = (e) => {
    this.props.startdisplayOfffunction();
    if (this.state.scenenow === "heart"){
      this.Heartback();
    } else if (this.state.scenenow === "contact") {
      this.camera.moveTargetTo(new Vector3(0.069, 1.65, 0.48), 200);
      this.camera.spinTo("alpha", 1.7453, 200);
      this.camera.spinTo("beta", 1.5711, 200);
      this.camera.spinTo("radius", 3.75, 200);
      setTimeout(() => {
        document.getElementsByClassName("App")[0].style.cursor = "grab";
        this.camera.lowerRadiusLimit = 3.75;
        this.camera.upperRadiusLimit = 3.75;
        this.camera.upperAlphaLimit = 2.0;
        this.camera.lowerAlphaLimit = 1.49;
        this.camera.lowerBetaLimit = 1.4;
        this.camera.upperBetaLimit = 1.74;
        this.setState({
          scenenow: "secondphase",
        });
        this.camera.attachControl(this.stage, true);
      }, 500);
    }
    var BodyMaterial = this.scene.getMaterialByName("BodyMaterial");
    setTimeout(() => {
      if(e.detail == "I Big"){
        this.gotoSpecificProduct("1");
        counter = 1;
      }
      if(e.detail == "You Big"){
        this.gotoSpecificProduct("2");
        counter = 0;
      }
      if(e.detail == "We Big"){
        this.gotoSpecificProduct("3");
        counter = 2;
      }
      if(e.detail == "I Small"){
        this.gotoSpecificProduct("1");
        counter = 1;
        setTimeout(() => {
          this.turnToSpecificProduct("smallBottle", counter);
        }, 2000);
      }
      if(e.detail == "You Small"){
        this.gotoSpecificProduct("2");
        counter = 0;
        setTimeout(() => {
          this.turnToSpecificProduct("smallBottle", counter);
        }, 2000);
      }
      if(e.detail == "We Small"){
        this.gotoSpecificProduct("3");
        counter = 2;
        setTimeout(() => {
          this.turnToSpecificProduct("smallBottle", counter);
        }, 2000);
      }
      if(e.detail == "Tool Obsidian"){
        this.gotoSpecificProduct("1");
        counter = 1;
        setTimeout(() => {
          this.turnToSpecificProduct("tool", counter);
        }, 2000);
      }
      if(e.detail == "Tool Opal"){
        this.customiseOpalInBackground();
        this.gotoSpecificProduct("2");
        counter = 0;
        setTimeout(() => {
          this.turnToSpecificProduct("tool", counter);
        }, 2000);
      }
      if(e.detail == "Tool Tiger"){
        this.customiseTiger();
        this.gotoSpecificProduct("3");
        counter = 2;
        setTimeout(() => {
          this.turnToSpecificProduct("tool", counter);
        }, 2000);
      }
      TweenMax.to(BodyMaterial, 1, {
        alpha: 0.25,
      });
      document.getElementsByClassName("INCORPbottomend")[0].style.cursor = "grab";
    }, 1000);
  }

  goToProductFromOutside = (e) => {
    this.props.startdisplayOfffunction();
    this.Startanimation()
    var BodyMaterial = this.scene.getMaterialByName("BodyMaterial");
    setTimeout(() => {
      if(e.detail == "I Big"){
        this.gotoSpecificProduct("1");
        counter = 1;
      }
      if(e.detail == "You Big"){
        this.gotoSpecificProduct("2");
        counter = 0;
      }
      if(e.detail == "We Big"){
        this.gotoSpecificProduct("3");
        counter = 2;
      }
      if(e.detail == "I Small"){
        this.gotoSpecificProduct("1");
        counter = 1;
        setTimeout(() => {
          this.turnToSpecificProduct("smallBottle", counter);
        }, 2000);
      }
      if(e.detail == "You Small"){
        this.gotoSpecificProduct("2");
        counter = 0;
        setTimeout(() => {
          this.turnToSpecificProduct("smallBottle", counter);
        }, 2000);
      }
      if(e.detail == "We Small"){
        this.gotoSpecificProduct("3");
        counter = 2;
        setTimeout(() => {
          this.turnToSpecificProduct("smallBottle", counter);
        }, 2000);
      }
      if(e.detail == "Tool Obsidian"){
        this.gotoSpecificProduct("1");
        counter = 1;
        setTimeout(() => {
          this.turnToSpecificProduct("tool", counter);
        }, 2000);
      }
      if(e.detail == "Tool Opal"){
        this.customiseOpalInBackground();
        this.gotoSpecificProduct("2");
        counter = 0;
        setTimeout(() => {
          this.turnToSpecificProduct("tool", counter);
        }, 2000);
      }
      if(e.detail == "Tool Tiger"){
        this.customiseTiger();
        this.gotoSpecificProduct("3");
        counter = 2;
        setTimeout(() => {
          this.turnToSpecificProduct("tool", counter);
        }, 2000);
      }
      TweenMax.to(BodyMaterial, 1, {
        alpha: 0.25,
      });
      document.getElementsByClassName("INCORPbottomend")[0].style.cursor = "grab";
    }, 8000);
  }

  //Build the scene when the component has been loaded.
  componentDidMount() {
    this.setEngine();
    this.setScene();
    this.setCamera();
    this.loadModels();
    window.addEventListener("resize", this.onResizeWindow);
    window.addEventListener("Startanimation", this.Startanimation);
    window.addEventListener("Aboutanimation", this.Aboutanimation);
    window.addEventListener("Shopanimation", this.Shopanimation);
    window.addEventListener("Homeanimation", this.Homeanimation);
    window.addEventListener("Productsanimation", this.Productsanimation);
    window.addEventListener("Contactanimation", this.Contactanimation);
    window.addEventListener("Heartback", this.Heartback);
    window.addEventListener("turnoffannotations", this.TurnOffAnnotations);
    window.addEventListener("annotationToggle", this.annotationToggle);
    window.addEventListener("customiseObsidian", this.customiseObsidian)
    window.addEventListener("customiseOpal", this.customiseOpal)
    window.addEventListener("customiseOpalInBackground", this.customiseOpalInBackground)
    window.addEventListener("customiseTiger", this.customiseTiger)
    window.addEventListener("goToProductFromInside", this.goToProductFromInside)
    window.addEventListener("goToProductFromOutside", this.goToProductFromOutside)
  }
  //Renderes our Canvas tag and saves a reference to it.
  render() {
    return (
      <div>
        {" "}
        <canvas className="canvas3d" ref={(el) => (this.stage = el)}></canvas>
        <div style={{ display: this.props.ArrowsDisplay }}>
          <div
            className="ArrowHelper btnleft"
            onClick={() => this.bottlesetbesidecameraLeft(counter - 1)}></div>
          <div
            className="ArrowHelper btnright"
            onClick={() => this.bottlesetbesidecameraRight(counter + 1)}></div>
        </div>
      </div>
    );
  }
}
//returns the scene to be used by other components
export default Scene3;
