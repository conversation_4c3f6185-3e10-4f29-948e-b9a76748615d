.AboutPage {
  position: absolute;
  /* height: 80%; */
  height: 85%;
  width: 98%;
  left: 2%;
  margin-top: 3.5%;
  z-index: 999;
  background-color: transparent;
}

/**
 * ----------------------------------------
 * animation scale-up-center
 * ----------------------------------------
 */
@-webkit-keyframes scale-up-center {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
  }

  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes scale-up-center {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
  }

  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

/* ----------------------------------------------
 * Generated by Animista on 2022-6-12 2:12:57
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation scale-up-hor-left
 * ----------------------------------------
 */
@-webkit-keyframes scale-up-hor-left {
  0% {
    -webkit-transform: scaleX(0.4);
    transform: scaleX(0.4);
    -webkit-transform-origin: 0% 0%;
    transform-origin: 0% 0%;
  }

  100% {
    -webkit-transform: scaleX(1);
    transform: scaleX(1);
    -webkit-transform-origin: 0% 0%;
    transform-origin: 0% 0%;
  }
}

@keyframes scale-up-hor-left {
  0% {
    -webkit-transform: scaleX(0.4);
    transform: scaleX(0.4);
    -webkit-transform-origin: 0% 0%;
    transform-origin: 0% 0%;
  }

  100% {
    -webkit-transform: scaleX(1);
    transform: scaleX(1);
    -webkit-transform-origin: 0% 0%;
    transform-origin: 0% 0%;
  }
}

.aboutcard {
  width: 29%;
  /* min-height: 50%; */
  float: left;
  background-color: #fff;
  margin-top: 2%;
  padding: 1.2%;
  max-width: 310px;
  -webkit-animation: scale-up-hor-left 0.5s cubic-bezier(0.39, 0.575, 0.565, 1) both;
  animation: scale-up-hor-left 0.5s cubic-bezier(0.39, 0.575, 0.565, 1) both;
}

.aboutcardtitle {
  width: 100%;
  height: 10%;
  margin-top: 0%;
  font-weight: normal;
  text-align: left;
  font-size: 1.2em;
  padding-bottom: 3%;
  margin-left: auto;
  margin-right: auto;
  border-bottom: 0.5px solid black;
}

.closeyABOUT {
  position: relative;
  cursor: pointer;
  float: right;
  right: 0;
  /* margin-right: 3%;
  top: 0; */
  /* margin-top: 0%; */
  margin-top: -1.5%;
  width: 25px;
  height: 25px;
  padding: 0%;
  outline: none;
  background-color: #f5f5f5;
  border: 0;
  border-radius: 22px;
}

.closeyABOUT img {
  width: 60%;
  border-radius: 50px;
}

.aboutcarddescription {
  text-align: left;
  margin-top: 3%;
  font-size: 0.8em;
  line-height: 1.25;
  height: auto;
  overflow-y: hidden;
  overflow-x: hidden;
}

.AboutPageImages {
  position: relative;
  height: 80%;
  width: 50%;
  float: right;
  margin-right: 15%;
  margin-top: 4%;
  z-index: 999;
  background-color: transparent;
  border-radius: 22px;
  animation: scale-up-center 0.4s cubic-bezier(0.39, 0.575, 0.565, 1) both;
}

.AboutPage h4 {
  width: 20%;
  display: inline;
  font-size: 1.7em !important;
  background-color: #fff;
  padding: 0%;
  margin: 0 !important;
}

.AboutPage p {
  width: 90%;
  text-align: center;
  border-radius: 10px;
  padding: 0.5%;
  margin-left: auto;
  margin-right: auto;
  /* background-color: rgba(222, 222, 222, 0.1); */
  background-color: #fff;
  font-size: 16px;
  margin: 1em;
}

.AboutPageContent {
  position: relative;
  width: 70%;
  height: auto;
  margin-top: 0%;
  margin-left: auto;
  margin-right: auto;
}

.close {
  position: relative;
  cursor: pointer;
  float: right;
  margin-right: 1%;
  margin-top: 1%;
  width: 3%;
  padding: 0%;
  outline: none;
  background-color: white;
  border: 0;
  border-radius: 22px;
}

.close img {
  width: 80%;
  border-radius: 50px;
}

.backBUtton {
  position: relative;
  cursor: pointer;
  float: left;
  margin-left: 1%;
  margin-top: 1%;
  width: 3%;
  padding: 0%;
  outline: none;
  background-color: transparent;
  border: 0;
  border-radius: 22px;
}

.backBUtton img {
  width: 80%;
  border-radius: 50px;
}

.members {
  width: 30%;
  text-align: center;
  position: absolute;
  cursor: pointer;
  /* z-index: -9999999999999 !important; */
  /* animation: float 30s ease-in infinite; */
}

@keyframes float {
  0% {
    /* box-shadow: 0 5px 15px 0px rgba(0,0,0,0.6); */
    transform: translatey(0px);
    transform: translatex(-10px);
    transform: scale(90%);
  }

  50% {
    /* box-shadow: 0 25px 15px 0px rgba(0,0,0,0.2); */

    transform: translatex(-90px);
    transform: translatey(-20px);
    transform: scale(96%);
  }

  80% {
    /* box-shadow: 0 5px 15px 0px rgba(0,0,0,0.6); */
    transform: translatey(-10px);
    transform: translatex(-30px);
    transform: scale(97%);
  }

  90% {
    /* box-shadow: 0 5px 15px 0px rgba(0,0,0,0.6); */
    transform: translatey(-5px);
    transform: translatex(-10px);
    transform: scale(98%);
  }

  98% {
    /* box-shadow: 0 5px 15px 0px rgba(0,0,0,0.6); */
    transform: translatey(-2px);
    transform: translatex(-5px);
    transform: scale(99%);
  }

  99% {
    /* box-shadow: 0 5px 15px 0px rgba(0,0,0,0.6); */
    transform: translatey(-1px);
    transform: translatex(-2px);
    transform: scale(99%);
  }

  100% {
    /* box-shadow: 0 5px 15px 0px rgba(0,0,0,0.6); */
    transform: translatey(0px);
    transform: translatex(0px);
    transform: scale(100%);
  }
}

@keyframes float1 {
  0% {
    /* box-shadow: 0 5px 15px 0px rgba(0,0,0,0.6); */
    transform: translatey(0px);
    transform: translatex(10px);
  }

  50% {
    /* box-shadow: 0 25px 15px 0px rgba(0,0,0,0.2); */

    transform: translatex(90px);
    transform: translatey(-20px);
  }

  80% {
    /* box-shadow: 0 5px 15px 0px rgba(0,0,0,0.6); */
    transform: translatey(-10px);
    transform: translatex(30px);
  }

  90% {
    /* box-shadow: 0 5px 15px 0px rgba(0,0,0,0.6); */
    transform: translatey(-5px);
    transform: translatex(10px);
  }

  98% {
    /* box-shadow: 0 5px 15px 0px rgba(0,0,0,0.6); */
    transform: translatey(-2px);
    transform: translatex(5px);
  }

  99% {
    /* box-shadow: 0 5px 15px 0px rgba(0,0,0,0.6); */
    transform: translatey(-1px);
    transform: translatex(2px);
  }

  100% {
    /* box-shadow: 0 5px 15px 0px rgba(0,0,0,0.6); */
    transform: translatey(0px);
    transform: translatex(0px);
  }
}

.XcontainerA {
  position: absolute;
  width: auto;
  text-align: right;
  right: 12px;
  top: 12px;
  z-index: 9999999999999999999 !important;
}

.CloseContactA {
  width: 20px;
  height: 20px;
  position: relative;
  right: 0;
  background-color: #f5f5f5;
  border-radius: 50%;
  border: none;
  padding: 10%;
  cursor: pointer;
  margin-top: -8px;
}

.CloseContactA img {
  width: 60% !important;
}

.members:hover {
  /* animation: float 4s ease-in-out infinite; */
}

.members img {
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
  width: 96%;
  height: auto;
  margin-left: auto;
  margin-right: auto;
  margin-top: auto;
  margin-bottom: auto;
  /* -webkit-box-shadow: 10px 11px 10px 0px rgba(0, 0, 0, 0.21);
  box-shadow: 10px 11px 10px 0px rgba(0, 0, 0, 0.21); */
}

.members.first {
  width: 50%;
  margin-top: -10%;
  margin-left: 6%;
}

.members.second {
  width: 38%;
  margin-top: 63%;
  margin-left: 37%;
}

.members.third {
  width: 50%;
  margin-top: -8%;
  margin-left: 60%;
  z-index: 99;
  /* animation: float1 40s ease-in infinite !important; */
}

.members.fourth {
  width: 38%;
  margin-top: 30%;
  margin-left: 15%;
  z-index: 10;
}

.members.fifth {
  width: 38%;
  margin-top: 22%;
  margin-left: 55%;
  /* animation: float1 40s ease-in infinite !important; */
}

.members.sixth {
  width: 33%;
  margin-top: 43%;
  margin-left: 60%;
}

.members.seventh {
  width: 44%;
  margin-top: 27%;
  margin-left: 93%;
  /* animation: float1 40s ease-in infinite !important; */
}

.members.eighth {
  width: 29%;
  margin-top: 57%;
  margin-left: -10%;
}

.members.ninth {
  width: 40%;
  margin-top: 8%;
  margin-left: -20%;
  /* animation: float1 40s ease-in infinite !important; */
}

.members.tinth {
  width: 38%;
  margin-top: 33%;
  margin-left: -29%;
  /* animation: float1 40s ease-in infinite !important; */
}

.members.elevnth {
  width: 40%;
  margin-top: 62%;
  margin-left: 90%;
}

.members.twelve {
  width: 34%;
  margin-top: 69%;
  margin-left: -40%;
}

.members.thirteen {
  width: 34%;
  margin-top: 76%;
  margin-left: 4%;
}

.members.fourteen {
  width: 34%;
  margin-top: 84%;
  margin-left: 57%;
}

.members.fifteen {
  width: 34%;
  margin-top: 96%;
  margin-left: 27%;
}

.members span {
  display: block;
  width: 120%;
  position: absolute;
  color: black;
  text-align: left;
  /* -webkit-box-shadow: 0px 0px 9px 0px rgba(0, 0, 0, 0.9);
  box-shadow: 0px 0px 9px 0px rgba(0, 0, 0, 0.9); */
  padding: 8%;
  font-size: 0.8vw;
  background-color: #ffffff;
  z-index: 99999999999999 !important;
  line-height: 1.25;
  /* animation: scale-up-center 0.2s cubic-bezier(0.39, 0.575, 0.565, 1) both; */
  animation: scale-up-ver-center 0.5s cubic-bezier(0.39, 0.575, 0.565, 1) both;
  transform-origin: left top;
}

.members span a {
  /* color: #0000EE; */
  color: #000000;
}

.Nametitle {
  border-bottom: 0.5px solid #000000;
  text-align: left;
  margin-bottom: 1vh;
  font-size: 1.2em;
  font-weight: normal;
  padding-bottom: 1%;
}

.members:hover span {
  display: block;
}

.members h5 {
  position: absolute;
  /* margin-top: -52%;
  margin-left: 4%; */
  /* text-transform: uppercase; */
  left: 7px;
  bottom: calc(100% - 15px);
  margin: 0%;
  font-size: 0.8vw;
  font-style: italic !important;
}

.boxShadow {
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
  cursor: grab;
}

.TVButton {
  position: absolute;
  width: 20%;
  height: 10%;
  bottom: 0;
  margin-bottom: -8%;
  background-color: #050505;
  color: white;
}

#topy {
  width: 150%;
  margin-top: -40%;
  margin-left: -170%;
}

#topyvan {
  width: 150%;
  margin-top: -59%;
  margin-left: -200%;
}

#lefty {
  width: 140%;
  margin-left: -5%;
  margin-top: -230%;
}

#middly {
  margin-left: -40%;
}

#topyy {
  margin-top: -130%;
  margin-left: -20%;
}

#topyy1 {
  margin-top: -150%;
  margin-left: 100%;
}

#modycx {
  margin-top: -100%;
  margin-left: -140%;
}

#modycxau {
  margin-top: -100%;
  margin-left: 100%;
}

#modycxkai{
  margin-top: -100%;
  margin-left: 100%;
}

#ohlmanspan {
  margin-top: -126%;
  margin-left: -155%;
}

#torsospan {
  margin-top: -145%;
  margin-left: 130%;
}

#custo {
  margin-top: -100%;
  margin-left: 80%;
}

#sam {
  margin-top: -60%;
}

#emmun {
  margin-top: -70%;
}

#ugo {
  margin-left: 5%;
  margin-top: -57%;
}

#kai {
  margin-left: 5%;
  margin-top: -69%;
}

#ohl {
  margin-left: 5%;
  margin-top: -61%;
}

#pol {
  /* right: 0;
  margin-right: 6%; */
  margin-top: -61%;
}

#jr {
  margin-left: 5%;
  margin-top: -53%;
}

#siss {
  margin-top: -53%;
}

#topyres {
  width: 150%;
  margin-top: -185%;
  margin-left: 75%;
}

#van {
  margin-top: -53%;
}

#mich {
  margin-top: -70%;
}

#marco {
  margin-top: -70%;
}

#pan {
  margin-top: -56%;
}

#modular {
  margin-top: -67%;
}

#Aurélien {
  margin-top: -70%;
}

@media only screen and (max-width: 600px) {
  .members h5 {
    position: absolute;
    margin-top: -50%;
    margin-left: 4%;
    text-transform: uppercase;
    font-size: 0.4em;
    font-style: italic !important;
    white-space: nowrap;
  }

  .members.first {
    width: 50%;
    margin-top: -8%;
    margin-left: -6%;
  }

  .members.second {
    width: 45%;
    margin-top: 124%;
    margin-left: 23%;
  }

  .members.third {
    width: 61%;
    margin-top: -8%;
    margin-left: 60%;
    z-index: 99;
    /* animation: float1 40s ease-in infinite !important; */
  }

  .members.fourth {
    width: 49%;
    margin-top: 52%;
    margin-left: 24%;
    z-index: 10;
  }

  .members.fifth {
    width: 45%;
    margin-top: 26%;
    margin-left: 44%;
    /* animation: float1 40s ease-in infinite !important; */
  }

  element.style {
    left: 0px;
    top: 0px;
  }

  .members.sixth {
    width: 57%;
    margin-top: 86%;
    margin-left: 55%;
  }

  .members.seventh {
    width: 42%;
    margin-top: 50%;
    margin-left: 85%;
    /* animation: float1 40s ease-in infinite !important; */
  }

  element.style {
    left: 0px;
    top: 0px;
  }

  .members.eighth {
    width: 50%;
    margin-top: 91%;
    margin-left: -8%;
  }

  .members.ninth {
    width: 59%;
    margin-top: 23%;
    margin-left: -27%;
    /* animation: float1 40s ease-in infinite !important; */
  }

  .members.tinth {
    width: 52%;
    margin-top: 58%;
    margin-left: -27%;
    /* animation: float1 40s ease-in infinite !important; */
  }

  .members.elevnth {
    width: 44%;
    margin-top: 118%;
    margin-left: 73%;
  }

  .members.twelve {
    width: 34%;
    margin-top: 120%;
    margin-left: -14%;
  }

  .aboutcard {
    width: 90%;
    min-height: 30%;
    float: none;
    margin-left: 3%;
    background-color: #fff;
    margin-top: 8%;
    padding: 2%;
    -webkit-animation: scale-up-hor-left 0.5s cubic-bezier(0.39, 0.575, 0.565, 1) both;
    animation: scale-up-hor-left 0.5s cubic-bezier(0.39, 0.575, 0.565, 1) both;
  }

  .aboutcardtitle {
    width: 100%;
    height: 10%;
    margin-top: 2%;
    font-weight: normal;
    text-align: left;
    font-size: 1.2em;
    padding-bottom: 4%;
    margin-left: auto;
    margin-right: auto;
    border-bottom: 0.5px solid black;
  }

  .closeyABOUT {
    position: relative;
    cursor: pointer;
    float: right;
    right: 0;
    /* margin-right: 3%;
    top: 0; */
    margin-top: 0%;
    width: 25px;
    height: 25px;
    padding: 0%;
    outline: none;
    background-color: #f5f5f5;
    border: 0;
    border-radius: 22px;
  }

  .closeyABOUT img {
    width: 60%;
    border-radius: 50px;
  }

  .aboutcarddescription {
    text-align: left;
    margin-top: 4%;
    /* font-size: 3.8vw;
    line-height: 1.25; */
    font-size: 0.8em;
    overflow-y: scroll;
    overflow-x: hidden;
    height: 25vh;
  }

  .AboutPageImages {
    position: relative;
    height: 50%;
    width: 94%;
    float: none;
    margin-left: 2.5%;
    margin-top: 8%;
    z-index: 999;
    background-color: transparent;
    border-radius: 22px;
    animation: scale-up-center 0.4s cubic-bezier(0.39, 0.575, 0.565, 1) both;
  }

  .members span {
    font-size: 0.48em;
  }

  #kkira {
    margin-top: 1%;
    margin-left: -50%;
  }

  #topy {
    width: 150%;
    margin-top: -94%;
    margin-left: -155%;
  }

  #lefty {
    width: 140%;
    margin-left: -58%;
    margin-top: -192%;
  }

  #topyres {
    width: 150%;
    margin-top: -224%;
    margin-left: -34%;
  }

  #modycxau {
    margin-top: -118%;
    margin-left: 104%;
  }

  #modycx {
    margin-top: -130%;
    margin-left: -140%;
  }
}