# Project Structure

## Root Directory

```
├── public/                 # Static assets and 3D models
├── src/                   # Source code
├── .github/               # CI/CD workflows and GitHub configuration
├── .kiro/                 # Kiro AI assistant configuration
├── .netlify/              # Netlify deployment configuration
├── package.json           # Dependencies and scripts
└── README.md              # CI/CD pipeline documentation
```

## Source Code Organization (`src/`)

```
src/
├── App.js                 # Main application component with state management
├── App.css                # Global styles and custom fonts
├── index.js               # React app entry point with Apollo provider
├── checkout.js            # Shopify checkout utilities and mutations
├── Components/            # React components
│   ├── Scene3d.js         # Desktop 3D scene with Babylon.js
│   ├── Scene3dMobile.js   # Mobile-optimized 3D scene
│   ├── ControlPanel.js    # UI controls for 3D interaction
│   ├── Cart.js            # Shopping cart component
│   ├── Product.js         # Product display component
│   ├── ProductDetail.js   # Detailed product information
│   ├── Customization.js   # Product customization interface
│   ├── CustomerAuth.js    # User authentication
│   └── GolobalUI/         # Global UI components
├── Fonts/                 # Custom font files (Caslon family)
└── Images/                # UI images and icons
```

## Public Assets (`public/`)

- **3D Models**: `.glb` files for bottles, tools, and animations
- **Textures**: `.jpg` image files for 3D material mapping
- **Audio**: `.mp3` files for product narration
- **UI Assets**: Icons, logos, and interface graphics
- **Videos**: Product demonstration videos
- **HDR**: Environment maps for 3D lighting

## Key Architecture Patterns

### Component Hierarchy

- **App.js**: Central state management with hooks
- **Scene Components**: Separate desktop/mobile 3D implementations
- **UI Components**: Modular, reusable interface elements
- **E-commerce Components**: Shopify integration layer

### State Management

- React hooks for local component state
- Apollo Client for GraphQL data management
- Custom events for 3D scene communication
- Checkout state managed through mutations

### Responsive Design

- Separate 3D scenes for desktop (`Scene3d.js`) and mobile (`Scene3dMobile.js`)
- CSS media queries for UI adaptation
- Touch/gesture support for mobile interactions

### Asset Organization

- Static assets in `public/` for direct access
- 3D models and textures co-located by feature
- Optimized file formats (`.glb` for 3D, compressed images)
