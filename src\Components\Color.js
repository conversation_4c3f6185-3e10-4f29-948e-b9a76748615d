import { GradientBlock } from "babylonjs";
import React, { Component } from "react";

class Color extends Component {
  colorHex = () => {
    if (this.props.colorName === "Black") {
      return "#1C1C1C";
    } else if (this.props.colorName === "Blue") {
      return "#2C415A";
    } else if (this.props.colorName === "Orange") {
      return "#6B4C15";
    } else if (this.props.colorName === "Silver") {
      return "#DEE3E4";
    } else if (this.props.colorName === "Red") {
      return "#6B1615";
    }
  };

  render() {
    return (
      <button
        onClick={() => this.props.colorPressed(this.props.colorName)}
        style={{
          backgroundImage:
            "linear-gradient(180deg, rgba(255,255,255,0.25) 0%, rgba(255,255,255,0) 100%)",
            backgroundColor: this.colorHex()
        }}
        /*style={{ backgroundImage: "url(" + this.colorImage() + ")" }}*/
        className="buttonColours"
      ></button>
    );
  }
}

export default Color;
