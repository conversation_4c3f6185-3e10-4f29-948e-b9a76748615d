################ UNIVERSAL VALUES HERE ################
#######################################################

############## NODE CREDENTIALS #######################
NODE_VERSION=18.16.0
#######################################################


############## FRONTEND VARIABLES #######################
AWS_FRONTEND_SSM_ROLE_PATH=/slaves/frontend/role-arn
AWS_S3_BUILD_BUCKET=frontend-built-websites
AWS_AMPLIFY_STAGING_APP_NAME=Staging-Web-Apps
AWS_AMPLIFY_PRODUCTION_APP_NAME=Frontend-Web-Apps
AWS_AMPLIFY_STAGING_RESOURCE_NAME=IncorpDemo
AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME=Incorp
AWS_AMPLIFY_PRODUCTION_DOMAIN=modularcx.io
AWS_AMPLIFY_PRODUCTION_SUBDOMAIN=incorp
#######################################################
