// src/App.js
import React, { useEffect } from "react";
import "./MobileShop.css";
import Cart from "../../Cart";
import { set } from "lodash";
import useState from "react-usestateref";
import MobileCart from "../MobileCart/MobileCart";
import { Media, Player, controls } from "react-media-player";
const { PlayPause, MuteUnmute, CurrentTime, Progress } = controls;

// import ""
function MobileShop(props) {
  const [MobileCartDisplay, setMobileCartDisplay] = useState("none");
  const [ibigcounter, setibigcounter, ibigcounterref] = useState(0);
  const [ismallcounter, setismallcounter, ismallcounterref] = useState(0);
  const [youbigcounter, setyoubigcounter, youbigcounterref] = useState(0);
  const [yousmallcounter, setyousmallcounterl, yousmallcounterref] = useState(0);
  const [webigcounter, setwewebigcounter, webigcounterref] = useState(0);
  const [wesmallcouner, setwesmallcounerl, wesmallcounerref] = useState(0);
  const [blackonyx, setblackonyx, blackonyxref] = useState(0);
  const [opal, setopal, opalref] = useState(0);
  const [tiger, settiger, tigerref] = useState(0);

  const [ToolOpacity, setToolOpacity] = useState(0.5);
  useEffect(() => {
    // This gets called after every render, by default
    // (the first one, and every one after that)
    if (props.totalitems > 0) {
      setToolOpacity(1);
    } else if (props.totalitems == 0) {
      setToolOpacity(0.5);
    }
    // If you want to implement componentWillUnmount,
    // return a function from here, and React will call
    // it prior to unmounting.
  });

  const addtoCart = (lineItemId, item) => {
    console.log(lineItemId, item,"sdasdas")
    if (item == 1) {
      setibigcounter(ibigcounter + 1);
    } else if (item == 2) {
      setismallcounter(ismallcounter + 1);
    } else if (item == 3) {
      setyoubigcounter(youbigcounter + 1);
    } else if (item == 4) {
      setyousmallcounterl(yousmallcounter + 1);
    } else if (item == 5) {
      setwewebigcounter(webigcounter + 1);
    } else if (item == 6) {
      setwesmallcounerl(wesmallcouner + 1);
    }
    props.addVariantToCart(lineItemId, 1);
  };

  const removetoCart = (lineItemId, item) => {
    if (item == 1 && ibigcounter > 0) {
      setibigcounter(ibigcounter - 1);
      props.updateLineItemInCart(lineItemId, ibigcounterref);
      // console.log(ibigcounterref)
    } else if (item == 2 && ismallcounter > 0) {
      setismallcounter(ismallcounter - 1);
      props.updateLineItemInCart(lineItemId, ismallcounterref);
    } else if (item == 3 && youbigcounter > 0) {
      setyoubigcounter(youbigcounter - 1);
      props.updateLineItemInCart(lineItemId, youbigcounterref);
    } else if (item == 4 && yousmallcounter > 0) {
      setyousmallcounterl(yousmallcounter - 1);
      props.updateLineItemInCart(lineItemId, yousmallcounterref);
    } else if (item == 5 && webigcounter > 0) {
      setwewebigcounter(webigcounter - 1);
      props.updateLineItemInCart(lineItemId, webigcounterref);
    } else if (item == 6 && wesmallcouner > 0) {
      setwesmallcounerl(wesmallcouner - 1);
      props.updateLineItemInCart(lineItemId, wesmallcounerref);
    }
  };

  const addToolToCart = (lineItemId, Type) => {
    if (ToolOpacity > 0.6) {
      if (Type == 1) {
        setblackonyx(blackonyx + 1);
        props.addVariantToCart(lineItemId, blackonyx);
      } else if (Type == 2) {
        setopal(opal + 1);
        props.addVariantToCart(lineItemId, opal);
      } else if (Type == 3) {
        settiger(tiger + 1);
        props.addVariantToCart(lineItemId, tiger);
      }
    }
  };

  const removeTooltoCart = (lineItemId, item) => {
    if (item == 1 && blackonyx > 0) {
      setblackonyx(blackonyx - 1);
      props.updateLineItemInCart(lineItemId, blackonyxref);
      // console.log(blackonyxref)
    } else if (item == 2 && opal > 0) {
      setopal(opal - 1);
      props.updateLineItemInCart(lineItemId, opalref);
    } else if (item == 3 && tiger > 0) {
      settiger(tiger - 1);
      props.updateLineItemInCart(lineItemId, tigerref);
    }
  };
  const DisplayMobileCart = (Target) => {
    if (Target == "open") {
      setMobileCartDisplay("");
    } else if (Target == "close") {
      setMobileCartDisplay("none");
    }
  };
  return (
    <>
      <button className="MobileIncorpButton" onClick={() => props.ShopPageDisplaySwitch("close")}>
        <img src="logo.png" />
      </button>
      <div className="MobileShop">
        <div className="MobileShopPageContent">
          <div className="Mobileproduct">
            <div className="MobileProductTitle">
              <span>
                <b>I</b>
              </span>
              <p>
                I am focused.
                <br /> I am energized
              </p>
              <div style={{fontStyle: "italic"}} className="MobileProductDiscription">
                With CLEANSING Thyme I eliminate distraction while SUPPORTING with grounding Vetiver and healing Myrrh. Antioxidant Melissa vitalizes,
                GUIDING me towards my authentic inner self. <br /> <br />
              </div>
            </div>
            <div className="MobileProductImage">
              <div className="Mobiledisplayproduct">
                <img src="p3.png" />
                <div className="Mobilecountershop">
                  <button
                    className="reduce"
                    onClick={() =>
                      removetoCart(
                        "Z2lkOi8vc2hvcGlmeS9DaGVja291dExpbmVJdGVtLzQyMzg4MTU5MTM2MDAyMD9jaGVja291dD01NzZkMTQ1MGQ1ZTVjN2M1NTdmNDdkM2NkYWJlNjA5Ng==",
                        1
                      )
                    }
                  >
                    -
                  </button>
                  <div className="numbercounter">{ibigcounter}</div>
                  <button className="increase" onClick={() => addtoCart("Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE1OTEzNjAwMg==", 1)}>
                    +
                  </button>
                </div>
              </div>
              <div className="Mobiledisplayproduct">
                <img id="Mobileprettysmall" src="psmall.png" />
                <div className="Mobilecountershop">
                  <button
                    className="reduce"
                    onClick={() =>
                      removetoCart(
                        "Z2lkOi8vc2hvcGlmeS9DaGVja291dExpbmVJdGVtLzQyMzg4MDMzNDcwNzIyMD9jaGVja291dD03YWU5MjhjYjMyNWRmMzk0MTkyMjNhMGNlOWFkZTg1Ng==",
                        2
                      )
                    }
                  >
                    -
                  </button>
                  <div className="numbercounter">{ismallcounter}</div>
                  <button className="increase" onClick={() => addtoCart("Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODAzMzQ3MDcyMg==", 2)}>
                    +
                  </button>
                </div>
              </div>
              <div className="Mobiledisplayproduct">
              <div className="VoiceContainer">
                <Media>
                  <div className="media">
                      <Player src="I (vol adj & cut)- 9722, 5.48 PM.mp3" />
                      {/* <CurrentTime style={{marginRight:"5%"}}/> */}
                    <div className="media-controls">
                      <PlayPause className="PlayPuase"/>
                    </div>
                  </div>
                </Media>
              </div>
                <button className="MobileBlackCart">add to cart</button>
              </div>
            </div>
          </div>
          <div className="Mobileproduct">
            <div className="MobileProductTitle">
              <span>
                <b> YOU</b>
              </span>
              <p>
                YOU are unconditional love.
                <br /> YOU are light
              </p>
              <div style={{fontStyle: "italic"}} className="MobileProductDiscription">
                With CLEANSING Lime and Coriander; you stimulate your body and mind. You SUPPORT emotional stability with Sandalwood, opening your
                heart with Rose and GUIDING you towards divine inner love.
                <br /> <br />
              </div>
            </div>
            <div className="MobileProductImage">
              <div className="Mobiledisplayproduct">
                <img src="p1.png" />
                <div className="Mobilecountershop">
                  <button
                    className="reduce"
                    onClick={() =>
                      removetoCart(
                        "Z2lkOi8vc2hvcGlmeS9DaGVja291dExpbmVJdGVtLzQyMzcxNDg5NjYxMTg2MD9jaGVja291dD0zODMxYjAyYjM4YjQxNjdmZWFjYmY5NjBmYTIzNzAyYw==",
                        3
                      )
                    }
                  >
                    -
                  </button>
                  <div className="numbercounter">{youbigcounter}</div>
                  <button className="increase" onClick={() => addtoCart("Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM3MTQ4OTY2MTE4Ng==", 3)}>
                    +
                  </button>
                </div>
              </div>
              <div className="Mobiledisplayproduct">
                <img id="Mobileprettysmall" src="psmall.png" />
                <div className="Mobilecountershop">
                  <button
                    className="reduce"
                    onClick={() =>
                      removetoCart(
                        "Z2lkOi8vc2hvcGlmeS9DaGVja291dExpbmVJdGVtLzQyMzg4MTYwMTE5MDQyMD9jaGVja291dD0xNDVhYzYzMzY1YzI4MGZjMjEyZmVkNzA1YTNiZmUzNQ==",
                        4
                      )
                    }
                  >
                    -
                  </button>
                  <div className="numbercounter">{yousmallcounter}</div>
                  <button className="increase" onClick={() => addtoCart("Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE2MDExOTA0Mg==", 4)}>
                    +
                  </button>
                </div>
              </div>
              <div className="Mobiledisplayproduct">
              <div className="VoiceContainer">
                <Media>
                  <div className="media">
                      <Player src="You2 (vol adj & cut) - 9_7_22, 5.59 PM.mp3" />
                      {/* <CurrentTime style={{marginRight:"5%"}}/> */}
                    <div className="media-controls">
                      <PlayPause className="PlayPuase"/>
                    </div>
                  </div>
                </Media>
              </div>
                <button className="MobileBlackCart">add to cart</button>
              </div>
            </div>
          </div>
          <div className="Mobileproduct">
            <div className="MobileProductTitle">
              <span>
                <b>WE</b>
              </span>
              <p>
                WE are all encompassing.
                <br /> WE are independent
              </p>
              <div style={{fontStyle: "italic"}} className="MobileProductDiscription">
                With CLEANSING Coriander, Orange and Thyme; we ignite your creative mind and act as an aphrodisiac. We SUPPORT tranquility with
                Vetiver while Roman Chamomile GUIDES us towards inner equilibrium.
                <br />
                <br />
              </div>
            </div>
            <div className="MobileProductImage">
              <div className="Mobiledisplayproduct">
                <img src="p2.png" />
                <div className="Mobilecountershop">
                  <button
                    className="reduce"
                    onClick={() =>
                      removetoCart(
                        "Z2lkOi8vc2hvcGlmeS9DaGVja291dExpbmVJdGVtLzQyMzcxNDk1Mzk1NTg2MD9jaGVja291dD0wOWNkNGM5YWRhY2UxMDRkZDg5MmNjY2U2NzRmNjM0OA==",
                        5
                      )
                    }
                  >
                    -
                  </button>
                  <div className="numbercounter">{webigcounter}</div>
                  <button className="increase" onClick={() => addtoCart("Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM3MTQ5NTM5NTU4Ng==", 5)}>
                    +
                  </button>
                </div>
              </div>
              <div className="Mobiledisplayproduct">
                <img id="Mobileprettysmall" src="psmall.png" />
                <div className="Mobilecountershop">
                  <button
                    className="reduce"
                    onClick={() =>
                      removetoCart(
                        "Z2lkOi8vc2hvcGlmeS9DaGVja291dExpbmVJdGVtLzQyMzg4MTU5ODg5NjY2MD9jaGVja291dD0zYzBiMzU5NjcxOWY3ODQxYmNmOTU2ZTg4Zjk2NjUzYQ==",
                        6
                      )
                    }
                  >
                    -
                  </button>
                  <div className="numbercounter">{wesmallcouner}</div>
                  <button className="increase" onClick={() => addtoCart("Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE1OTg4OTY2Ng==", 6)}>
                    +
                  </button>
                </div>
              </div>
              <div className="Mobiledisplayproduct">
              <div className="VoiceContainer">
                <Media>
                  <div className="media">
                      <Player src="we (vol adj & cut)- 9722, 5.49 PM.mp3" />
                      {/* <CurrentTime style={{marginRight:"5%"}}/> */}
                    <div className="media-controls">
                      <PlayPause className="PlayPuase"/>
                    </div>
                  </div>
                </Media>
              </div>
                <button className="MobileBlackCart">add to cart</button>
              </div>
            </div>
          </div>
          <div className="Mobileproduct" style={{ border: "none" }}>
            <div className="MobileProductTitle" style={{ opacity: ToolOpacity }}>
              <span>
                <b>TOOLS</b>
              </span>
              <p>Our Tools</p>
              <div className="MobileProductDiscription" style={{ opacity: ToolOpacity }}>
                Use OPAL tool for lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna
                aliqua.
                <br />
                <br />
                The ONYX tool lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna
                aliqua.
              </div>
            </div>
            <div className="MobileProductImage" style={{ opacity: ToolOpacity }}>
              <div className="Mobiledisplayproduct">
                <img id="Mobileprettysmall" src="toolblack.png" />
                {/* <span>Onyx</span> */}
                <div className="Mobilecountershop">
                  <button className="reduce" onClick={() => removeTooltoCart("id", 1)}>
                    -
                  </button>
                  <div className="numbercounter">{blackonyx}</div>
                  <button className="increase" onClick={() => addToolToCart("id", 1)}>
                    +
                  </button>
                </div>
              </div>
              <div className="Mobiledisplayproduct">
                <img id="Mobileprettysmall" src="toolblu.png" />
                {/* <span >Opal</span> */}
                <div className="Mobilecountershop">
                  <button className="reduce" onClick={() => removeTooltoCart("id", 2)}>
                    -
                  </button>
                  <div className="numbercounter">{opal}</div>
                  <button className="increase" onClick={() => addToolToCart("id", 2)}>
                    +
                  </button>
                </div>
              </div>
              <div className="Mobiledisplayproduct">
                <button className="MobileBlackCart">add to cart</button>
              </div>
            </div>
          </div>
        </div>
        <button className="mobilecheckout" onClick={() => DisplayMobileCart("open")}>
          CHECKOUT
        </button>
      </div>
      <div className="MobileCartdiv" style={{ display: MobileCartDisplay }}>
        <MobileCart
          updateLineItemInCart={props.updateLineItemInCart}
          checkout={props.checkout}
          isCartOpen={props.isCartOpen}
          handleCartClose={props.handleCartClose}
          customerAccessToken={props.customerAccessToken}
          ShopPageDisplaySwitch={props.ShopPageDisplaySwitch}
          DisplayMobileCart={DisplayMobileCart}
        />
      </div>
    </>
  );
}

export default MobileShop;
