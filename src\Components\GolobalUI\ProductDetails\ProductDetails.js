// src/App.js
import React, { Component } from "react";
import "./ProductDetails.css";
import MobileProductSwitcher from "../MobileProductSwitcher/MobileProductSwitcher";
import { Media, Player, controls } from "react-media-player";
import CustomPlayPause from "../MediaPlayer/CustomPlayPause ";
import CustomVideoPlay from "../MediaPlayer/CustomVideoPlay";

const { PlayPause, MuteUnmute, CurrentTime, Progress } = controls;

class ProductDetails extends Component {
  state = { counterforbig: 0, counterforsmall: 0 ,counterforopal:0,counterforobsedian:0, counterfortiger:0};

  constructor(props) {
    super(props);
  }

  decreaseounteropal = () => {
    // console.log(product)
    if (this.state.counterforopal > 0) {
      this.setState({ counterforopal: this.state.counterforopal - 1 });
    }
  };
  decreaseounterobsedian= () => {
    // console.log(product)
    if (this.state.counterforobsedian > 0) {
      this.setState({ counterforobsedian: this.state.counterforobsedian - 1 });
    }
  };
  decreaseountertiger= () => {
    // console.log(product)
    if (this.state.counterfortiger > 0) {
      this.setState({ counterfortiger: this.state.counterfortiger - 1 });
    }
  };

  IncreaseCounterForopal  = () => {
    this.setState({ counterforopal: this.state.counterforopal + 1 });
  };
  IncreaseCounterForobsedian = () => {
    this.setState({ counterforobsedian: this.state.counterforobsedian + 1 });
  };
  IncreaseCounterFortiger = () => {
    this.setState({ counterfortiger: this.state.counterfortiger + 1 });
  };
  
  decreaseounterBig = () => {
    // console.log(product)
    if (this.state.counterforbig > 0) {
      this.setState({ counterforbig: this.state.counterforbig - 1 });
    }
  };
  decreaseounterForSmall = () => {
    // console.log(product)
    if (this.state.counterforsmall > 0) {
      this.setState({ counterforsmall: this.state.counterforsmall - 1 });
    }
  };



  IncreaseCounterForBigMobile = () => {
    this.setState({ counterforbig: this.state.counterforbig + 1 });
  };
  IncreaseCounterForSmallMobile = () => {
    this.setState({ counterforsmall: this.state.counterforsmall + 1 });
  };
  addToCartmobile = (counter,id) => {
    // console.log(this.props.PDITitle,this.props.idofmodelbig)
    console.log(counter,id)
    this.props.addVariantToCart(
      id,
      counter
    );
  };

  addToCart = () => {
    if (this.props.PDITitle == "I") {
      this.props.addVariantToCart(
        this.props.idofmodelbig,
        this.props.ibigcounter
      );
    } else if (this.props.PDITitle == "YOU") {
      this.props.addVariantToCart(
        this.props.idofmodelbig,
        this.props.youbigcounter
      );
    } else if (this.props.PDITitle == "WE") {
      this.props.addVariantToCart(
        this.props.idofmodelbig,
        this.props.webigcounter
      );
    }
    setTimeout(() => {
      if (this.props.PDITitle == "I") {
        this.props.addVariantToCart(
          this.props.idofmodelsmall,
          this.props.ismallcounter
        );
      } else if (this.props.PDITitle == "YOU") {
        this.props.addVariantToCart(
          this.props.idofmodelsmall,
          this.props.yousmallcounter
        );
      } else if (this.props.PDITitle == "WE") {
        this.props.addVariantToCart(
          this.props.idofmodelsmall,
          this.props.wesmallcouner
        );
      }
    }, 1000);
    // for tools later
    setTimeout(() => {
      this.props.addVariantToCart(
        "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE2ODAxNjEzMA==",
        this.props.Obsidiancounter
      );
    }, 1500);
    setTimeout(() => {
      this.props.addVariantToCart(
        "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE5Mjc1NTk3MA==",
        this.props.Opalcounter
      );
    }, 1700);
    // console.log(product)
    // this.props.addVariantToCart(
    //   this.props.idofmodelbig,
    //   this.props.counterforbig
    // );
    // setTimeout(() => {
    //   this.props.addVariantToCart(
    //     this.props.idofmodelsmall,
    //     this.props.counterforsmall
    //   );
    //   this.setState({ counterforsmall: 0 });
    //   this.setState({ counterforbig: 0 });
    // }, 1000);
  };

  addToCartEdited = () => {
    // document.getElementsByClassName("App")[0].style.cursor = "default";
    document.getElementsByClassName("INCORPbottomend")[0].style.cursor = "grab";
    this.props.Productsanimation();
    var itemChosen = "";
    if(this.props.currentModel == "toolObsidian"){
      itemChosen = "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE2ODAxNjEzMA==";
    } else if (this.props.currentModel == "toolOpal"){
      itemChosen = "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE5Mjc1NTk3MA==";
    } else if (this.props.currentModel == "body1"){
      itemChosen = "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE1OTEzNjAwMg==";
    } else if (this.props.currentModel == "body2"){
      itemChosen = "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM3MTQ4OTY2MTE4Ng==";
    } else if (this.props.currentModel == "body3"){
      itemChosen = "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM3MTQ5NTM5NTU4Ng==";
    } else {
      if (this.props.PDITitle == "I") {
        itemChosen = "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODAzMzQ3MDcyMg==";
      } else if (this.props.PDITitle == "YOU") {
        itemChosen = "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE2MDExOTA0Mg==";
      } else if (this.props.PDITitle == "WE") {
        itemChosen = "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE1OTg4OTY2Ng==";
      }
    }
    this.props.addVariantToCart(
      itemChosen,
      1
    );
    let event = new CustomEvent("buy-one", {
      detail: itemChosen,
    });
    window.dispatchEvent(event);
    
    setTimeout(() => {
      this.props.setShopDisplay();
    }, 500);
  }

  counterforbigbottlesState = () => {
    if (this.props.PDITitle == "I") {
      return this.props.ibigcounter;
    } else if (this.props.PDITitle == "YOU") {
      return this.props.youbigcounter;
    } else if (this.props.PDITitle == "WE") {
      return this.props.webigcounter;
    }
  };

  counterforsmallbottlesState = () => {
    if (this.props.PDITitle == "I") {
      return this.props.ismallcounter;
    } else if (this.props.PDITitle == "YOU") {
      return this.props.yousmallcounter;
    } else if (this.props.PDITitle == "WE") {
      return this.props.wesmallcouner;
    }
  };

  customiseObsidian = () => {
    let event = new CustomEvent("customiseObsidian", {});
    window.dispatchEvent(event);
  }

  customiseOpal = () => {
    let event = new CustomEvent("customiseOpal", {});
    window.dispatchEvent(event);
  }

  customiseTiger = () => {
    let event = new CustomEvent("customiseTiger", {});
    window.dispatchEvent(event);
  }

  render() {
    return (
      <>
        <div
          className="ProductDetails"
          style={{ display: this.props.ProductDetailsDisplay }}>
          {/* <button className="closey" onClick={() => this.props.ProductDisplaySwitch("close")}>
            <img src="backdetails.png" />
          </button> */}
          <div className="card">
            <div className="cardtitle">
              {this.props.PDITitle}{" "}
              {this.props.PDITitle != "Tool" ?
              (<div className="VoiceContainer">
                <Media>
                  <div className="media">
                    <CustomPlayPause />
                    <Player src={this.props.PDSound} />
                    <div className="media-controls"></div>
                  </div>
                </Media>
              </div>) :
              (<div></div>)
              }
            </div>
            <div className="cardline">{this.props.PDICardLine}</div>
            <div style={{fontStyle: "italic"}} className="carddescription">
              {this.props.PDIDescription == "With CLEANSING Thyme I eliminate distraction while SUPPORTING with grounding Vetiver and healing Myrrh. Antioxidant Melissa vitalizes, GUIDING me towards my authentic inner self." ? 
              (
                <span>With CLEANSING <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Thyme</span> I eliminate distraction while SUPPORTING with grounding <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Vetiver</span> and healing <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Myrrh</span>. Antioxidant <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Melissa</span> vitalizes, GUIDING me towards my authentic inner self.</span>
              ) :
              (
                this.props.PDIDescription == "With CLEANSING Lime and Coriander; you stimulate your body and mind. You SUPPORT emotional stability with Sandalwood, opening your heart with Rose and GUIDING you towards divine inner love." ?
                (
                  <span>With CLEANSING <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Lime</span> and <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Coriander</span>; you stimulate your body and mind. You SUPPORT emotional stability with <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Sandalwood</span>, opening your heart with <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Rose</span> and GUIDING you towards divine inner love.</span>
                ) :
                (
                  this.props.PDIDescription == "With CLEANSING Coriander, Orange and Thyme; we ignite your creative mind and act as an aphrodisiac. We SUPPORT tranquility with Vetiver while Roman Chamomile GUIDES us towards inner equilibrium." ?
                  (
                    <span>With CLEANSING <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Coriander, Orange</span> and <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Thyme</span>; we ignite your creative mind and act as an aphrodisiac. We SUPPORT tranquility with <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Vetiver</span> while <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Roman Chamomile</span> GUIDES us towards inner equilibrium.</span>
                  ) :
                  (
                    <span></span>
                  )
                )
              )}
              {this.props.PDITitle != "Tool" ?
              (<div className="editionMSG">
                {this.props.QuantityLimitedStatment}
              </div>) :
              (<div></div>)
              }
              {this.props.PDITitle != "Tool" ?
              (<div>
                <p className="ingredients">
                  Tool comes with the 33ml bottle. <br></br>
                  Chain comes with the 5ml bottle. <br></br>
                  {/* One tool available with every bottle. */}
                </p>
              </div>) :
              (<div></div>)
              }
              {this.props.PDITitle == "Tool" ?
              (<div className="toolsFlex">
                <span>Obsidian</span>
                <button onClick={() => this.customiseObsidian()}  style={{width: "25px", height: "25px", border: "none", padding:"0px", backgroundColor: "transparent", cursor:"pointer"}}> 
                  <img className="toolsCustButtonsDesktop" src= "Incorp-ToolObsidian.png" />
                </button>
                <span>Opal</span>
                <button onClick={() => this.customiseOpal()} style={{width: "25px", height: "25px", border: "none", padding:"0px", backgroundColor: "transparent", cursor:"pointer"}}> 
                  <img className="toolsCustButtonsDesktop" src= "Incorp-ToolOpal.png" />
                </button>
                <span>Tiger Eye</span>
                <button onClick={() => this.customiseTiger()} style={{width: "25px", height: "25px", border: "none", padding:"0px", backgroundColor: "transparent", cursor:"pointer"}}> 
                  <img className="toolsCustButtonsDesktop" src= "Incorp-ToolTiger.png" />
                </button>
              </div>) :
              (<div></div>)
              }
              {/* <p className="ingredients">
                Made with 100% pure and 100% natural Chemotyped cruelty free
                Essential oils. All raw materials analyzed by HPLChromatography
                and Mass Spectrometry, the highest quality controls.
              </p> */}
            </div>
            <div className="cardproduct">
              {/* <div className="ContainerSize1">
                <div className="counter">
                  <button
                    className="reduce"
                    onClick={() => this.props.setibigcounterdecrease()}>
                    -
                  </button>
                  <span> {this.counterforbigbottlesState()}</span>
                  <button
                    className="increase"
                    onClick={() => this.props.setibigcounter()}>
                    +
                  </button>
                </div>
                <span className="ItemDetailsNumbers">
                  {this.props.PDITitle == "I" ? "33ml \xa0€865" : ""}
                  {this.props.PDITitle == "WE" ? "33ml \xa0€835" : ""}
                  {this.props.PDITitle == "YOU" ? "33ml \xa0€935" : ""}
                  <br />
                  <span style={{fontStyle: "italic"}}>50/50 <i>available</i></span>
                </span>
              </div> */}
              {/* <div className="ContainerSize1">
                <div className="counter">
                  <button
                    className="reduce"
                    onClick={() => this.props.setismallcounterdecrease()}>
                    -
                  </button>
                  <span> {this.counterforsmallbottlesState()}</span>
                  <button
                    className="increase"
                    onClick={() => this.props.setismallcounter()}>
                    +
                  </button>
                </div>
                <span className="ItemDetailsNumbers">
                  {this.props.PDITitle == "I" ? "5ml \xa0€415" : ""}
                  {this.props.PDITitle == "WE" ? "5ml \xa0€385" : ""}
                  {this.props.PDITitle == "YOU" ? "5ml \xa0€485" : ""}
                  <br />
                  <span style={{fontStyle: "italic"}}>50/50 <i>available</i></span>
                </span>
              </div> */}
              {/* <span style={{ fontSize: "1vw" }}>
                Tool{" "}
                <div className="counter" id="fontyy">
                  Obsidian or Onyx
                </div>
              </span> */}
              {/* <div className="ToolMSGAV">
                One tool available with any perfume{" "}
              </div>
              <div className="ContainerSize1">
                <div className="counter">
                  <button
                    className="reduce"
                    onClick={() => this.props.setObsidiancounterdecrease()}>
                    -
                  </button>
                  <span> {this.props.Obsidiancounter}</span>
                  <button
                    className="increase"
                    onClick={() => this.props.setObsidiancounter()}>
                    +
                  </button>
                </div>
                <span className="ItemDetailsNumbers">
                  Obsidian&ensp;€65
                  <button className="EYEButton" 
                  onMouseEnter={() => this.props.Changerimg("black")}
                  onMouseLeave={() => this.props.Changerimg("white")}>
                    <img src={this.props.InfoImageSRC} />
                  </button>
                  <br />
                  <span style={{fontStyle: "italic"}}>50/50 <i>available</i></span>
                </span>
              </div> */}
              {/* <div className="ContainerSize1">
                <div className="counter">
                  <button
                    className="reduce"
                    onClick={() => this.props.setOpalcounterdecrease()}>
                    -
                  </button>
                  <span> {this.props.Opalcounter}</span>
                  <button
                    className="increase"
                    onClick={() => this.props.setOpalcounter()}>
                    +
                  </button>
                </div>
                <span className="ItemDetailsNumbers">
                  Opal&ensp;€85
                  <button className="EYEButton" 
                  onMouseEnter={() => this.props.Changerimg("black")}
                  onMouseLeave={() => this.props.Changerimg("white")}>
                    <img src={this.props.InfoImageSRC} />
                  </button>
                  <br />
                  <span style={{fontStyle: "italic"}}>50/50 <i>available</i></span>
                </span>
              </div> */}
            </div>
            <div className="CarddFooter">
              <div className="InfoCardNew">
                Designed by
                <button onClick={() => this.props.ProductInfoSwitcher()} 
                onMouseEnter={() => this.props.Changerimg("black")}
                onMouseLeave={() => this.props.Changerimg("white")}>
                  <img src={this.props.InfoImageSRC} />
                </button>
              </div>
              <button onClick={() => this.addToCartEdited()} id="buyOneButton" className="cart">
                Buy
              </button>
            </div>
          </div>
        </div>
        {/* /******* div for  bottle ******** */}
        <div
          className="bottlediv"
          style={{ display: this.props.ProductDetailsBottleDisplay, animation: "fadeIn 0.5s", overflowY: "scroll" }}>
          {window.innerWidth > 601  ? (
            <button
            className="closey"
            id="editedone"
            onClick={() =>
              this.props.ProductDisplayBottleSwitch("Body", "close")
            }>
            <img src="close.png" />
          </button>
            ) :
          (<div></div>
          )}
          <div className="card">
          <div className="cardtitle" id="titleedited">
              ESSENTIAL OIL BLEND
            </div>
            <div className="cardline">By Sissel Tolaas</div>
            <div className="carddescription">
              Using the highest quality, pure essential oils, Sissel mixed this unique blend
            </div>
            <img style={{width: "-webkit-fill-available", marginTop: "10%"}} src="Sissel Tolaas_BW-min.jpg" />
            <div className="cardtitle" id="titleedited" style={{marginTop: "10%"}}>
              33ml BOTTLE
            </div>
            <div className="cardline">By Samuel Reis</div>
            <div className="carddescription">
              Casted out of the core of a tree
            </div>
            <img style={{width: "-webkit-fill-available", marginTop: "10%"}} src="Samuel Reis-min.jpg" />
            
            {/* <div className="youtubeframe">
              <Media>
                <div className="media">
                  <div className="media-player">
                    <CustomVideoPlay />
                    <Player
                      src="33ml bottle - 720WebShareName.mov"
                      className="VideoContainer"
                    />
                  </div>
                </div>
              </Media>
            </div> */}
          </div>
        </div>
        {/* /******* div for  bottle cap ******** */}
        <div
          className="cup"
          style={{ display: this.props.ProductDetailsBottlecupDisplay, animation: "fadeIn 0.5s" }}>
          {window.innerWidth > 601  ? (
            <button
            className="closey"
            id="editedone"
            onClick={() =>
              this.props.ProductDisplayBottleSwitch("Cover", "close")
            }>
            <img src="close.png" />
          </button>
            ) :
          (<div></div>
          )}
          {/* {window.innerWidth > 601  ? (
            <div style={{position: "absolute", top: "55%", left: "15%"}}>
              <img style={{width: "18vw"}} src={this.props.handImage33ml} />
            </div>
            ) :
          (<div></div>
          )} */}
          <div className="card">
            <div className="cardtitle" id="titleedited">
              33ml TOP
            </div>
            <div className="cardline">By Ugo Cacciatori</div>
            <div className="carddescription">
            Shaped with wax to mirror and map the top of the bottle, then casted in bronze 
            </div>
            <img style={{width: "-webkit-fill-available", marginTop: "10%"}} src="Ugo Cacciatori-min.jpg" />
            {/* <div className="youtubeframe">
              <Media>
                <div className="media">
                  <div className="media-player">
                    <CustomVideoPlay />
                    <Player
                      src="33ml top  - 720WebShareName.mov"
                      className="VideoContainer"
                    />
                  </div>
                </div>
              </Media>
            </div> */}
          </div>
        </div>
        {/* /******* div for  Tool ******** */}
        <div
          className="Tooldiv"
          style={{ display: this.props.ProductDetailsBottleToolDisplay, animation: "fadeIn 0.5s" }}>
          {window.innerWidth > 601  ? (
            <button
            className="closey"
            id="editedone"
            onClick={() =>
              this.props.ProductDisplayBottleSwitch("Tool", "close")
            }>
            <img src="close.png" />
          </button>
            ) :
          (<div></div>
          )}
          {/* {window.innerWidth > 601  ? (
            <div style={{position: "absolute", bottom: "calc(0% - 3.5px)", left: "105%"}}>
              <img style={{width: "11vw"}} src={this.props.handImagetool} />
            </div>
            ) :
          (<div></div>
          )} */}
          <div className="card">
            <div className="cardtitle" id="titleedited">
              TOOL
            </div>
            <div className="cardline">By Marco Panconesi</div>
            <div className="carddescription">
            Shaped to fit into the 33ml bottle; used for acupressure, massage or as a spoon
            </div>
            <img style={{width: "-webkit-fill-available", marginTop: "10%"}} src="Marco Panconesi-min.jpg" />
            {/* <div className="youtubeframe">
              <Media>
                <div className="media">
                  <div className="media-player">
                    <CustomVideoPlay />
                    <Player
                      src="TOOL_H - 720WebShareName.mov"
                      className="VideoContainer"
                    />
                  </div>
                </div>
              </Media>
            </div> */}
          </div>
        </div>
        {/* /******* div for  smallbottle ******** */}
        <div
          className="SmallBdiv"
          style={{ display: this.props.ProductDetailsBottleSmallDisplay, animation: "fadeIn 0.5s" }}>
          {window.innerWidth > 601  ? (
             <button
            className="closey"
            id="editedone"
            onClick={() =>
              this.props.ProductDisplayBottleSwitch("SmallBottle", "close")
            }>
            <img src="close.png" />
          </button>
            ) :
          (<div></div>
          )}
          <div className="card">
            <div className="cardtitle" id="titleedited">
              5ml TOP
            </div>
            <div className="cardline">By Ugo Cacciatori</div>
            <div className="carddescription">
              Taken from the scan of the 33ml top, shaped and reduced size 
            </div>
            <img style={{width: "-webkit-fill-available", marginTop: "10%"}} src="Ugo Cacciatori-min.jpg" />
            {/* <div className="youtubeframe">
              <Media>
                <div className="media">
                  <div className="media-player">
                    <CustomVideoPlay />
                    <Player
                      src="5ml bottle  - 720WebShareName.mov"
                      className="VideoContainer"
                    />
                  </div>
                </div>
              </Media>
            </div> */}
          </div>
        </div>
        {/* {window.innerWidth > 601  ? (
            <div style={{zIndex: "1", position: "absolute", top: "60%", left: "70%", display: this.props.ProductDetailsBottleSmallBodyDisplay, animation: "fadeIn 0.5s"}}>
              <img style={{width: "18vw"}} src={this.props.handImage5ml} />
            </div>
            ) :
          (<div></div>
          )} */}
        <div
          className="SmallBBodydiv"
          style={{ display: this.props.ProductDetailsBottleSmallBodyDisplay, animation: "fadeIn 0.5s", overflowY: "scroll" }}>
          {window.innerWidth > 601  ? (
            <button
            className="closey"
            id="editedone"
            onClick={() =>
              this.props.ProductDisplayBottleSwitch("SmallBottleBody", "close")
            }>
            <img src="close.png" />
          </button>
            ) :
          (<div></div>
          )}
          <div className="card">
            <div className="cardtitle" id="titleedited">
              ESSENTIAL OIL BLEND
            </div>
            <div className="cardline">By Sissel Tolaas</div>
            <div className="carddescription">
              Using the highest quality, pure essential oils, Sissel mixed this unique blend
            </div>
            <img style={{width: "-webkit-fill-available", marginTop: "10%"}} src="Sissel Tolaas_BW-min.jpg" />
            <div className="cardtitle" id="titleedited" style={{marginTop: "10%"}}>
              5ml BOTTLE
            </div>
            <div className="cardline">By Kira Lillie</div>
            <div className="carddescription">
              Taken from the scan of the 33ml bottle, dripped and reduced size 
            </div>
            <img style={{width: "-webkit-fill-available", marginTop: "10%"}} src="Kira Lillie_BW-min.jpg" />
            {/* <div className="youtubeframe">
              <Media>
                <div className="media">
                  <div className="media-player">
                    <CustomVideoPlay />
                    <Player
                      src="5ml bottle  - 720WebShareName.mov"
                      className="VideoContainer"
                    />
                  </div>
                </div>
              </Media>
            </div> */}
          </div>
        </div>
        {window.innerWidth < 601 && this.props.SceneStatus == "INSIDE" ? (
          <div style={{ display: this.props.MobileProductDetailsDisplay }}>
            <MobileProductSwitcher
              PDICardLine={this.props.PDICardLine}
              decreaseounterBig={this.decreaseounterBig}
              decreaseounteropal={this.decreaseounteropal}
              decreaseounterobsedian={this.decreaseounterobsedian}
              decreaseountertiger={this.decreaseountertiger}
              counterforbig={this.state.counterforbig}
              counterforobsedian={this.state.counterforobsedian}
              counterforopal={this.state.counterforopal}
              counterfortiger={this.state.counterfortiger}
              decreaseounterForSmall={this.decreaseounterForSmall}
              counterforsmall={this.state.counterforsmall}
              addToCart={this.addToCart}
              addToCartmobile={this.addToCartmobile}
              IncreaseCounterForBigMobile={this.IncreaseCounterForBigMobile}
              IncreaseCounterForSmallMobile={this.IncreaseCounterForSmallMobile}
              IncreaseCounterForopal={this.IncreaseCounterForopal}
              IncreaseCounterFortiger={this.IncreaseCounterFortiger}
              IncreaseCounterForobsedian={this.IncreaseCounterForobsedian}
              PDITitle={this.props.PDITitle}
              bodyUnderline={this.props.bodyUnderline}
              bodySmallUnderline={this.props.bodySmallUnderline}
              toolObsidian={this.props.toolObsidian}
              toolOpal={this.props.toolOpal}
              toolTiger={this.props.toolTiger}
              ShopPageDisplaySwitchSpecial={this.props.ShopPageDisplaySwitchSpecial}
              productTitleOpacity={this.props.productTitleOpacity}
              productTitleAnimation={this.props.productTitleAnimation}
              currentModel={this.props.currentModel}
              showProductDetailMobile={this.props.showProductDetailMobile}
            />
          </div>
        ) : (
          ""
        )}
      </>
    );
  }
}

export default ProductDetails;
