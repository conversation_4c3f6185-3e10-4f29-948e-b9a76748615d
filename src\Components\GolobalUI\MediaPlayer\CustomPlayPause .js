import React, { Component } from 'react';
import { withMediaProps } from 'react-media-player';
import "./CustomPlayPause.css";

class CustomPlayPause extends Component {
  shouldComponentUpdate({ media }) {
    return this.props.media.isPlaying !== media.isPlaying
  }

  _handlePlayPause = () => {
    // this.props.media.playPause()
    this.props.media.isPlaying ? this.props.media.stop() : this.props.media.play()
  }

  render() {
    const { media } = this.props
    if (window.innerWidth > 601) {
      return (
        <button
         className="PlayerContainer"
          onClick={this._handlePlayPause}
        >
          {media.isPlaying ? <img src="pictoSoundPause.png" /> : <img src="pictoSoundOn.png" /> }
        </button>
      )
    } else {
      return (
        <button
         className="PlayerContainer"
          onClick={this._handlePlayPause}
        >
          {media.isPlaying ? <img src="pictoSoundPause.png" /> : <img src="pictoSoundOn.png" /> }
        </button>
      )
    }
  }
}

export default withMediaProps(CustomPlayPause);