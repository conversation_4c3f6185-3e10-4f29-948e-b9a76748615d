.MobileAboutPage {
    position: absolute;
    width: 100%;
    height: 86%;
    margin-top: 38%;
    /* background-color: rgb(240, 240, 240); */
    background-color: rgba(236, 231, 231, 1);
    z-index: 2;
}

.MobilePageContentMobile {
    width: 94%;
    margin-left: 3%;
    margin-right: 3%;
    padding-top: 5%;
    overflow-y: auto;
    overflow-x: hidden;
    height: 86%;
}

.MobilePageContentMobile::-webkit-scrollbar {
    display: none;
}

.AboutMdiscription {
    display: flex;
    align-items: center;
    text-align: left;
    /* font-weight: 550; */
    font-size: 4.5vw;
    /* line-height: 2.5vh; */
    line-height: 21px;
    font-size: 4.15vw;
    align-items: flex-start;
    flex-direction: column;
}

.Dmember {
    width: 100%;
    margin-top: 6%;
}

.DTitle {
    font-size: 6.5vw;
    /* font-weight: 600; */
    display: flex;
    text-align: left;
    font-family: "<PERSON><PERSON><PERSON>", <PERSON>aslon;
    margin-bottom: 2%;
    font-style: italic;
}

.DImage {
    width: 100%;
}

.DImage img {
    width: 100%;
}

.DDiscription {
    width: 100%;
    display: flex;
    text-align: left;
    margin-top: 2%;
    /* font-weight: 580; */
    flex-wrap: wrap;
    line-height: 21px;
    font-size: 4.15vw;
}