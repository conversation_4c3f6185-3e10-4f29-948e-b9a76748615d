@font-face {
  font-family: "Caslon";
  src: url("../src/Fonts/Caslon540.ttf") format("truetype");
}
@font-face {
  font-family: "CaslonBold";
  src: url("../src/Fonts/LibreCaslonText-Bold.ttf") format("truetype");
}
button {
  outline: none !important;
}
.App {
  text-align: center;
  font-family: Caslon !important;
  /* cursor: url("http://www.rw-designer.com/cursor-extern.php?id=123034"), auto; */
  cursor: grab;
}
body {
  font-family: Caslon !important;
  margin: 0px;
  overflow-y: hidden;
  overflow-x: hidden;
  /* background-color: rgba(240, 240, 240, 1) !important; */
  background-color: rgba(236, 231, 231, 1) !important;
}
.App-logo {
  height: 40vmin;
  pointer-events: none;
}
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #f5f5f5;
}
::-webkit-scrollbar {
  width: 6px;
  background-color: #f5f5f5;
}
::-webkit-scrollbar-thumb {
  background-color: #000000;
}
@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}
.ArrowHelper {
  position: absolute;
  width: 8%;
  height: 60%;
  text-align: center;
  bottom: 0%;
  background: none;
  margin: auto 10px auto auto;
  top: 10%;
  outline: none;
  cursor: pointer;
  padding: 1%;
  text-align: center;
  align-items: center;
}
.btnnavigation {
  position: absolute;
  /* width: 55px;
  height: 60px; */
  width: 99px;
  height: 91px;
  text-align: center;
  /* bottom: 0%; */
  background: none;
  margin: auto 10px auto auto;
  border: none;
  top: 30%;
  border-radius: 50%;
  outline: none;
  cursor: pointer;
  padding: 1%;
  text-align: center;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transform: rotate(90deg);
}
.ArrowHelper:hover .btnnavigation {
  opacity: 0.8;
}
.btnnavigation img {
  width: 36%;
  margin-top: 25%;
  margin-left: auto;
  margin-right: auto;
  transform: rotate(-90deg);
  opacity: 0.8;
}
.btnleft {
  left: 0;
  margin-left: 0%;
}
.btnright {
  right: 0;
  margin-right: 0%;
}
.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}
.App-link {
  color: #61dafb;
}
@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.IncorpFont {
  font-family: Caslon !important;
  font-style: italic;
  position: absolute;
  width: 50%;
  height: 20%;
  top: 40%;
  left: 25%;
  z-index: 99999999999999999999999 !important;
}
/* @media screen and (max-width: 1000px) and (orientation: landscape) {
  .canvas3d {
    width: 100vh;
    transform: rotate(-90deg);
  }
} */
