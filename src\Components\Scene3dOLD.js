import React, { Component } from "react";

import {
  Scene,
  Engine,
  AssetsManager,
  ArcRotateCamera,
  CubicEase,
  Animation,
  Vector3,
  HemisphericLight,
  QuadraticEase,
  SpotLight,
  PointLight,
  ShadowGenerator,
  FxaaPostProcess,
  PassPostProcess,
  ConvolutionPostProcess,
  TonemapPostProcess,
  TonemappingOperator,
  DefaultRenderingPipeline,
  ImageProcessingPostProcess,
  ParticleSystem,
  EasingFunction,
  Mesh,
  SceneLoader,
  Layer,
  Color3,
  Color4,
  Tools,
  HighlightLayer,
  MeshBuilder,
  Texture,
  FreeCamera,
  DirectionalLight,
  PhotoDome,
  HDRCubeTexture,
  CubeTexture,
  StandardMaterial,
  PBRMaterial,
  PBRMetallicRoughnessMaterial,
  ReflectionProbe,
  FresnelParameters,
  RefractionTexture,
  BackgroundMaterial,
  MirrorTexture,
  Plane,
  ActionManager,
  SwitchBooleanAction,
  ExecuteCodeAction,
  PointerEventTypes,
  KeyboardEventTypes,
  Axis,
  GlowLayer,
  Space,
  Matrix,
  DefaultLoadingScreen,
  EnvironmentTextureTools,
} from "babylonjs";
import "babylonjs-loaders";

// Here we extend Reacts component class
class Scene3 extends Component {
  // Makes the canvas behave responsively
  onResizeWindow = () => {
    if (this.engine) {
      this.engine.resize();
    }
  };

  // Sets up our canvas tag for webGL scene
  setEngine = () => {
    this.engine = new Engine(this.stage, true, {
      antialias: true,
      stencil: true,
      preserveDrawingBuffer: true,
    });
    if (window.devicePixelRatio < 2) {
      this.engine.setHardwareScalingLevel(1 / 2);
    } else {
      this.engine.setHardwareScalingLevel(1 / window.devicePixelRatio);
    }
    this.engine.setHardwareScalingLevel(1 / window.devicePixelRatio);
    this.stage.style.width = "100%";
    this.stage.style.height = "100vh";
    this.stage.style.outline = "none";
    this.stage.style.borderRadius = "20px";
    //this.stage.style.boxShadow = "0 2px 2px 0 rgb(0 0 0 / 14%), 0 3px 1px -2px rgb(0 0 0 / 12%), 0 1px 5px 0 rgb(0 0 0 / 20%)"
    this.stage.style.boxShadow = "none";
    this.engine.resize();
    if (window.innerWidth < 992) {
      this.stage.style.width = "100%";
      this.stage.style.height = "30vh";
      this.stage.style.boxShadow = "none";
    }
  };

  // Creates the scene graph
  setScene = () => {
    this.scene = new Scene(this.engine);
    this.scene.clearColor = new Color4(1, 1, 1, 1).toLinearSpace();
    //this.scene.debugLayer.show();
  };

  setCamera = () => {
    this.camera = new ArcRotateCamera("Camera", 1.5708, 1.5708, 512.0955, new Vector3(-3.317, 151.078, -6.399), this.scene);
    this.camera.attachControl(this.stage, true);
    // this.camera.lowerRadiusLimit=512.0955
    // this.camera.upperRadiusLimit=512.0955
  };

  constructor(props) {
    super(props);
    // We bind our event to keep the proper "this" context.
    this.loadModels = this.loadModels.bind(this);
    this.Startanimation = this.Startanimation.bind(this);
  }

  loadModels = async () => {
    DefaultLoadingScreen.prototype.displayLoadingUI = function () {
      if (this._loadingDiv) {
        // Do not add a loading screen if there is already one
        return;
      }
      //   this._loadingDivBlock = document.createElement("div");
      //   this._loadingDivBlock.id = "babylonjsLoadingDivBlock";
      //   this._loadingDivBlock.style.opacity = "1";
      //   this._loadingDiv = document.createElement("div");
      //   this._loadingDiv.id = "babylonjsLoadingDiv";
      //   this._loadingDiv.style.animation = "spin 1s linear infinite";
      //   // Generating keyframes
      //   var style = document.createElement("style");
      //   style.type = "text/css";
      //   document.getElementsByTagName("head")[0].appendChild(style);
      //   document.body.appendChild(this._loadingDivBlock);
      //   this._loadingDivBlock.appendChild(this._loadingDiv);
      //   this._loadingDivBlock.style.transition = "opacity 1s ease";
      //   this._loadingDivBlock.style.pointerEvents = "none";
      //   this._loadingDivBlock.style.backgroundColor = "transparent";
      //   this._loadingDivBlock.style.position = "absolute";
      //   this._loadingDivBlock.style.width = "100%";
      //   this._loadingDivBlock.style.height = "100vh";
      //   this._loadingDivBlock.style.top = "-15vh";
      //   if (window.innerWidth < 470) {
      //     this._loadingDivBlock.style.left = "-12%";
      //   } else {
      //     this._loadingDivBlock.style.left = "-4%";
      //   }
      //   this._loadingDiv.style.opacity = "1";
      //   this._loadingDiv.style.transition = "opacity 1s ease";
      //   this._loadingDiv.style.pointerEvents = "none";
      //   this._loadingDiv.style.backgroundColor = "transparent";
      //   this._loadingDiv.style.borderRadius = "50%";
      //   this._loadingDiv.style.position = "relative";
      //   this._loadingDiv.style.margin = "auto";
      //   this._loadingDiv.style.top = "47vh";
      //   this._loadingDiv.style.width = "12vh";
      //   this._loadingDiv.style.height = "12vh";
      //   this._loadingDiv.innerHTML = "<img src='loadingfig.gif' />";
    };

    let HDR = new CubeTexture.CreateFromPrefilteredData("./hdr/studio.env", this.scene);
    //HDR.setReflectionTextureMatrix(Matrix.RotationY(Tools.ToRadians(0)));
    this.scene.environmentTexture = HDR;
    HDR.level = 1;

    await SceneLoader.AppendAsync("", "./BodyV1.glb", this.scene);
    let Body = this.scene.getMeshByName("__root__");
    Body.name = "BodyMesh";

    await SceneLoader.AppendAsync("", "./Hebox_V2.glb", this.scene);
    let product1 = this.scene.getMeshByName("__root__");
    let HDR1 = new CubeTexture.CreateFromPrefilteredData("./hdr/Perfume_HDR.env.env", this.scene);
    product1.environmentTexture = HDR1;
    product1.name = "product1";
    product1.scaling.x = 30;
    product1.scaling.y = 30;
    product1.scaling.z = 30;
    product1.rotation = new Vector3(Tools.ToRadians(36.0755), Tools.ToRadians(90.8997), Tools.ToRadians(-88.9678));
    product1.position = new Vector3(17.87, 133.619, 12.235);

    this.scene.getMaterialByName("Body").name = "productBody";
    let productBody = this.scene.getMaterialByName("productBody");
    productBody.albedoColor = new Color3.FromInts(216, 203, 186);
    productBody.metallic = 0.21;
    productBody.emissiveColor = new Color3.FromInts(255, 255, 255);

    productBody.emissiveTexture = new Texture("Hebox_Refraction.jpg", this.scene, true, true);
    productBody.clearCoat.texture = new Texture("Hebox_Refraction.jpg", this.scene);

    productBody.clearCoat.indexOfRefraction = 1.5;
    productBody.clearCoat.isEnabled = true;
    productBody.backFaceCulling = true;
    productBody.clearCoat.roughness = 0;
    // productBody.metallicF0Factor = 0;
    // productBody.clearCoat.intensity = 0.23;
    // productBody.clearCoat.roughness = 0;

    this.scene.getMaterialByName("Cover").name = "Cover";
    let Cover = this.scene.getMaterialByName("Cover");
    Cover.albedoColor = new Color3.FromInts(153, 126, 96);
    Cover.metallic = 1;
    Cover.clearCoat.roughness = 0.13;
    Cover.metallicF0Factor = 1;
    Cover.bumpTexture = new Texture("Cover_Normal.jpg", this.scene, false, false);

    this.scene.getMaterialByName("Helix").name = "Helix";
    let Helix = this.scene.getMaterialByName("Helix");
    Helix.albedoColor = new Color3.FromInts(255, 255, 255);
    Helix.metallic = 0;
    Helix.clearCoat.roughness = 0;
    Helix.metallicF0Factor = 1;
    Helix.backFaceCulling = true;
    // Helix.bumpTexture = new Texture(
    //   "Cover_Normal.jpg",
    //   this.scene,
    //   false,
    //   false
    // );

    await SceneLoader.AppendAsync("", "./KiraLogo.glb", this.scene);

    /************************************/
    let Logo = this.scene.getMeshByName("__root__");
    Logo.name = "LogoMesh";
    Logo.position = new Vector3(0, 260, -12);
    Logo.rotation = new Vector3(Tools.ToRadians(90.8706), Tools.ToRadians(-55.5804), Tools.ToRadians(117.6595));
    Logo.scaling.x = 50;
    Logo.scaling.y = 50;
    Logo.scaling.z = 50;

    // product1.isPickable = true;
    // product1.actionManager = new ActionManager(this.scene);
    // product1.actionManager.registerAction(
    //   new ExecuteCodeAction(
    //     {
    //       trigger: ActionManager.OnPickTrigger,
    //     },
    //     function () {
    //      alert("helooooo")
    //     }
    //   )
    // );

    // let camera2=this.scene.getCameraById("VRayCam001")
    // this.camera.attachControl(this.stage, false);
    //   this.scene.activeCamera=camera2
    // camera2.attachControl(this.stage, true);
    //   var aniamtion=this.scene.getAnimationGroupByName("All Animations")
    //   aniamtion.loopAnimation=false
    //   aniamtion.start()

    //   console.log(aniamtion.onAnimationGroupEndObservable)
    //   aniamtion.onAnimationGroupEndObservable

    //     aniamtion.onAnimationEnd = function () {
    // 	// animatable.animationStarted = false;
    // 	alert('animatable.onAnimationEnd func reports the animation stopped');
    // }

    this.engine.runRenderLoop(() => {
      if (this.props.SceneStatus == "INSIDE") {
        product1.setEnabled(true);
      } else if (this.props.SceneStatus == "OUTSIDE") {
        product1.setEnabled(false);
      }

      this.scene.render();
    });
    let animations = await Animation.ParseFromFileAsync(null, "./animationradius.json");
    this.camera.animations = animations;
    //this.scene.beginAnimation(this.camera, 0, 1000, false);
  };
  Startanimation = () => {
    this.scene.beginAnimation(this.camera, 0, 1000, false);
  };
  //Build the scene when the component has been loaded.
  componentDidMount() {
    this.setEngine();
    this.setScene();
    this.setCamera();
    this.loadModels();
    window.addEventListener("resize", this.onResizeWindow);
    window.addEventListener("Startanimation", this.Startanimation);
  }
  //Renderes our Canvas tag and saves a reference to it.
  render() {
    return <canvas className="canvas3d" ref={(el) => (this.stage = el)}></canvas>;
  }
}
//returns the scene to be used by other components
export default Scene3;
