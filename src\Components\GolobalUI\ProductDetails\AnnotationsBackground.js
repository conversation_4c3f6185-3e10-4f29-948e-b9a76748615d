import React, { Component } from "react";
import "./ProductDetails.css";

class AnnotationsBackground extends Component {

  render() {
    return (
        <div className="AnnotationsBackground" style={{display: this.props.AnnotationsBackgroundDisplay, 
            animation: this.props.PDMAnimation}}>
            <div style={{position: "absolute", width: "100%", height: "22%", top: "0%"}} onClick={()=> this.props.AnnotationBackgroundMobileHide()}>
            </div>
            <div style={{position: "absolute", width: "100%", height: "28%", top: "72%"}} onClick={()=> this.props.AnnotationBackgroundMobileHide()}>   
            </div>
            
        </div>
    );
  }
}

export default AnnotationsBackground;
