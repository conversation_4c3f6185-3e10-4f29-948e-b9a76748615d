import React, { Component } from "react";
import Color from "./Color";

class Customization extends Component {
  render() {
    return (
      <div className="Customization">
        <div className="TabsMenu">
          <button
            className="Colours"
            style={{
              color: this.props.coloursMenuColor,
              borderBottom: this.props.coloursMenuBorder
            }}
          >
            Customisations
          </button>
        </div>
        <div className="TabsContent">
          <div
            className="ColoursContent"
            style={{ display: this.props.coloursDisplay }}
          >
            {this.props.colors.map(color => (
              <Color
                key={color}
                colorName={color}
                colorPressed={this.props.colorPressed}
              />
            ))}
          </div>
        </div>
      </div>
    );
  }
}

export default Customization;
