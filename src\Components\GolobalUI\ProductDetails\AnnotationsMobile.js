import React, { Component } from "react";
import "./ProductDetails.css";

class AnnotationsMobile extends Component {

  render() {
    return (
        <div className="AnnotationsMobile"  style={{display: this.props.AnnotationsMobileDisplay}}>
          <button onClick={() => this.props.ProductInfoSwitcherMobile()} style={{border: "none", padding:"0px", backgroundColor: "transparent"}}> 
                <img className="annotationEye" src= {this.props.InfoImageSRC} />
          </button>
        </div>
    );
  }
}

export default AnnotationsMobile;
