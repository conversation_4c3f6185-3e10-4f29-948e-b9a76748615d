.ButtonsUI {
  overflow: hidden;
}
.ButtonsUI a {
  text-decoration: none;
}
.INCORPlogo {
  width: 15%;
  position: absolute;
  z-index: 99;
  height: 10%;
  margin-left: 42.5%;
  margin-top: 0%;
  padding: 0%;
}
.INCORPlogo img {
  width: 100%;
}
.INCORPbottom {
  width: 5.5%;
  position: absolute;
  z-index: 99;
  height: 7.5%;
  margin-left: 47.25%;
  /* width: 7%;
  position: absolute;
  z-index: 99;
  height: 10%;
  margin-left: 46.5%; */
  bottom: 50%;

  /* padding: 0%;  
  margin-bottom: 1%; */
  /* text-align: center;
  cursor: pointer; */

  /* animation: expand 2s linear; */
  /* animation-direction: alternate; */
  /* -webkit-animation: expand 2s cubic-bezier(0.4, 0, 1, 1) forwards; */
  /* -moz-animation: expand  2s linear  ; */
  /* animation: expand  1s ease ;
  animation-iteration-count: 1; */
  /* transition: all 1s;
  transform: translateY(500%) */
  /* -moz-animation-iteration-count: 1;
  -webkit-animation-iteration-count: 1 */
}
.INCORPbottomend {
  width: 5.5%;
  position: absolute;
  z-index: 99;
  height: 7.5%;
  margin-left: 47.25%;
  bottom: 50%;
  cursor: url("images/Back_black_105x55_50.png"), pointer;

  /* padding: 0%;  
  margin-bottom: 1%; */
  /* text-align: center;
  cursor: pointer; */

  /* animation: expand 2s linear; */
  /* animation-direction: alternate; */
  -webkit-animation: expand 4s forwards;
  /* -moz-animation: expand  2s linear  ; */
  /* animation: expand  1s ease ;
  animation-iteration-count: 1; */
  /* transition: all 1s;
  transform: translateY(500%) */
  /* -moz-animation-iteration-count: 1;
  -webkit-animation-iteration-count: 1 */
}
.incorplogoimage {
  /* position: absolute; */
  /* left: 452px; */
  /* margin-left: 39%;
  margin: auto;
  margin-left: 40.9%;
  top: 4%; */
  display: flex;
  justify-content: center;
  /* height: 500px; */
  display: flex;
  align-items: center;
  position: inherit;
  /* margin-top: 0%; */
  justify-content: center;
}
.INCORPbottomend img {
  width: 100%;
  -webkit-animation: spin 3s linear infinite;
  -moz-animation: spin 3s linear infinite;
  animation: spin 3s linear infinite;
}
.INCORPbottom img {
  width: 100%;
  -webkit-animation: spin 2.5s linear infinite;
  -moz-animation: spin 2.5s linear infinite;
  animation: spin 2.5s linear infinite;
}
.INCORPbottom img:hover {
  /* animation-name: spin;
  animation-duration: 5000ms;
  animation-iteration-count: infinite;
  animation-timing-function: linear; */
}
@-moz-keyframes spin {
  100% {
    -moz-transform: rotate(360deg);
  }
}
@-webkit-keyframes spin {
  100% {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes spin {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes expand {
  0% {
    left: 0px;
    bottom: 50%;
  }
  /* 25%  { left: 0px; bottom: 40%;}
  50%  { left: 0px; bottom: 30%;}
  75%  { left: 0px; bottom: 20%;} */
  100% {
    left: 0px;
    bottom: 3%;
  }
  /* 75%  { left: 0px; top: 80px;}
  100% { left: 0px; top: 100px;} */
  /* 0%{
    bottom: 50%
  }

100%{
    bottom: 0% 
    
  } */
}
@keyframes expandMobile {
  0% {
    left: 0px;
    bottom: 50%;
  }
  /* 25%  { left: 0px; bottom: 40%;}
  50%  { left: 0px; bottom: 30%;}
  75%  { left: 0px; bottom: 20%;} */
  100% {
    left: 0px;
    bottom: -10%;
  }
  /* 75%  { left: 0px; top: 80px;}
  100% { left: 0px; top: 100px;} */
  /* 0%{
    bottom: 50%
  }

100%{
    bottom: 0% 
    
  } */
}

.go {
  /*New content */
  -webkit-animation-fill-mode: forwards;
}
.Pictogram {
  position: absolute;
  height: 50px;
  width: 50px;
  top: calc(62% - 50px);
  left: calc(51.5% - 50px);
}
.pictogramimg {
  height: 50px;
  width: 50px;
}

.downloadmap {
  position: absolute;
  /* top: calc(50vh - 50px); */
  bottom: 2%;
  right: 38%;
  width: 38px;
  height: 38px;
  border: 1px solid black;
  background-color: transparent;
  border-radius: 25px;
  cursor: pointer;
  padding: 0.2%;
  transition: 0.2s;
}
.downloadmap img {
  width: 26px;
  /* float: left; */
}
.downloadmap span {
  display: none;
  margin: 0;
  padding: 0;
  margin-top: 0px;
  font-family: Caslon;
  font-size: 8px;
  font-family: Caslon;
  font-weight: bold;
  letter-spacing: 0.1vh;
}
.downloadmap:hover {
  width: 130px;
  height: 80px;
  right: 35%;
}
.downloadmap:hover span {
  display: inline-block;
}
.ControlsShop {
  color: black;
  outline: none;
  border-radius: 0px;
  border: none;
  position: relative;
  cursor: pointer;
  font-size: 1.5em;
  font-family: Caslon;
  padding: 0;
  margin: 0;
  display: flex;
  background-color: #fcf8f0;
  /* box-sizing: border-box; */
  text-align: center;
  font-weight: 500;
  letter-spacing: 0.05vw;
  border: 1px solid red;
}
.ControlsShop:hover {
  background-color: black;
  color: white;
  transition: 0.5s ease;
}
.Controls.Right.shop:hover .ControlBag span {
  color: white;
}
.Controls {
  color: black;
  background-color: transparent;
  outline: none;
  border-radius: 0px;
  border: none;
  display: flex;
  /* width: 8%; */
  font-weight: 500;
  /* letter-spacing: 0.1vw; */
  position: absolute;
  cursor: pointer;
  /* padding: 0%; */
  /* font-size: 1.2em; */
  font-size: 15px;
  font-family: Caslon;
  padding: 7px 6px;
  /* background-color: #fcf8f0; */
  /* background-color: rgba(240, 240, 240, 1); */
  background-color: rgba(236, 231, 231, 1);
  box-sizing: border-box;
  text-align: center;
  padding-bottom: 4px;
  animation: fadeIn 2.5s;
  /* animation-delay: 10s;
  -webkit-animation-delay: 10s; */
  /* -webkit-animation-name: fadeIn;
  -webkit-animation-duration: 2s;
  -webkit-animation-delay: 10s; */
}
@keyframes fadeIn {
  0% { opacity: 0; }
  50% { opacity: 0; }
  100% { opacity: 1; }
}
.Controls:hover {
  background-color: black;
  color: white;
  transition: 0.5s ease;
}
.Controls.left {
  left: 0;
  margin-left: 2%;
  margin-top: 2%;
  /* width: 96px; */
}
.Controls.Right {
  right: 0;
  margin-right: 2%;
  margin-top: 2%;
  /* width: 74px; */
}
.Controls.Right.shop {
  right: 0;
  margin-right: 2%;
  margin-top: 2%;
  display: grid;
  display: -ms-grid;
  grid-template-columns: 75% 20%;
  grid-template-rows: auto;
  grid-column-gap: 4px;
  grid-row-gap: 0px;
  align-items: center;
  text-align: center;
  /* width: 80px; */
  width: 68px;
  height: auto;
  font-weight: 500;
  /* letter-spacing: 0.05vw; */
  justify-items: end;
  padding: 2px 6px;
}
.Controls.Right.shop:hover .ControlBag {
  background-color: black;
  color: white;
  transition: 0.5s ease;
}
.ControlBag {
  position: relative;
  /* background-color: #fcf8f0; */
  /* border: 1px solid red; */
  padding-top: auto;
  padding-bottom: auto;
  border-radius: 11px;
  margin-top: auto;
  margin-bottom: auto;
  /* width: 100%; */
  width: 92%;
  height: 84%;
}

.ControlBag img {
  position: relative;
  /* width: 19px; */
  /* padding-top: 3px; */
  padding-bottom: 2px;
  margin-left: auto;
  margin-right: auto;
}
.ControlBagSpanLessThan10 {
  position: absolute;
  right: 30%;
  top: 38%;
  height: 30%;
  width: 40%;
  font-size: 0.5em;
}
.ControlBagSpanLessThan10:hover {
  color: white;
}
.ControlBagSpanGreaterThan10 {
  position: absolute;
  right: 10%;
  top: 40%;
  height: 30%;
  width: 80%;
  font-size: 0.45em;
}
.ControlBagSpanGreaterThan10:hover {
  color: white;
}
.Controls.Right.bottom {
  bottom: 0;
  margin-bottom: 1.6%;
  /* width: 107px; */
}
.Controls.Left.bottom {
  bottom: 0;
  left: 0;
  margin-left: 2%;
  margin-bottom: 2%;
  /* width: 154px; */
}
.start {
  position: absolute;
  width: 20%;
  z-index: 999999;
  left: 0;
  color: red;
}
.Animationstart {
  font-family: Caslon;
  font-size: 1.2em;
  border-radius: 0px;
  width: auto;
  height: auto;
  position: fixed;
  color: #fff;
  background-color: #000;
  bottom: 0;
  /* margin-bottom: 35vh;
  margin-left: 8vw; */
  padding: 6px;
  padding-bottom: 4px;
  cursor: pointer;
  border: none;
  position: absolute;
  /* right: 38.75%; */
  bottom: 36%;
  left: calc(50% + 133px);
  animation: fadeIn 4.5s;
}
.Animationstart:hover {
  background-color: transparent;
  color: #000;
  border-width: 0.5px;
  border: 0.5px SOLID #000;
}

.AnimationstartMobile {
  font-family: Caslon;
  font-size: 1.2em;
  border-radius: 0px;
  width: auto;
  height: auto;
  position: fixed;
  color: #fff;
  background-color: #000;
  /* bottom: 0; */
  /* margin-bottom: 35vh;
  margin-left: 8vw; */
  padding: 6px;
  padding-bottom: 4px;
  cursor: pointer;
  border: none;
  position: absolute;
  /* right: 38.75%; */
  top: 70%;
  left: calc(50% + 100px);
  animation: fadeIn 2.5s;
}
.AnimationstartMobile:hover {
  background-color: transparent;
  color: #000;
  border-width: 0.5px;
  border: 0.5px SOLID #000;
}
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.LogoOfModular {
  position: absolute;
  bottom: 2%;
  left: 38%;
  background: white;
  border-radius: 40px;
  box-shadow: 0 0 3px rgba(0, 102, 204, 1);
  padding-left: 0;
  padding-right: 0;
  width: auto;
  min-width: 38px;
  height: 38px;
  display: flex;
  justify-content: center;
  align-items: center;
  /* transition: all 0.3s; */
  text-align: left;
  transition-timing-function: cubic-bezier(0.25, 0.1, 0.25, 1);
  z-index: 9999;
  cursor: pointer;
}
.LogoOfModular:hover .testingLogoimage {
  max-width: 50%;
  width: auto;
}
.testingLogoimage {
  /* opacity: 0; */
  width: auto;
  max-width: 50%;
  font-size: 0px;
  line-height: 12px;
  color: rgba(0, 0, 0, 0.65);
  /* transition: all 0.3s; */
  transition-timing-function: cubic-bezier(0.25, 0.1, 0.25, 1);
  text-decoration: none;
}
.LogoOfModular.full,
.LogoOfModular:hover {
  padding-left: 5px;
  padding-right: 5px;
  /* right: 22%; */
  left: 33%;
}
.LogoOfModular.full .PoweredBy,
.LogoOfModular:hover .PoweredBy {
  opacity: 1;
  font-size: 12px;
  margin-right: 5px;
  margin-top: 1px;
  max-width: 250px;
}
.LogoOfModular.full .ModularWord,
.LogoOfModular:hover .ModularWord {
  opacity: 1;
  width: 41%;
  margin-left: 3px;
}
.PoweredBy {
  opacity: 0;
  width: auto;
  max-width: 0;
  font-size: 0px;
  line-height: 12px;
  color: rgba(0, 0, 0, 0.65);
  /* transition: all 0.3s; */
  transition-timing-function: cubic-bezier(0.25, 0.1, 0.25, 1);
  text-decoration: none;
}

.testingLogo:hover .poweredby {
  opacity: 1;
  font-size: 14px;
  font: bold;
  max-width: 50px;
  width: 14%;
}
.LogoOfModular.full .PoweredBy,
.LogoOfModular:hover .PoweredBy {
  opacity: 1;
  font-size: 12px;
  margin-right: 5px;
  margin-top: 1px;
  max-width: 250px;
}
.LogoOfModular.full .ModularWord,
.LogoOfModular:hover .ModularWord {
  opacity: 1;
  width: 41%;
  margin-left: 3px;
}
.ModularWord {
  opacity: 0;
  width: 0px;
  /* transition: all 0.3s; */
  transition-timing-function: cubic-bezier(0.25, 0.1, 0.25, 1);
}
.Notification {
  position: absolute;
  display: inline;
  white-space: nowrap;
  width: auto;
  font-size: 1vw;
  right: 0;
  margin-right: 2%;
  margin-top: 2%;
  padding: 0.6%;
  background-color: #050505;
  color: #fff;
  -webkit-animation: scale-down-ver-center-noti 10s cubic-bezier(0.25, 0.46, 0.45, 0.94) both !important;
  animation: scale-down-ver-center-noti 10s cubic-bezier(0.25, 0.46, 0.45, 0.94) both !important;
}

@-webkit-keyframes scale-down-ver-center-noti {
  0% {
    -webkit-transform: scaleY(0);
    transform: scaleY(0);
  }
  100% {
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
  }
}
/* @keyframes scale-down-ver-center {
  0% {
    -webkit-transform: scaleY(0);
    transform: scaleY(0);
  }
  100% {
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
  }
} */
@media only screen and (max-width: 600px) {
  .Notification {
    position: absolute;
    display: inline;
    width: 50% !important;
    white-space: nowrap;
    width: auto;
    font-size: 1em;
    right: 0;
    left: 0;
    margin: auto;
    margin-top: 2%;
    padding: 0.6%;
    background-color: #050505;
    color: #fff;
    -webkit-animation: scale-down-ver-center-noti 10s cubic-bezier(0.25, 0.46, 0.45, 0.94) both !important;
    animation: scale-down-ver-center-noti 10s cubic-bezier(0.25, 0.46, 0.45, 0.94) both !important;
  }

  .INCORPbottomend {
    width: 20%;
    position: absolute;
    z-index: 99;
    height: 10%;
    margin-left: 40.5%;
    bottom: 50%;
    /* animation: expand 2s linear; */
    /* animation-direction: alternate; */
    -webkit-animation: expandMobile 2s cubic-bezier(0.4, 0, 1, 1) forwards;
    /* -moz-animation: expand 2s linear; */
  }
  .INCORPbottom {
    width: 20%;
    position: absolute;
    z-index: 99;
    height: 10%;
    margin-left: 40.5%;
    bottom: 50%;
    cursor: pointer;

    /* padding: 0%;  
  margin-bottom: 1%; */
    /* text-align: center;
  cursor: pointer; */

    /* animation: expand 2s linear; */
    /* animation-direction: alternate; */
    /* -webkit-animation: expand 2s cubic-bezier(0.4, 0, 1, 1) forwards; */
    /* -moz-animation: expand  2s linear  ; */
    /* animation: expand  1s ease ;
  animation-iteration-count: 1; */
    /* transition: all 1s;
  transform: translateY(500%) */
    /* -moz-animation-iteration-count: 1;
  -webkit-animation-iteration-count: 1 */
  }
  .ControlBag {
    position: relative;
    background-color: #fcf8f0;
    /* border: 1px solid red; */
    padding-top: auto;
    padding-bottom: auto;
    border-radius: 11px;
    margin-top: auto;
    margin-bottom: auto;
    width: 100%;
    margin-left: 2px;
  }
  .ControlBag img {
    position: relative;
    width: 15px;
    padding-top: 0px;
    margin-left: auto;
    margin-right: auto;
  }
  .ControlBag span {
    position: absolute;
    right: 5px;
    top: 8px;
    font-size: 11px;
  }
  .Controls.Right.shop {
    right: 0;
    margin-right: 2%;
    margin-top: 1.6%;
    display: flex;
    display: -ms-grid;
    grid-template-columns: 75% 25%;
    grid-template-rows: auto;
    grid-column-gap: 1px;
    grid-row-gap: 0px;
    align-items: center;
    text-align: center;
    width: auto;
    height: auto;
    font-weight: 500;
    /* letter-spacing: 0.05vw; */
  }
  .Controls {
    color: black;
    background-color: transparent;
    outline: none;
    border-radius: 0px;
    border: none;
    display: flex;
    font-weight: 500;
    /* letter-spacing: 0.05vw; */
    position: absolute;
    cursor: pointer;
    font-size: 1.2em;
    font-family: Caslon;
    padding: 2px 6px;
    /* background-color: rgba(240, 240, 240, 1); */
    background-color: rgba(236, 231, 231, 1);
    box-sizing: border-box;
    text-align: center;
    white-space: nowrap !important;
  }
  .FollowMobile {
    display: flex;
    align-items: center;
    text-align: center;
    width: auto;
    height: auto;
    font-weight: 500;
    position: fixed;
    /* width: 30px;
    height: 20px; */
    right: 12px;
    top: 12px;
  }
}

.CartMobileElement{
  position: absolute;
  width: 24px;
  top: 3px;
  left: calc(50% - 12px);
}

.CartMobileSpanLessThan10{
  position: absolute;
  font-size: 0.6em;
  top: 11px;
  left: calc(50% - 2.75px);
  font-family: 'Inter';
}
.CartMobileSpanGreaterThan10{
  position: absolute;
  font-size: 0.6em;
  top: 12px;
  left: calc(50% - 5px);
  font-family: 'Inter';
}