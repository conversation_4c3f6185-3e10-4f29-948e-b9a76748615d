// src/App.js
import React, { Component } from "react";
import "./GlobalUI.css";
import ProductDetail from "./ProductDetails/ProductDetails";
import AboutPage from "./AboutPage/AboutPage";
import Shop from "./Shop/Shop";
import ContactPage from "./ContactPage/ContactPage";
import BodyMap from "./BodyMap/BodyMap";
import MobileManu from "./MobileManu/MobileManu";
import MobileShop from "./MobileShop/MobileShop";
import ProductDetailsMobile from "./ProductDetails/ProductDetailsMobile";
import ProductDetailsContentMobile from "./ProductDetails/ProductDetailsContentMobile";
import ToolCustomisation from "./ProductDetails/ToolCustomisation";
import AnnotationsBackground from "./ProductDetails/AnnotationsBackground"
import AnnotationsContentMobile from "./ProductDetails/AnnotationsContentMobile"
import AnnotationsMobile from "./ProductDetails/AnnotationsMobile";
import MobileAboutPage from "./MobileAboutPage/MobileAboutPage"
import NewMobileManu from "./NewMobileManu/NewMobileManu";
import MobileNewCart from "./MobileNewCart/MobileNewCart"
class GlobalUI extends Component {
  constructor(props) {
    super(props);
    this.StartAnimation = this.props.StartAnimation.bind(this);
    this.AboutAnimation = this.props.AboutAnimation.bind(this);
    this.ShopAnimation = this.props.ShopAnimation.bind(this);
  }
  state = {
    colors: ["Black", "Blue", "Orange", "Silver", "Red"],
    currentColor: "",
    AboutPageDisplay: "none",
    ShopPageDisplay: "none",
    ContactPageDisplay: "none",
    ShopImageSRC: "picto-shoppingBag.svg",
    ShopImageSRCMobile: "shoppingBag.png",
    MobileProductDetailsDisplay: "",
    ProductDetailsMobileDisplay: "",
    ToolCustomisationDisplay: "none",
    AnnotationsBackgroundDisplay: "none",
    ProductDetailsMobileContentDisplay: "none",
    AnnotationsMobileContentDisplay: "none",
    PDMAnimation: "fadeIn 0.5s",
    ToolsCustAnimation: "fadeIn 0.5s",
    AnnotationsMobileDisplay: "",
    ibigcounter: 0,
    ismallcounter: 0,
    youbigcounter: 0,
    yousmallcounter: 0,
    webigcounter: 0,
    wesmallcouner: 0,
    Obsidiancounter: 0,
    Opalcounter: 0,
    Tigercounter: 0,
    productTitleOpacity: 1,
    productTitleAnimation: "fadeIn 0.5s",
  };
  // ibigcounter={this.state.ibigcounter}
  // ismallcounter={this.state.ismallcounter}
  // youbigcounter={this.state.youbigcounter}
  // yousmallcounter={this.state.yousmallcounter}
  // webigcounter={this.state.webigcounter}
  // wesmallcouner={this.state.wesmallcouner}
  // Obsidiancounter={this.state.Obsidiancounter}
  // Opalcounter={this.state.Opalcounter}

  // setibigcounter={this.setibigcounter}
  // setismallcounter={this.setismallcounter}
  // setyoubigcounter={this.setyoubigcounter}
  // setyousmallcounterl={this.setyousmallcounterl}
  // setwewebigcounter={this.setwewebigcounter}
  // setwesmallcounerl={this.setwesmallcounerl}
  // setObsidiancounter={this.setObsidiancounter}
  // setOpalcounter={this.setOpalcounter}

  // setibigcounterdecrease={this.setibigcounterdecrease}
  // setismallcounterdecrease{this.setismallcounterdecrease}
  // setyoubigcounterdecrease{this.setyoubigcounterdecrease}
  // setyousmallcounterldecrease{this.setyousmallcounterldecrease}
  // setwewebigcounterdecrease{this.setwewebigcounterdecrease}
  // setwesmallcounerldecrease{this.setwesmallcounerldecrease}
  // setObsidiancounterdecrease{this.setObsidiancounterdecrease}
  // setOpalcounterdecrease{this.setOpalcounterdecrease}
  // const [ibigcounter, setibigcounter, ibigcounterref] = useState(0);
  // const [ismallcounter, setismallcounter, ismallcounterref] = useState(0);
  // const [youbigcounter, setyoubigcounter, youbigcounterref] = useState(0);
  // const [yousmallcounter, setyousmallcounterl, yousmallcounterref] = useState(0);
  // const [webigcounter, setwewebigcounter, webigcounterref] = useState(0);
  // const [wesmallcouner, setwesmallcounerl, wesmallcounerref] = useState(0);
  // const [Obsidiancounter, setObsidiancounter, Obsidiancounterref] = useState(0);
  // const [Opalcounter, setOpalcounter, Opalcounterref] = useState(0);
  setibigcounterdecrease = () => {
    // console.log(this.props.PDITitle, "this.props.PDITitle");
    if (this.props.PDITitle == "I") {
      if (this.state.ibigcounter - 1 >= 0) {
        this.setState({
          ibigcounter: this.state.ibigcounter - 1,
        });
      }
    } else if (this.props.PDITitle == "YOU") {
      if (this.state.youbigcounter - 1 >= 0) {
        this.setState({
          youbigcounter: this.state.youbigcounter - 1,
        });
      }
    } else if (this.props.PDITitle == "WE") {
      if (this.state.youbigcounter - 1 >= 0) {
        this.setState({
          webigcounter: this.state.youbigcounter - 1,
        });
      }
    }
  };

  setShopDisplay = () => {
    this.setState({ ShopPageDisplay: "" });
  }

  goToProduct = (product) => {
    if (this.props.SceneStatus == "INSIDE"){
      let event = new CustomEvent("goToProductFromInside", {detail: product});
      window.dispatchEvent(event);
      this.setState({ ShopPageDisplay: "none" });
    } else{
      let event = new CustomEvent("goToProductFromOutside", {detail: product});
      window.dispatchEvent(event);
      this.setState({ ShopPageDisplay: "none" });
    }
  }

  setibigcounter = () => {
    if (this.props.PDITitle == "I") {
      this.setState({
        ibigcounter: this.state.ibigcounter + 1,
      });
    } else if (this.props.PDITitle == "YOU") {
      this.setState({
        youbigcounter: this.state.youbigcounter + 1,
      });
    } else if (this.props.PDITitle == "WE") {
      this.setState({
        webigcounter: this.state.webigcounter + 1,
      });
    }
  };

  setismallcounterdecrease = () => {
    if (this.props.PDITitle == "I") {
      if (this.state.ismallcounter - 1 >= 0) {
        this.setState({
          ismallcounter: this.state.ismallcounter - 1,
        });
      }
    } else if (this.props.PDITitle == "YOU") {
      if (this.state.yousmallcounter - 1 >= 0) {
        this.setState({
          yousmallcounter: this.state.yousmallcounter - 1,
        });
      }
    } else if (this.props.PDITitle == "WE") {
      if (this.state.wesmallcouner - 1 >= 0) {
        this.setState({
          wesmallcouner: this.state.wesmallcouner - 1,
        });
      }
    }
  };

  setismallcounter = () => {
    if (this.props.PDITitle == "I") {
      this.setState({
        ismallcounter: this.state.ismallcounter + 1,
      });
    } else if (this.props.PDITitle == "YOU") {
      this.setState({
        yousmallcounter: this.state.yousmallcounter + 1,
      });
    } else if (this.props.PDITitle == "WE") {
      this.setState({
        wesmallcouner: this.state.wesmallcouner + 1,
      });
    }
  };

  setyoubigcounterdecrease = () => {
    this.setState({
      youbigcounter: this.state.youbigcounter - 1,
    });
  };
  setyousmallcounterldecrease = () => {
    this.setState({
      yousmallcounter: this.state.yousmallcounter - 1,
    });
  };
  setwewebigcounterdecrease = () => {
    this.setState({
      webigcounter: this.state.webigcounter - 1,
    });
  };
  setwesmallcounerldecrease = () => {
    this.setState({
      wesmallcouner: this.state.wesmallcouner - 1,
    });
  };
  setObsidiancounterdecrease = () => {
    if (this.state.Obsidiancounter - 1 >= 0) {
      this.setState({
        Obsidiancounter: this.state.Obsidiancounter - 1,
      });
    }
  };
  setOpalcounterdecrease = () => {
    if (this.state.Opalcounter - 1 >= 0) {
      this.setState({
        Opalcounter: this.state.Opalcounter - 1,
      });
    }
  };
  setTigercounterdecrease = () => {
    if (this.state.Tigercounter - 1 >= 0) {
      this.setState({
        Tigercounter: this.state.Tigercounter - 1,
      });
    }
  };

  setyoubigcounter = () => {
    this.setState({
      youbigcounter: this.state.youbigcounter + 1,
    });
  };
  setyousmallcounterl = () => {
    this.setState({
      yousmallcounter: this.state.yousmallcounter + 1,
    });
  };
  setwewebigcounter = () => {
    this.setState({
      webigcounter: this.state.webigcounter + 1,
    });
  };
  setwesmallcounerl = () => {
    this.setState({
      wesmallcouner: this.state.wesmallcouner + 1,
    });
  };
  setObsidiancounter = () => {
    if (
      this.state.ibigcounter +
      this.state.ismallcounter +
      this.state.youbigcounter +
      this.state.webigcounter +
      this.state.wesmallcouner +
      this.state.yousmallcounter -
      (this.state.Obsidiancounter + this.state.Opalcounter) >
      0
    ) {
      this.setState({
        Obsidiancounter: this.state.Obsidiancounter + 1,
      });
    }
  };
  setOpalcounter = () => {
    if (
      this.state.ibigcounter +
      this.state.ismallcounter +
      this.state.youbigcounter +
      this.state.webigcounter +
      this.state.wesmallcouner +
      this.state.yousmallcounter -
      (this.state.Obsidiancounter + this.state.Opalcounter) >
      0
    ) {
      this.setState({
        Opalcounter: this.state.Opalcounter + 1,
      });
    }
  };

  AboutPageDisplaySwitch = (action) => {
    if (this.props.SceneStatus == "OUTSIDE") {
      this.setState({
        ShopPageDisplay: "none",
        ContactPageDisplay: "none",
        MobileProductDetailsDisplay: "none",
      });
      this.AboutAnimation();
      setTimeout(() => {
        setTimeout(() => {
          if (action == "open") {
            this.setState({
              AboutPageDisplay: "",
            });
          } else if (action == "close") {
            this.setState({ AboutPageDisplay: "none" });
          }
        }, this.props.cameraAnimationEndTimeout);
      }, 500);
    } else {
      this.setState({
        ShopPageDisplay: "none",
        ContactPageDisplay: "none",
        MobileProductDetailsDisplay: "none",
      });
      this.AboutAnimation();
      setTimeout(() => {
        setTimeout(() => {
          if (action == "open") {
            this.setState({
              AboutPageDisplay: "",
            });
          } else if (action == "close") {
            this.setState({ AboutPageDisplay: "none" });
          }
        }, 4800);
      }, 500);
    }
  };
  AboutPageDisplaySwitchMobile = (action) => {

    if (action === "open"){
      this.setState({
        ShopPageDisplay: "none",
        ContactPageDisplay: "none",
        MobileProductDetailsDisplay: "none",
        AboutPageDisplay: "",
      });
    } else if (action === "close"){
      this.setState({
        AboutPageDisplay: "none",
        MobileProductDetailsDisplay: "",
      });
    }
    
  }


  ShopPageDisplaySwitch = (action) => {
    // this.ShopAnimation();

    if (action == "open") {
      // document.getElementsByClassName("App")[0].style.cursor = "default";
      document.getElementsByClassName("INCORPbottomend")[0].style.cursor = "grab";
      if (this.props.SceneStatus == "OUTSIDE") {
        this.setState({
          ShopPageDisplay: "",
          // AboutPageDisplay: "none",
          ContactPageDisplay: "none",
          MobileProductDetailsDisplay: "none",
        });
      } else {
        if (window.innerWidth > 601) {
          this.props.Productsanimation();
        } else {
          this.setState({PDMAnimation: "fadeIn 0.5s"})
        } 
        this.setState({
          AboutPageDisplay: "none",
          MobileProductDetailsDisplay: "",
        });
        setTimeout(() => {
          this.setState({
            ShopPageDisplay: "",
            ContactPageDisplay: "none",
          });
        }, 500);
      }
    } else if (action == "close") {
      if (window.innerWidth < 601) {
        this.setState({PDMAnimation: "fadeOut 0.5s"})
        setTimeout(() => {
          this.setState({ ShopPageDisplay: "none" });
        }, 500);
      } else{
        this.setState({ ShopPageDisplay: "none" });
      }
      if (this.props.SceneStatus == "OUTSIDE") {
        // document.getElementsByClassName("App")[0].style.cursor = "default";
        document.getElementsByClassName("INCORPbottomend")[0].style.cursor = "grab";
      } else {
        document.getElementsByClassName("App")[0].style.cursor = "grab";
      }
    }
  };

  ShopPageDisplaySwitchSpecial = (action) => {
    // this.ShopAnimation();
    let event = new CustomEvent("unhide3D", {
    });
    window.dispatchEvent(event);

    if (action == "open") {
      if (this.state.ShopPageDisplay === "none"){
        this.setState({PDMAnimation: "fadeIn 0.5s"})
        this.setState({
          AboutPageDisplay: "none",
          MobileProductDetailsDisplay: "",
        });
        setTimeout(() => {
          this.setState({
            ShopPageDisplay: "",
            ContactPageDisplay: "none",
          });
          this.ContactPageDisplaySwitch("close");
        }, 500);
      } else {
        this.setState({PDMAnimation: "fadeOut 0.5s"})
        setTimeout(() => {
          this.setState({ ShopPageDisplay: "none" });
        }, 500);
      }
    } 
  };
  ContactPageDisplaySwitch = (action) => {
    if (this.props.SceneStatus == "OUTSIDE") {
      if (action == "open") {
        this.setState({
          ContactPageDisplay: "",
          AboutPageDisplay: "none",
          ShopPageDisplay: "none",
          MobileProductDetailsDisplay: "none",
        });
        this.props.BodyMapDisplaySwitcher("Mind");
        this.props.BodyMapDisplaySwitcher("Truth");
        this.props.BodyMapDisplaySwitcher("Heart");
        this.props.BodyMapDisplaySwitcher("Center");
        this.props.BodyMapDisplaySwitcher("Relief");
        this.props.BodyMapDisplaySwitcher("Energize");
        this.props.BodyMapDisplaySwitcher("Ground");
      } else if (action == "close") {
        this.setState({ ContactPageDisplay: "none" });
      }
    } else {
      if (action == "open") {
        this.setState({
          AboutPageDisplay: "none",
          ShopPageDisplay: "none",
          MobileProductDetailsDisplay: "none",
        });
        this.props.Contactanimation("open");
        setTimeout(() => {
          setTimeout(() => {
            this.setState({
              ContactPageDisplay: "",
            });
            document.getElementsByClassName("Controls Right shop")[0].style.display = "none";
            document.getElementsByClassName("Controls Right bottom")[0].style.display = "none";
            document.getElementsByClassName("Controls Left bottom")[0].style.display = "none";
            document.getElementsByClassName("Controls left")[0].style.display = "none";
            document.getElementsByClassName("INCORPbottomend")[0].style.display = "none";
          }, 750);
        }, 1000);
      } else if (action == "close") {
        this.props.Contactanimation("close");
        this.setState({ ContactPageDisplay: "none" });
        setTimeout(() => {
          this.setState({
            MobileProductDetailsDisplay: "",
          });
        }, 500);
        if (document.getElementsByClassName("Controls Right shop")[0] != null){
          document.getElementsByClassName("Controls Right shop")[0].style.display = "";
        }
        if (document.getElementsByClassName("Controls Right bottom")[0] != null){
          document.getElementsByClassName("Controls Right bottom")[0].style.display = "";
        }
        if (document.getElementsByClassName("Controls Left bottom")[0] != null){
          document.getElementsByClassName("Controls Left bottom")[0].style.display = "";
        }
        if (document.getElementsByClassName("Controls left")[0] != null){
          document.getElementsByClassName("Controls left")[0].style.display = "";
        }
        if (document.getElementsByClassName("INCORPbottomend")[0] != null){
          document.getElementsByClassName("INCORPbottomend")[0].style.display = "";
        }
      }
    }
  };

  downloadimage = () => {
    var link = document.createElement("a");
    link.download = "Body-Map.png";
    link.href = "bodymap.jpg";
    link.click();
  };
  Changerimg = (target) => {
    if (target == "white") {
      this.setState({ ShopImageSRC: "picto-shoppingBag2.svg" });
    } else if (target == "black") {
      this.setState({ ShopImageSRC: "picto-shoppingBag.svg" });
    }
  };
  BackBUttonPressed = () => {
    // console.log("backbutton");
    if (this.state.AboutPageDisplay === "") {
      this.props.Heartback();
    }
    this.setState({
      ContactPageDisplay: "none",
      AboutPageDisplay: "none",
      ShopPageDisplay: "none",
      MobileProductDetailsDisplay: "",
    });
  };

  showProductDetailMobile = (action) => {
    if (action === "show") {
      this.setState({
        PDMAnimation: "fadeIn 0.5s",
        ProductDetailsMobileContentDisplay: "",
      });
    } else if (action === "hide") {
      this.setState({
        PDMAnimation: "fadeOut 0.5s",
      });
      setTimeout(() => {
        this.setState({ ProductDetailsMobileContentDisplay: "none" })
      }, 500);
    }
  }
  

  showAnnotationsMobile = (action) => {
    if (action === "show") {
      this.setState({
        PDMAnimation: "fadeIn 0.5s",
        AnnotationsMobileContentDisplay: "",
      });
    } else if (action === "hide") {
      this.setState({
        PDMAnimation: "fadeOut 0.5s",
      });
      setTimeout(() => {
        this.setState({ AnnotationsMobileContentDisplay: "none" })
      }, 500);
    }
  }


  // componentDidMount = () =>{
  //   if(this.props.SceneStatus == "INSIDE"){
  //    this.setState({
  //     MobileProductDetailsDisplay: ""
  //    }) 
  //   }else if (this.props.SceneStatus == "OUTSIDE"){
  //     this.setState({
  //       MobileProductDetailsDisplay: "none"
  //      }) 
  //   }
  // }

  ToolsCustAnimationShow = () =>{
    // this.setState({
    //   ToolsCustAnimation: "fadeIn 0.5s",
    //   ToolCustomisationDisplay: "",
    //   productTitleAnimation: "fadeOut 0.5s",
    // });
    // setTimeout(() => {
    //   this.setState({ productTitleOpacity: 0 })
    // }, 500);
  }

  ToolsCustAnimationHide = () =>{
    this.setState({
      productTitleAnimation: "fadeIn 0.5s",
      productTitleOpacity: 1,
      ToolsCustAnimation: "fadeOut 0.5s",
    });
    setTimeout(() => {
      this.setState({ ToolCustomisationDisplay: "none" })
    }, 500);
  }

  AnnotationBackgroundMobileShow = () =>{
    this.setState({
      PDMAnimation: "fadeIn 0.5s",
      AnnotationsBackgroundDisplay: "",
    });
  }

  AnnotationBackgroundMobileHide = () =>{
    this.setState({
      PDMAnimation: "fadeOut 0.5s",
    });
    setTimeout(() => {
      this.setState({ AnnotationsBackgroundDisplay: "none" })
    }, 500);
    let event = new CustomEvent("CloseAnnotationCardsMobile", {});
    window.dispatchEvent(event);
  }

  componentDidMount() {
    window.addEventListener("AnnotationBackgroundMobileShow", this.AnnotationBackgroundMobileShow);
    window.addEventListener("ToolsCustAnimationShow", this.ToolsCustAnimationShow);
    window.addEventListener("ToolsCustAnimationHide", this.ToolsCustAnimationHide);
  }
  render() {
    return (
      <div className="ButtonsUI">
        <div
          className="INCORPbottom"
          onClick={() => {
            this.setState({
              AboutPageDisplay: "none",
              ShopPageDisplay: "none",
              ContactPageDisplay: "none",
            });
            this.props.Homeanimation();
          }}>
          <img src="kiralogotest.png" />
        </div>
        <div className="incorplogoimage" style={{ display: "none" }}>
          <img
            style={{
              position: "absolute",
              top: "4.20%",
              height: "auto",
              height: "10.8vh",
            }}
            src="logo.png"
          />
        </div>
        <div>
          {window.innerWidth < 601 && this.props.SceneStatus == "INSIDE" ? (
            <ProductDetailsMobile
              ProductDetailsMobileDisplay={this.state.ProductDetailsMobileDisplay}
              showProductDetailMobile={this.showProductDetailMobile}
            />
          ) :
            (<div></div>
            )}
            {window.innerWidth < 601 && this.props.SceneStatus == "INSIDE" ? (
            <AnnotationsBackground
            AnnotationsBackgroundDisplay={this.state.AnnotationsBackgroundDisplay}
            AnnotationBackgroundMobileHide={this.AnnotationBackgroundMobileHide}
            PDMAnimation={this.state.PDMAnimation}
            />
          ) :
            (<div></div>
            )}
            {window.innerWidth < 601 && this.props.SceneStatus == "INSIDE" ? (
            <ToolCustomisation
              ToolCustomisationDisplay={this.state.ToolCustomisationDisplay}
              ToolsCustAnimation={this.state.ToolsCustAnimation}
              ToolsCustAnimationShow={this.ToolsCustAnimationShow}
              ToolsCustAnimationHide={this.ToolsCustAnimationHide}
            />
          ) :
            (<div></div>
            )}
          {window.innerWidth < 601 && this.props.SceneStatus == "INSIDE" ? (
            <ProductDetailsContentMobile
              ProductDetailsMobileContentDisplay={this.state.ProductDetailsMobileContentDisplay}
              PDITitle={this.props.PDITitle}
              PDSound={this.props.PDSound}
              PDICardLine={this.props.PDICardLine}
              PDIDescription={this.props.PDIDescription}
              QuantityLimitedStatment={this.props.QuantityLimitedStatment}
              QuantityLimitedMob1Statment={this.props.QuantityLimitedMob1Statment}
              QuantityLimitedMob2Statment={this.props.QuantityLimitedMob2Statment}
              showProductDetailMobile={this.showProductDetailMobile}
              PDMAnimation={this.state.PDMAnimation}
              currentModel={this.props.currentModel}
            />) :
            (<div></div>
            )}
          {window.innerWidth < 601 && this.props.SceneStatus == "INSIDE" ? (
            <AnnotationsMobile
              AnnotationsMobileDisplay={this.props.AnnotationsMobileDisplay}
              InfoImageSRC={this.props.InfoImageSRC}
              ProductInfoSwitcher={this.props.ProductInfoSwitcher}
              ProductInfoSwitcherMobile={this.props.ProductInfoSwitcherMobile}
            />) :
            (<div></div>
            )}
          {window.innerWidth < 601 && this.props.SceneStatus == "INSIDE" ? (
            <AnnotationsContentMobile
              AnnotationsMobileContentDisplay={this.state.AnnotationsMobileContentDisplay}
              showAnnotationsMobile={this.showAnnotationsMobile}
              PDMAnimation={this.state.PDMAnimation}
            />) :
            (<div></div>
            )}
        </div>
        {window.innerWidth < 601 && this.props.SceneStatus == "INSIDE" ? (
          <div >
              <NewMobileManu
                totalitems={this.props.totalitems}
                ShopPageDisplaySwitch={this.ShopPageDisplaySwitch}
                AboutPageDisplaySwitch={this.AboutPageDisplaySwitch}
                ContactPageDisplaySwitch={this.ContactPageDisplaySwitch}
                StartAnimation={this.props.StartAnimation}
                AboutPageDisplaySwitchMobile={this.AboutPageDisplaySwitchMobile}
              />
              <div className="CartMobileElement" style={{animation: "fadeIn 1.5s"}} onClick={() => this.ShopPageDisplaySwitchSpecial("open")}>
                <img style={{width: "24px"}} src={this.state.ShopImageSRCMobile} />
                {this.props.totalitems < 10 ? (
                  <span className="CartMobileSpanLessThan10">
                    {this.props.totalitems}
                  </span>
                ) : (
                  <span className="CartMobileSpanGreaterThan10">
                    {this.props.totalitems}
                  </span>
                )}
              </div>
          <a
              className="Controls"
              style={{right: "10px", top: "10px", padding: "2px", animation: "fadeIn 1.5s"}}
              href="https://www.instagram.com/incorp.world/"
              target="_blank"
              >
              <a style={{ height: "11px", fontSize: "0.8em" }}>FOLLOW</a>
            </a>
          </div>
            ) :
            (<div></div>
            )}
        {window.innerWidth > 601 ? (
          <div style={{ display: this.props.menuButtonsDisplay }}>
            <button
              style={{ display: "none" }}
              className="Controls left"
              onClick={() => this.AboutPageDisplaySwitch("open")}>
              <a style={{ height: "14px" }}>ABOUT</a>
            </button>
            <button
              style={{ display: "none" }}
              className="Controls Right shop"
              onClick={() => this.ShopPageDisplaySwitch("open")}
              onMouseEnter={() => this.Changerimg("white")}
              onMouseLeave={() => this.Changerimg("black")}>
              <a style={{ height: "11px" }}>SHOP</a>
              <div className="ControlBag">
                <img src={this.state.ShopImageSRC} />
                {this.props.totalitems < 10 ? (
                  <span className="ControlBagSpanLessThan10">
                    {this.props.totalitems}
                  </span>
                ) : (
                  <span className="ControlBagSpanGreaterThan10">
                    {this.props.totalitems}
                  </span>
                )}
              </div>
            </button>
            {/* <div className="Controls Right shop">
          <button className="ControlsShop" onClick={() => this.ShopPageDisplaySwitch("open")}>
            SHOP
          </button>
          <div className="ControlBag">
            <img src="picto-shoppingBag.svg" />
            <span>{this.props.totalitems}</span>
          </div>
            </div> */}

            <button
              style={{ display: "none" }}
              className="Controls Left bottom"
              onClick={() => this.ContactPageDisplaySwitch("open")}>
              <a style={{ height: "14px" }}>CONTACT</a>
            </button>
            {/* {window.location.replace('https://www.modularcx.co.uk/')}} "} */}
            <a
              style={{ display: "none" }}
              className="Controls Right bottom"
              href="https://www.instagram.com/incorp.world/"
              target="_blank">
              <a style={{ height: "14px" }}>FOLLOW</a>
            </a>
          </div>
        ) : (
            <div></div>
        )}
        {window.innerWidth > 601 ? (
          <button
            className="Animationstart"
            onClick={this.props.StartAnimation}
            style={{ display: this.props.StartDisplay }}>
            Enter
          </button>) : (
          <button
            className="AnimationstartMobile"
            onClick={this.props.StartAnimation}
            style={{ display: this.props.StartDisplay }}>
            Enter
          </button>
        )}
        {/* <button className="downloadmap" onClick={() => this.downloadimage()}>
          <img src="d1.png" />
          <span className="download">DOWNLOAD BODY MAP</span>
        </button> */}
        {/* <div
          className="LogoOfModular"
          onClick={() => {
            window.open("https://www.modularcx.co.uk/", "_blank");
          }}
        >
          <span className="PoweredBy"> Powered by </span>
          <img className="testingLogoimage" src="https://i.ibb.co/cwRgcR9/modular-1x1-logo-web-001-32x32.jpg"></img>
          <img className="ModularWord" src="https://i.ibb.co/pbxCwwn/Rsz-m-lock-up-1x6-2.jpg"></img>
        </div> */}
        <div>
          <ProductDetail
            ProductDisplaySwitch={this.props.ProductDisplaySwitch}
            ProductDisplayBottleSwitch={this.props.ProductDisplayBottleSwitch}
            ProductDetailsDisplay={this.props.ProductDetailsDisplay}
            ProductDetailsBottlecupDisplay={
              this.props.ProductDetailsBottlecupDisplay
            }
            ProductDetailsBottleToolDisplay={
              this.props.ProductDetailsBottleToolDisplay
            }
            ProductDetailsBottleDisplay={this.props.ProductDetailsBottleDisplay}
            ProductDetailsBottleSmallDisplay={
              this.props.ProductDetailsBottleSmallDisplay
            }
            ProductDetailsBottleSmallBodyDisplay={
              this.props.ProductDetailsBottleSmallBodyDisplay
            }
            SetProductCounter={this.SetProductCounter}
            P1Big={this.props.P1Big}
            P1Small={this.props.P1Small}
            P2Big={this.props.P2Big}
            P2Small={this.props.P2Small}
            P3Big={this.props.P3Big}
            P3Small={this.props.P3Small}
            SetProductCounterP1Big={this.props.SetProductCounterP1Big}
            SetProductCounterP1Small={this.props.SetProductCounterP1Small}
            addVariantToCart={this.props.addVariantToCart}
            checkout={this.props.checkout}
            removeLineItemInCart={this.props.removeLineItemInCart}
            updateLineItemInCart={this.props.updateLineItemInCart}
            isCartOpen={this.props.isCartOpen}
            handleCartClose={this.props.handleCartClose}
            customerAccessToken={this.props.customerAccessToken}
            idofmodelbig={this.props.idofmodelbig}
            idofmodelsmall={this.props.idofmodelsmall}
            BottleDivDisplay={this.props.BottleDivDisplay}
            PDITitle={this.props.PDITitle}
            QuantityLimitedStatment={this.props.QuantityLimitedStatment}
            PDSound={this.props.PDSound}
            PDICardLine={this.props.PDICardLine}
            PDIDescription={this.props.PDIDescription}
            annotationToggle={this.props.annotationToggle}
            SceneStatus={this.props.SceneStatus}
            MobileProductDetailsDisplay={this.state.MobileProductDetailsDisplay}
            ibigcounter={this.state.ibigcounter}
            ismallcounter={this.state.ismallcounter}
            youbigcounter={this.state.youbigcounter}
            yousmallcounter={this.state.yousmallcounter}
            webigcounter={this.state.webigcounter}
            wesmallcouner={this.state.wesmallcouner}
            Obsidiancounter={this.state.Obsidiancounter}
            Opalcounter={this.state.Opalcounter}
            Tigercounter={this.state.Tigercounter}
            setibigcounter={this.setibigcounter}
            setismallcounter={this.setismallcounter}
            setyoubigcounter={this.setyoubigcounter}
            setyousmallcounterl={this.setyousmallcounterl}
            setwewebigcounter={this.setwewebigcounter}
            setwesmallcounerl={this.setwesmallcounerl}
            setObsidiancounter={this.setObsidiancounter}
            setOpalcounter={this.setOpalcounter}
            setibigcounterdecrease={this.setibigcounterdecrease}
            setismallcounterdecrease={this.setismallcounterdecrease}
            setyoubigcounterdecrease={this.setyoubigcounterdecrease}
            setyousmallcounterldecrease={this.setyousmallcounterldecrease}
            setwewebigcounterdecrease={this.setwewebigcounterdecrease}
            setwesmallcounerldecrease={this.setwesmallcounerldecrease}
            setObsidiancounterdecrease={this.setObsidiancounterdecrease}
            setOpalcounterdecrease={this.setOpalcounterdecrease}
            setTigercounterdecrease={this.setTigercounterdecrease}
            Productsanimation={this.props.Productsanimation}
            setShopDisplay={this.setShopDisplay}
            InfoImageSRC={this.props.InfoImageSRC}
            ProductInfoSwitcher={this.props.ProductInfoSwitcher}
            Changerimg={this.props.Changerimg}
            bodyUnderline={this.props.bodyUnderline}
            bodySmallUnderline={this.props.bodySmallUnderline}
            toolObsidian={this.props.toolObsidian}
            toolOpal={this.props.toolOpal}
            toolTiger={this.props.toolTiger}
            ShopPageDisplaySwitchSpecial={this.ShopPageDisplaySwitchSpecial}
            productTitleOpacity={this.state.productTitleOpacity}
            productTitleAnimation={this.state.productTitleAnimation}
            handImage33ml={this.props.handImage33ml}
            handImage5ml={this.props.handImage5ml}
            handImagetool={this.props.handImagetool}
            currentModel={this.props.currentModel}
            showProductDetailMobile={this.showProductDetailMobile}
          />
        </div>
        <div style={{ display: this.state.AboutPageDisplay }}>
          {window.innerWidth > 601 ?
            <AboutPage
              AboutPageDisplaySwitch={this.AboutPageDisplaySwitch}
              BackBUttonPressed={this.BackBUttonPressed}
              updateXY={this.props.updateXY}
            />
            :
            <MobileAboutPage />
          }
        </div>
        <div style={{ display: this.state.ShopPageDisplay }}>
          {window.innerWidth > 601 ? (
            <Shop
              ibigcounter={this.state.ibigcounter}
              ismallcounter={this.state.ismallcounter}
              youbigcounter={this.state.youbigcounter}
              yousmallcounter={this.state.yousmallcounter}
              webigcounter={this.state.webigcounter}
              wesmallcouner={this.state.wesmallcouner}
              Obsidiancounter={this.state.Obsidiancounter}
              Opalcounter={this.state.Opalcounter}
              Tigercounter={this.state.Tigercounter}
              shopData={this.props.shopData}
              totalitems={this.props.totalitems}
              ShopPageDisplaySwitch={this.ShopPageDisplaySwitch}
              BackBUttonPressed={this.BackBUttonPressed}
              SetProductCounter={this.SetProductCounter}
              ProductCounter={this.state.ProductCounter}
              addVariantToCart={this.props.addVariantToCart}
              checkout={this.props.checkout}
              removeLineItemInCart={this.props.removeLineItemInCart}
              updateLineItemInCart={this.props.updateLineItemInCart}
              isCartOpen={this.props.isCartOpen}
              handleCartClose={this.props.handleCartClose}
              customerAccessToken={this.props.customerAccessToken}
              setibigcounter={this.setibigcounter}
              setismallcounter={this.setismallcounter}
              setyoubigcounter={this.setyoubigcounter}
              setyousmallcounterl={this.setyousmallcounterl}
              setwewebigcounter={this.setwewebigcounter}
              setwesmallcounerl={this.setwesmallcounerl}
              setObsidiancounter={this.setObsidiancounter}
              setOpalcounter={this.setOpalcounter}
              setibigcounterdecrease={this.setibigcounterdecrease}
              setismallcounterdecrease={this.setismallcounterdecrease}
              setyoubigcounterdecrease={this.setyoubigcounterdecrease}
              setyousmallcounterldecrease={this.setyousmallcounterldecrease}
              setwewebigcounterdecrease={this.setwewebigcounterdecrease}
              setwesmallcounerldecrease={this.setwesmallcounerldecrease}
              setObsidiancounterdecrease={this.setObsidiancounterdecrease}
              setOpalcounterdecrease={this.setOpalcounterdecrease}
              setTigercounterdecrease={this.setTigercounterdecrease}
              updateLineItemInCartLessDesktop={this.props.updateLineItemInCartLessDesktop}
              goToProduct={this.goToProduct}
              updateLineItemInCartAddMobile={this.props.updateLineItemInCartAddMobile}
              updateLineItemInCartLessMobile={this.props.updateLineItemInCartLessMobile}

            />

          ) : (
            // <MobileShop
            //   shopData={this.props.shopData}
            //   totalitems={this.props.totalitems}
            //   ShopPageDisplaySwitch={this.ShopPageDisplaySwitch}
            //   BackBUttonPressed={this.BackBUttonPressed}
            //   SetProductCounter={this.SetProductCounter}
            //   ProductCounter={this.state.ProductCounter}
            //   addVariantToCart={this.props.addVariantToCart}
            //   checkout={this.props.checkout}
            //   removeLineItemInCart={this.props.removeLineItemInCart}
            //   updateLineItemInCart={this.props.updateLineItemInCart}
            //   isCartOpen={this.props.isCartOpen}
            //   handleCartClose={this.props.handleCartClose}
            //   customerAccessToken={this.props.customerAccessToken}
            //   ibigcounter={this.state.ibigcounter}
            //   ismallcounter={this.state.ismallcounter}
            //   youbigcounter={this.state.youbigcounter}
            //   yousmallcounter={this.state.yousmallcounter}
            //   webigcounter={this.state.webigcounter}
            //   wesmallcouner={this.state.wesmallcouner}
            //   Obsidiancounter={this.state.Obsidiancounter}
            //   Opalcounter={this.state.Opalcounter}
            //   setibigcounter={this.setibigcounter}
            //   setismallcounter={this.setismallcounter}
            //   setyoubigcounter={this.setyoubigcounter}
            //   setyousmallcounterl={this.setyousmallcounterl}
            //   setwewebigcounter={this.setwewebigcounter}
            //   setwesmallcounerl={this.setwesmallcounerl}
            //   setObsidiancounter={this.setObsidiancounter}
            //   setOpalcounter={this.setOpalcounter}
            //   setibigcounterdecrease={this.setibigcounterdecrease}
            //   setismallcounterdecrease={this.setismallcounterdecrease}
            //   setyoubigcounterdecrease={this.setyoubigcounterdecrease}
            //   setyousmallcounterldecrease={this.setyousmallcounterldecrease}
            //   setwewebigcounterdecrease={this.setwewebigcounterdecrease}
            //   setwesmallcounerldecrease={this.setwesmallcounerldecrease}
            //   setObsidiancounterdecrease={this.setObsidiancounterdecrease}
            //   setOpalcounterdecrease={this.setOpalcounterdecrease}
            // />
            <MobileNewCart
              shopData={this.props.shopData}
              totalitems={this.props.totalitems}
              ShopPageDisplaySwitch={this.ShopPageDisplaySwitch}
              BackBUttonPressed={this.BackBUttonPressed}
              SetProductCounter={this.SetProductCounter}
              ProductCounter={this.state.ProductCounter}
              addVariantToCart={this.props.addVariantToCart}
              checkout={this.props.checkout}
              removeLineItemInCart={this.props.removeLineItemInCart}
              updateLineItemInCart={this.props.updateLineItemInCart}
              updateLineItemInCartAddMobile={this.props.updateLineItemInCartAddMobile}
              updateLineItemInCartLessMobile={this.props.updateLineItemInCartLessMobile}
              isCartOpen={this.props.isCartOpen}
              handleCartClose={this.props.handleCartClose}
              customerAccessToken={this.props.customerAccessToken}
              ibigcounter={this.state.ibigcounter}
              ismallcounter={this.state.ismallcounter}
              youbigcounter={this.state.youbigcounter}
              yousmallcounter={this.state.yousmallcounter}
              webigcounter={this.state.webigcounter}
              wesmallcouner={this.state.wesmallcouner}
              Obsidiancounter={this.state.Obsidiancounter}
              Opalcounter={this.state.Opalcounter}
              Tigercounter={this.state.Tigercounter}
              setibigcounter={this.setibigcounter}
              setismallcounter={this.setismallcounter}
              setyoubigcounter={this.setyoubigcounter}
              setyousmallcounterl={this.setyousmallcounterl}
              setwewebigcounter={this.setwewebigcounter}
              setwesmallcounerl={this.setwesmallcounerl}
              setObsidiancounter={this.setObsidiancounter}
              setOpalcounter={this.setOpalcounter}
              setibigcounterdecrease={this.setibigcounterdecrease}
              setismallcounterdecrease={this.setismallcounterdecrease}
              setyoubigcounterdecrease={this.setyoubigcounterdecrease}
              setyousmallcounterldecrease={this.setyousmallcounterldecrease}
              setwewebigcounterdecrease={this.setwewebigcounterdecrease}
              setwesmallcounerldecrease={this.setwesmallcounerldecrease}
              setObsidiancounterdecrease={this.setObsidiancounterdecrease}
              setOpalcounterdecrease={this.setOpalcounterdecrease}
              setTigercounterdecrease={this.setTigercounterdecrease}
              ShopPageDisplay={this.state.ShopPageDisplay}
              PDMAnimation={this.state.PDMAnimation}
              ShopPageDisplaySwitchSpecial={this.ShopPageDisplaySwitchSpecial}
            />
          )}
        </div>
        <div style={{ display: this.state.ContactPageDisplay }}>
          <ContactPage
            ContactPageDisplaySwitch={this.ContactPageDisplaySwitch}
            BackBUttonPressed={this.BackBUttonPressed}
          />
        </div>
        <BodyMap
          BodyMapAnimation1={this.props.BodyMapAnimation1}
          BodyMapAnimation2={this.props.BodyMapAnimation2}
          BodyMapAnimation3={this.props.BodyMapAnimation3}
          BodyMapAnimation4={this.props.BodyMapAnimation4}
          BodyMapAnimation5={this.props.BodyMapAnimation5}
          BodyMapAnimation6={this.props.BodyMapAnimation6}
          BodyMapAnimation7={this.props.BodyMapAnimation7}
          MindDisplay={this.props.MindDisplay}
          TruthDisplay={this.props.TruthDisplay}
          HeartDisplay={this.props.HeartDisplay}
          CenterDisplay={this.props.CenterDisplay}
          ReliefDisplay={this.props.ReliefDisplay}
          EnergizeDisplay={this.props.EnergizeDisplay}
          GroundDisplay={this.props.GroundDisplay}
        />
        {/* <div
          className="Notification"
          style={{ display: this.props.NotificationDisplay }}>
          {this.props.ProductAddedtitle} Added
          Item Added
        </div> */}
      </div>
    );
  }
}

export default GlobalUI;
