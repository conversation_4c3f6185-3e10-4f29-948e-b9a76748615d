{"name": "incorp", "version": "0.1.0", "private": true, "dependencies": {"@apollo/client": "^3.8.8", "@babylonjs/gui": "^6.35.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "babylonjs": "^6.35.0", "babylonjs-gui": "^6.35.0", "babylonjs-loaders": "^6.35.0", "babylonjs-materials": "^6.35.0", "bootstrap": "^5.3.2", "cannon": "^0.6.2", "expo-modules-core": "^1.5.12", "expo-sensors": "^12.5.0", "graphql": "^16.8.1", "graphql-tag": "^2.12.6", "gsap": "^3.12.4", "javascript-obfuscator": "^4.1.0", "lodash": "^4.17.21", "netlify": "^13.1.11", "npm": "^10.2.5", "npm-check-updates": "^16.14.12", "pepjs": "^0.5.3", "prop-types": "^15.8.1", "react": "^18.2.0", "react-accelerometer": "^1.0.1", "react-addons-create-fragment": "^15.6.2", "react-art": "^18.2.0", "react-burger-menu": "^3.0.9", "react-dom": "^18.2.0", "react-hook-screen-orientation": "^1.0.4", "react-media-player": "^0.7.9", "react-usestateref": "^1.0.8", "web-vitals": "^3.5.0"}, "devDependencies": {"react-scripts": "^5.0.1"}, "scripts": {"start": "react-scripts start", "build": "GENERATE_SOURCEMAP=false react-scripts build", "winBuild": "set \"GENERATE_SOURCEMAP=false\" && react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "resolutions": {"nth-check": "^2.0.1"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}