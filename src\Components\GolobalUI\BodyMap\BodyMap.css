.BodyMap {
  position: absolute;
  width: 14%;
  height: auto;
  background-color: transparent;
  padding: 0.5%;
  /* transition: all .5s ease-in-out; */
  transform-origin: left top;
  /* animation: scale-up-ver-center 0.5s cubic-bezier(0.39, 0.575, 0.565, 1) both; */
}

.BodyMapTitle {
  width: 100%;
  text-align: left;
  font-weight: 500;
  padding-bottom: 2%;
  font-size: 1vw;
}

.BodyMapDescription {
  width: 100%;
  text-align: left;
  padding-top: 2%;
  font-size: 0.9vw;
  font-style: italic;
  line-height: 1.1;
}

.BodyMapImage {
  position: absolute;
  width: 90%;
}

.MindIMG {
  top: -40%;
  left: 100%;
}

.TruthIMG {
  width: 70%;
  top: -150%;
  right: 100%;
}

.HeartIMG {
  width: 120%;
  /* top: 186%; */
  top: 125%;
  left: 15%;
  transform: rotate(-90deg);
}

.CenterIMG {
  width: 70%;
  left: 90%;
  top: -100%;
  transform: rotate(90deg);
}

.ReliefIMG {
  width: 80%;
  left: 96%;
  top: 10%;
}

.EnergizeIMG {
  width: 60%;
  top: 0;
  left: 100%;
}

.GroundIMG {
  width: 80%;
  right: 90%;
  top: -50%;
  transform: rotate(-90deg);
}

.BodyMapImage img {
  width: 100%;
  opacity: 0.8;
}

@keyframes Mind {
  0% {
    opacity: 0;
  }

  20% {
    opacity: 1;
  }

  100% {
    opacity: 1;
  }
}

@keyframes Mindmask {
  0% {
    top: 12%;
    height: 15%;
  }

  20% {
    top: 28%;
    height: 0%;
  }

  100% {
    top: 28%;
    height: 0%;
  }
}

@keyframes MindmaskReverse {
  0% {
    top: 28%;
    height: 0%;
  }

  100% {
    top: 12%;
    height: 15%;
  }
}

.MindDIVMask {
  top: 12%;
  left: 61%;
  height: 15%;
  /* background-color: rgba(240, 240, 240, 1); */
  background-color: rgba(236, 231, 231, 1);
  animation: Mindmask 8s;
}

.MindDIVMaskReverse {
  top: 12%;
  left: 61%;
  height: 15%;
  /* background-color: rgba(240, 240, 240, 1); */
  background-color: rgba(236, 231, 231, 1);
  animation: MindmaskReverse 0.5s;
}

.MindDIV {
  top: 12%;
  left: 61%;
  animation: Mind 8s;
}

@keyframes Truth {
  0% {
    opacity: 0;
  }

  20% {
    opacity: 1;
  }

  100% {
    opacity: 1;
  }
}

@keyframes Truthmask {
  0% {
    top: 21%;
    height: 15%;
  }

  20% {
    top: 36%;
    height: 0%;
  }

  100% {
    top: 36%;
    height: 0%;
  }
}

@keyframes TruthmaskReverse {
  0% {
    top: 36%;
    height: 0%;
  }

  100% {
    top: 21%;
    height: 15%;
  }
}

.TruthDIVMask {
  top: 21%;
  left: 25%;
  height: 15%;
  /* background-color: rgba(240, 240, 240, 1); */
  background-color: rgba(236, 231, 231, 1);
  animation: Truthmask 8s;
}

.TruthDIVMaskReverse {
  top: 21%;
  left: 25%;
  height: 15%;
  /* background-color: rgba(240, 240, 240, 1); */
  background-color: rgba(236, 231, 231, 1);
  animation: TruthmaskReverse 0.5s;
}

.TruthDiv {
  top: 21%;
  left: 25%;
  animation: Truth 8s;
}

@keyframes Heart {
  0% {
    opacity: 0;
  }

  20% {
    opacity: 1;
  }

  100% {
    opacity: 1;
  }
}

@keyframes Heartmask {
  0% {
    top: 33%;
    height: 10%;
  }

  20% {
    top: 43%;
    height: 0%;
  }

  100% {
    top: 43%;
    height: 0%;
  }
}

@keyframes HeartmaskReverse {
  0% {
    top: 43%;
    height: 0%;
  }

  100% {
    top: 33%;
    height: 10%;
  }
}

.HeartDIVMask {
  top: 33%;
  left: 18%;
  height: 10%;
  /* background-color: rgba(240, 240, 240, 1); */
  background-color: rgba(236, 231, 231, 1);
  animation: Heartmask 8s;
}

.HeartDIVMaskReverse {
  top: 33%;
  left: 18%;
  height: 10%;
  /* background-color: rgba(240, 240, 240, 1); */
  background-color: rgba(236, 231, 231, 1);
  animation: HeartmaskReverse 0.5s;
}

.HeartDiv {
  top: 35%;
  left: 18%;
  animation: Heart 8s;
}

@keyframes Center {
  0% {
    opacity: 0;
  }

  20% {
    opacity: 1;
  }

  100% {
    opacity: 1;
  }
}

@keyframes Centermask {
  0% {
    top: 42%;
    height: 15%;
  }

  20% {
    top: 57%;
    height: 0%;
  }

  100% {
    top: 57%;
    height: 0%;
  }
}

@keyframes CentermaskReverse {
  0% {
    top: 57%;
    height: 0%;
  }

  100% {
    top: 42%;
    height: 15%;
  }
}

.CenterDIVMask {
  top: 42%;
  left: 66%;
  height: 15%;
  /* background-color: rgba(240, 240, 240, 1); */
  background-color: rgba(236, 231, 231, 1);
  animation: Centermask 8s;
}

.CenterDIVMaskReverse {
  top: 42%;
  left: 66%;
  height: 15%;
  /* background-color: rgba(240, 240, 240, 1); */
  background-color: rgba(236, 231, 231, 1);
  animation: CentermaskReverse 0.5s;
}

.CenterDiv {
  top: 42%;
  left: 66%;
  animation: Center 8s;
}

@keyframes Relief {
  0% {
    opacity: 0;
  }

  20% {
    opacity: 1;
  }

  100% {
    opacity: 1;
  }
}

@keyframes Reliefmask {
  0% {
    top: 52%;
    height: 15%;
  }

  20% {
    top: 67%;
    height: 0%;
  }

  100% {
    top: 67%;
    height: 0%;
  }
}

@keyframes ReliefmaskReverse {
  0% {
    top: 67%;
    height: 0%;
  }

  100% {
    top: 52%;
    height: 15%;
  }
}

.ReliefDIVMask {
  top: 52%;
  left: 70%;
  height: 15%;
  /* background-color: rgba(240, 240, 240, 1); */
  background-color: rgba(236, 231, 231, 1);
  animation: Reliefmask 8s;
}

.ReliefDIVMaskReverse {
  top: 52%;
  left: 70%;
  height: 15%;
  /* background-color: rgba(240, 240, 240, 1); */
  background-color: rgba(236, 231, 231, 1);
  animation: ReliefmaskReverse 0.5s;
}

.ReliefDiv {
  top: 52%;
  left: 70%;
  animation: Relief 8s;
}

@keyframes Energize {
  0% {
    opacity: 0;
  }

  20% {
    opacity: 1;
  }

  100% {
    opacity: 1;
  }
}

@keyframes Energizemask {
  0% {
    top: 69%;
    height: 15%;
  }

  20% {
    top: 84%;
    height: 0%;
  }

  100% {
    top: 84%;
    height: 0%;
  }
}

@keyframes EnergizemaskReverse {
  0% {
    top: 84%;
    height: 0%;
  }

  100% {
    top: 69%;
    height: 15%;
  }
}

.EnergizeDIVMask {
  top: 69%;
  left: 62%;
  height: 15%;
  /* background-color: rgba(240, 240, 240, 1); */
  background-color: rgba(236, 231, 231, 1);
  animation: Energizemask 8s;
}

.EnergizeDIVMaskReverse {
  top: 69%;
  left: 62%;
  height: 15%;
  /* background-color: rgba(240, 240, 240, 1); */
  background-color: rgba(236, 231, 231, 1);
  animation: EnergizemaskReverse 0.5s;
}

.EnergizeDiv {
  top: 69%;
  left: 62%;
  animation: Energize 8s;
}

@keyframes Ground {
  0% {
    opacity: 0;
  }

  20% {
    opacity: 1;
  }

  100% {
    opacity: 1;
  }
}

@keyframes Groundmask {
  0% {
    top: 81%;
    height: 15%;
  }

  20% {
    top: 99%;
    height: 0%;
  }

  100% {
    top: 99%;
    height: 0%;
  }
}

@keyframes GroundmaskReverse {
  0% {
    top: 99%;
    height: 0%;
  }

  100% {
    top: 81%;
    height: 15%;
  }
}

.GroundDIVMask {
  top: 81%;
  left: 25%;
  height: 15%;
  /* background-color: rgba(240, 240, 240, 1); */
  background-color: rgba(236, 231, 231, 1);
  animation: Groundmask 8s;
}

.GroundDIVMaskReverse {
  top: 81%;
  left: 25%;
  height: 15%;
  /* background-color: rgba(240, 240, 240, 1); */
  background-color: rgba(236, 231, 231, 1);
  animation: GroundmaskReverse 0.5s;
}

.GroundDiv {
  top: 81%;
  left: 25%;
  animation: Ground 8s;
}

/* ----------------------------------------------
 * Generated by Animista on 2022-9-8 1:21:37
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation scale-down-ver-to-bottom
 * ----------------------------------------
 */
@-webkit-keyframes scale-down-ver-bottom {
  0% {
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transform-origin: 0% 100%;
    transform-origin: 0% 100%;
  }

  100% {
    -webkit-transform: scaleY(0.3);
    transform: scaleY(0.3);
    -webkit-transform-origin: 0% 100%;
    transform-origin: 0% 100%;
  }
}

@keyframes scale-down-ver-bottom {
  0% {
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transform-origin: 0% 100%;
    transform-origin: 0% 100%;
  }

  100% {
    -webkit-transform: scaleY(0.3);
    transform: scaleY(0.3);
    -webkit-transform-origin: 0% 100%;
    transform-origin: 0% 100%;
  }
}

@-webkit-keyframes scale-up-ver-center {
  0% {
    -webkit-transform: scaleY(0);
    transform: scaleY(0);
  }

  100% {
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
  }
}

@keyframes scale-up-ver-center {
  0% {
    -webkit-transform: scaleY(0);
    transform: scaleY(0);
  }

  100% {
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
  }
}

@-webkit-keyframes scale-down-ver-center {
  0% {
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
  }

  100% {
    -webkit-transform: scaleY(0);
    transform: scaleY(0);
  }
}

@keyframes scale-down-ver-center {
  0% {
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
  }

  100% {
    -webkit-transform: scaleY(0);
    transform: scaleY(0);
  }
}

@media only screen and (max-width: 600px) {
  .BodyMap {
    width: 40%;
  }

  .BodyMapTitle {
    font-size: 1em;
  }

  .BodyMapDescription {
    font-size: 0.9em;
  }
}