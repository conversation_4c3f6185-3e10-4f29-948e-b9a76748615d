@font-face {
    font-family: "Inter Medium";
    src: url(../../../Fonts/Inter-Medium.ttf);
  }
  

@keyframes fadeIn {
    0% { opacity: 0; }
    100% { opacity: 1; }
}
.BurgerIconManu{
    position: absolute;
    width: 12%;
    top: 2px;
    left: 2px;
}
.BurgerIconManu button {
    width: 100%;
    background-color: transparent;
    outline: none;
    border: none;
}
.BurgerIconManu button img{
    width: 100%;
}
.MobileManuContent {
    width: 100%;
    /* height: 100%; */
    /* height: 78.5%; */
    height: 80%;
    top: 21.5%;
    position: absolute;
    /* background-color: rgba(240, 240, 240, 255); */
    background-color: rgba(236, 231, 231, 1);
    z-index: 9999;
    /* animation: fadeIn 0.5s; */
}
.IconDiv{
    margin-top: 11.5%;;
    width: 100%;
    height: 14%;
    margin-bottom: 3%;
}
.IconDiv img {
    width: 94%;
    /* height: 18vh; */
    height: auto;
    margin-left: auto;
    margin-right: auto;
}
.ListManuDiv{
    width: 100%;
    height: auto;
    display: flex;
    justify-content: left;
    align-items: left;
    text-align: left;
    flex-wrap: wrap;
}
.CartMCon{
    width: 100%;
    display: flex;
    flex-wrap: nowrap;
}
.CartsecP{
    
    width: auto;
    height: 100%;
}
.CartsecP.second{
    margin-left: 0.5%;
}
.ListItm{
    font-family: Caslon !important;
    width: 100%;
    display: flex;
    text-align: left;
    height: auto;
    text-transform: uppercase;
    font-size: 35px;
    background-color: transparent;
    outline: none;
    /* font-weight: 600; */
    letter-spacing: 0px;
    padding-left: 3%;
    padding-top: 2%;
    padding-bottom: 2%;
    border: none;
    color: #000;
    text-decoration: none;
}
.ListItm img {
    width: 30px;
    height: 30px;
    margin-left: 5%;
    margin-top: -7px;
    /* border: 1px solid red; */
}
.SpiniconDiv{
    width: 100%;
    display: flex;
    align-items: right;
    text-align: right;
    justify-content: right;
    padding: 2%;
    box-sizing: border-box;
}
.SpinContainer {
    width: 25%;
    float: right !important;
    margin-left: 75%;
    margin-top: -25px;
}
.SpinContainer img {
    width: 100%;
}
.bottomManu{
    width: 94%;
    display: flex;
    margin-left: auto;
    margin-right: auto;
    flex-wrap: wrap;
    padding-top: 2%;
    /* bottom: 2%; */
    bottom: 3.5%;
    border-top: 0.5px solid black;
    position: absolute;
    margin-left: 3%;
    font-family: "Inter";
}
.btmline{
    width: 100%;
    margin-top: 4%;
    display: flex;
    flex-wrap: nowrap;
    align-items: left;
    text-align: left;
    font-size: 15px;
}
.faqdiv{
    width: 60%;
}
.devdiv{
    width: 40%;
    color: #000;
    opacity: 0.25;
    white-space: nowrap;
    font-weight: 600;
    font-size:12px ;
    margin-top: auto;
    font-family: "Inter";
}
.devdiv img {
    width: 5%;
}
.webdiv{
    width: 60%;
    color: #000;
    opacity: 0.25;
    white-space: nowrap;
    font-weight: 600;
    font-size:12px ;
    margin-top: auto;
    font-family: "Inter";
}
.newControlBagSpanLessThan10 {
    /* right: 80px;
    top: 43%;
    height: 30%;
    width: 40%; */
    /* position: relative; 
     margin-left: -19px;
    margin-top: 0px;
    font-size: 0.5em; */
    position: absolute;
    margin-left: 12px;
    margin-top: -25px;
    font-size: 0.5em;
  }
  .newControlBagSpanLessThan10:hover {
    color: white;
  }
  .newControlBagSpanGreaterThan10 {
    /* position: relative; */
    /* right: 31%;
    top: 43%;
    height: 30%;
    width: 40%; */
    /* margin-left: -21px;
    margin-top: 10px;
    font-size: 0.45em; */
    position: absolute;
    margin-left: 11px;
    margin-top: -26px;
    font-size: 0.5em;
  }
  .newControlBagSpanGreaterThan10:hover {
    color: white;
  }
  .FAQCardPage{
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0%;
    top: 0%;
    background-color: rgba(150, 150, 150, 0.7);
    z-index: 99991;
}
.FAQCard{
    /* background-color: #f2f2f2; */
    background-color: #f6f6f6;
    width: 95%;
    /* height: 90%; */
    height: 75%;
    /* margin-top: 15%; */
    top: 22%;
    position: absolute;
    z-index: 9999;
    /* margin-left: 5%;
    margin-right: 5%; */
    margin-left: 2.5%;
    /* border-radius: 12px; */
    border-radius: 0px;
    padding: 5%;
    box-sizing: border-box;
    z-index: 6;
}
.FAQCardTitle{
    width: 100%;
    text-transform:uppercase;
    display: flex;
    align-items: left;
    text-align: left;
    justify-content: left;
    border:none;
    font-size: 4.5vw;
    padding-bottom: 3%;
    border-bottom: 0.5px solid #000; 
}
.FAQCardContent{
    width: 100%;
    padding-top: 5%;
    overflow-y: auto;
    overflow-x: hidden;
    height: 91%;
    text-align: left;
}
.FAQCardContent::-webkit-scrollbar {
    display: none;
}
.FAQTitle{
    font-style: italic;
}
.FAQAnswer{
    margin-top: 5px;
    margin-bottom: 10px;
}
.pressCard{
    /* background-color: #f2f2f2; */
    background-color: #f6f6f6;
    width: 95%;
    /* height: 90%; */
    /* height: 75%; */
    /* margin-top: 15%; */
    top: 40%;
    position: absolute;
    z-index: 9999;
    /* margin-left: 5%;
    margin-right: 5%; */
    margin-left: 2.5%;
    /* border-radius: 12px; */
    border-radius: 0px;
    padding: 5%;
    box-sizing: border-box;
    z-index: 6;
}
.pressCardPage{
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0%;
    top: 0%;
    background-color: rgba(150, 150, 150, 0.7);
    z-index: 99991;
}
.pressCardTitle{
    width: 100%;
    text-transform:uppercase;
    display: flex;
    align-items: left;
    text-align: left;
    justify-content: left;
    border:none;
    font-size: 4.5vw;
    padding-bottom: 3%;
    border-bottom: 0.5px solid #000; 
}
.pressCardContent{
    width: 100%;
    padding-top: 5%;
    overflow-y: auto;
    overflow-x: hidden;
    height: 91%;
    text-align: left;
    font-style: italic;
}
.pressCardContent::-webkit-scrollbar {
    display: none;
}