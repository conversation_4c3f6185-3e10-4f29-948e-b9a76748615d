import React, { Component } from "react";
import "./ProductDetails.css";
import { Media, Player, controls } from "react-media-player";
import CustomPlayPause from "../MediaPlayer/CustomPlayPause ";

class ProductDetailsContentMobile extends Component {

  render() {
    return (
        <div className="ProductDetailsContentMobile" style={{display: this.props.ProductDetailsMobileContentDisplay, 
            animation: this.props.PDMAnimation}}>
            <div style={{position: "absolute", width: "100%", height: "22%", top: "0%"}} onClick={()=> this.props.showProductDetailMobile("hide")}>
            </div>
            <div style={{position: "absolute", width: "100%", height: "28%", top: "72%"}} onClick={()=> this.props.showProductDetailMobile("hide")}>   
            </div>
            {this.props.currentModel.substr(0,4) != 'tool' ? (
              <div className="cardProductDetails">
            <div className="cardtitle" style={{textAlign: "center"}}>
              {this.props.PDITitle}{" "}
              <div className="VoiceContainer">
                <Media>
                  <div className="media">
                    <CustomPlayPause />
                    <Player src={this.props.PDSound} />
                    <div className="media-controls"></div>
                  </div>
                </Media>
              </div>
            </div>
            <div className="cardline">
            {this.props.PDICardLine.substr(0,1) == "I" ? (
            <div style={{textAlign: "center"}}>
            <span style={{fontStyle: "italic"}}>{this.props.PDICardLine.substr(0,4)}</span>
            <span>{this.props.PDICardLine.substr(4, this.props.PDICardLine.indexOf('.')-2)}</span><br></br>
            <span style={{fontStyle: "italic"}}>{this.props.PDICardLine.substr(0,4)}</span>
            <span>{this.props.PDICardLine.substr(this.props.PDICardLine.indexOf('.')+6,this.props.PDICardLine.length)}</span>
            </div>
          ) : ( 
          this.props.PDICardLine.substr(0,3) == "YOU" ? (
            <div style={{textAlign: "center"}}>
            <span style={{fontStyle: "italic"}}>{this.props.PDICardLine.substr(0,7)}</span>
            <span>{this.props.PDICardLine.substr(7, this.props.PDICardLine.indexOf('.')-5)}</span><br></br>
            <span style={{fontStyle: "italic"}}>{this.props.PDICardLine.substr(0,7)}</span>
            <span>{this.props.PDICardLine.substr(this.props.PDICardLine.indexOf('.')+9,this.props.PDICardLine.length)}</span>
            </div>
          ) : (
            <div style={{textAlign: "center"}}>
            <span style={{fontStyle: "italic"}}>{this.props.PDICardLine.substr(0,6)}</span>
            <span>{this.props.PDICardLine.substr(6, this.props.PDICardLine.indexOf('.')-4)}</span><br></br>
            <span style={{fontStyle: "italic"}}>{this.props.PDICardLine.substr(0,6)}</span>
            <span>{this.props.PDICardLine.substr(this.props.PDICardLine.indexOf('.')+8,this.props.PDICardLine.length)}</span>
            </div>
          )
          )}
            </div>
            <div style={{fontStyle: "italic"}} className="carddescription">
            {this.props.PDIDescription == "With CLEANSING Thyme I eliminate distraction while SUPPORTING with grounding Vetiver and healing Myrrh. Antioxidant Melissa vitalizes, GUIDING me towards my authentic inner self." ? 
              (
                <div style={{textAlign: "center"}}>With CLEANSING <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Thyme</span> I eliminate distraction while SUPPORTING with grounding <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Vetiver</span> and healing <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Myrrh</span>. Antioxidant <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Melissa</span> vitalizes, GUIDING me towards my authentic inner self.</div>
              ) :
              (
                this.props.PDIDescription == "With CLEANSING Lime and Coriander; you stimulate your body and mind. You SUPPORT emotional stability with Sandalwood, opening your heart with Rose and GUIDING you towards divine inner love." ?
                (
                  <div style={{textAlign: "center"}}>With CLEANSING <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Lime</span> and <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Coriander</span>; you stimulate your body and mind. You SUPPORT emotional stability with <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Sandalwood</span>, opening your heart with <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Rose</span> and GUIDING you towards divine inner love.</div>
                ) :
                (
                  this.props.PDIDescription == "With CLEANSING Coriander, Orange and Thyme; we ignite your creative mind and act as an aphrodisiac. We SUPPORT tranquility with Vetiver while Roman Chamomile GUIDES us towards inner equilibrium." ?
                  (
                    <div style={{textAlign: "center"}}>With CLEANSING <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Coriander, Orange</span> and <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Thyme</span>; we ignite your creative mind and act as an aphrodisiac. We SUPPORT tranquility with <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Vetiver</span> while <span style={{fontFamily: 'CaslonBold', fontStyle: "normal"}}>Roman Chamomile</span> GUIDES us towards inner equilibrium.</div>
                  ) :
                  (
                    <div></div>
                  )
                )
              )}
              <div className="editionMSG" style={{fontStyle: "normal", textAlign: "center"}}>
                {this.props.QuantityLimitedMob1Statment}<br></br>
                {this.props.QuantityLimitedMob2Statment}
              </div>
              <div>
              <p style={{fontStyle: "italic", textAlign: "center"}} className="ingredientsTool">
                Tool comes with the 33ml bottle <br></br>
                Chain comes with the 5ml bottle <br></br>
              </p>
              </div>
              <p className="ingredients" style={{textAlign: "center"}}>
                {"Pure essential oils mixed with Apricot oil & stabilized with vitamin E."}
              </p>
              <p className="ingredientsMid" style={{textAlign: "center"}}>
                {this.props.PDITitle == "I" ? "Total of 6 ingredients." : ""}
                {this.props.PDITitle == "WE" ? "Total of 7 ingredients." : ""}
                {this.props.PDITitle == "YOU" ? "Total of 6 ingredients." : ""}
              </p>
              <p className="ingredientsBot" style={{textAlign: "center"}}>
                {"Made with 100% pure and 100% natural Chemotyped cruelty free Essential oils."}
              </p>
            </div>
          </div>
            ) :
            (
            <div className="cardProductDetails">
            <div className="cardtitle" style={{textAlign: "center"}}>
              {"Tool"}{" "}
            </div>
            <div className="cardline">
            <div style={{textAlign: "center"}}>
              <span>Shaped to fit into the 33ml bottle; used for acupressure, massage or as a spoon for caviar.</span>
            </div>
            </div>
          </div>
            ) }
        </div>
    );
  }
}

export default ProductDetailsContentMobile;
