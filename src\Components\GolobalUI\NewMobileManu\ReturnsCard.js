import React, { useState } from "react";
import "./NewMobileManu.css";

function ReturnsCard(props) {

    return (
        <div className="pressCardPage" style={{display: props.returnsDisplay, 
            animation: props.pressAnimation}}>
            <div style={{position: "absolute", width: "100%", height: "40%", top: "0%"}} onClick={()=> props.returnsSwitch()}>
            </div>
            <div style={{position: "absolute", width: "100%", height: "55%", top: "45%"}} onClick={()=> props.returnsSwitch()}>
            </div>
            <div className="pressCard" style={{display: props.returnsDisplay, 
                animation: props.pressAnimation}}>
                <div className="pressCardTitle">
                    Returns
                </div>
                <div className="pressCardContent">
                <span>INCORP products are unique art pieces each numbered and are non refundable.</span><br></br>
                {/* <span><EMAIL></span> */}
                </div>
            </div>
        </div>
    );
}

export default ReturnsCard;
