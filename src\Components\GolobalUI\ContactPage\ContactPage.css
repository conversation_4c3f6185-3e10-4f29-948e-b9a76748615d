.ContactPage {
  position: absolute;
  /* height: 63%; */
  /* height: 55%; */
  width: 22%;
  /* margin-top: 8%; */
  /* top: 30%; */
  /* top: 20%; */
  top: 12%;
  z-index: 999;
  background-color: rgba(255, 255, 255, 1);
  border-radius: 0px;
  /* padding: 1.2%; */
  padding: 5.2% 5.2%;
  right: 0;
  left: 0;
  margin-left: auto;
  margin-right: auto;
  border-radius: 0px !important;
  transform: translateZ(0);
}
.ContactPageContainer {
  position: relative;
  width: 100%;
  height: 100%;
}
.ContactPageHead {
  width: 100%;
  display: flex;
  margin: 0;
  padding: 0;
  border-bottom: 0.5px solid black;
}
.CPtitle {
  width: 50%;
  text-transform: uppercase;
  text-align: left;
  font-size: 1.4vw;
  font-weight: 100;
}
.Xcontainer {
  width: 50%;
  text-align: right;
}
.CloseContact {
  width: 25px;
  height: 25px;
  position: relative;
  right: 0;
  background-color: #f5f5f5;
  border-radius: 50%;
  border: none;
  padding: 3%;
  cursor: pointer;
  /* margin-bottom: 10px; */
  /* top: -18%; */
  top: -27%;
}
.CloseContact img {
  /* width: 100%; */
  width: 62%;
}
.ContactPageBody {
  position: relative;
  width: 100%;
  height: 93.5%;
  padding: 0;
  margin: 0;
  text-align: left;
}
.FirstTwoInputs {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: 1fr;
  grid-column-gap: 20px;
  grid-row-gap: 0px;
  margin-top: 5%;
}
.ContactPageBody span {
  display: block;
  margin-top: 4%;
  margin-bottom: 2%;
  margin-left: 0%;
  font-weight: 500;
}
.ContactPageBody span:focus{
  border: none;
  color: transparent;
  background-color: transparent;
}
.ContactPageForm {
  width: 100%;
  height: 100%;
  text-align: left;
  font-size: 1vw;
  font-family: 'Inter';
}
.ContactPageForm input[type="text"] {
  width: 93.5%;
  margin: 0%;
  padding: 1.4%;
  border: none;
  background-color: #f3f4f6;
  border-radius: 0px;
  font-family: 'Inter';
}
.ContactPageForm input[type="tel"] {
  /* width: 66%; */
  width: 62%;
  margin: 0%;
  padding: 1.4%;
  border: none;
  background-color: #f3f4f6;
  border-radius: 0px;
  font-family: 'Inter';
}
input::placeholder {
  opacity: 0.3;
}
.formmsg {
  width: 100%;
  display: flex;
  padding-left: 0%;
  justify-content: left;
  padding: 0.5em 0;
  margin-top: 0.5vh;
  margin-bottom: 1.5vh;
  align-items: center;
}
.ContactPageForm input {
  background-color: #f3f4f6;
  border: none;
}
/* .ContactPageBody select {
  width: 14%;
  background-color: #f3f4f6;
  border: none;
  padding: 1.2%;
  border-radius: 0px;
} */
.my-select-menu {
  display: inline-block;
  /* width: 30%; */
  width: 35%;
  background-color: #f3f4f6;
  border: none;
  padding: 1.4%;
  border-radius: 0px;
  color: #000;
}
select option {
  background-color: white;
}
select option:hover {
  background-color: black !important;
  color: black !important;
}
.ContactPageForm input[type="submit"] {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 5vh;
  bottom: 0px;
  /* margin-bottom: 4%; */
  color: #fff;
  background-color: black;
  cursor: pointer;
  font-family: 'Inter';
  -webkit-border-radius: 0px !important;
  -khtml-border-radius: 0px !important;
    -moz-border-radius: 0px !important;
         border-radius: 0px !important;
         transform: translateZ(0);
         overflow: hidden
}
.ContactPageForm input[type="submit"]:hover {
  color: black;
  background-color: #fff;
  border: 1px solid black;
  cursor: pointer;
  border-radius: 0px !important;
}
.ContactPageForm input[type="checkbox"] {
  display: inline-block;
  cursor: pointer;
  width: 1.2em;
  height: 1.2em;
  border: 0.1em solid grey;
  border-radius: 0em;
  transform: translateY(-0.075em);
  margin-bottom: 0%;
}
.ContactPageForm input[type="checkbox"]:enabled:checked {
  background-color: black !important;
  color: white;
}
textarea::placeholder {
  opacity: 0.3;
}
#sin {
  margin-bottom: 5%;
}
#inline {
  display: inline-block;
  margin-left: 2%;
  margin-top: 0%;
  margin-bottom: 0%;
}
#message {
  height: 10vh;
  width: 97%;
  background-color: #f3f4f6;
  border: none;
  resize: none;
  padding: 1.4%;
  text-align: left;
  display: block;
  font-family: 'Inter';
  border-radius: 0px;
}
#edittab {
  padding: 3%;
}
input, textarea {
  -webkit-appearance: none;
  border-radius: 0;
}
@media only screen and (max-width: 600px) {
  .ContactPage {
    position: absolute;
    /* height: 68%; */
    width: 77%;
    /* margin-top: 30%; */
    top: 21%;
    z-index: 999;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 0px;
    padding: 5%;
    right: 0;
    left: 0;
    margin-left: auto;
    margin-right: auto;
  }
  .CPtitle {
    width: 50%;
    text-transform: uppercase;
    text-align: left;
    font-size: 4.5vw;
    font-weight: 100;
  }
  .ContactPageForm {
    font-size: 3.25vw;
    font-family: 'Inter';
  }
  .ContactPageBody {
    position: relative;
    width: 100%;
    height: 93.5%;
    padding: 0;
    margin: 0;
    text-align: left;
  }
  .my-select-menu {
    display: inline-block;
    /* width: 30%; */
    width: 35%;
    height: 22.5px;
    background-color: #f3f4f6;
    border: none;
    padding: 1.4%;
    border-radius: 0px;
    color: #000;
  }
  .ContactPageForm input[type="tel"] {
    /* width: 66%; */
    width: 63%;
    margin: 0%;
    padding: 1%;
    padding-top: 1.5%;
    padding-bottom: 1.45%;
    border: none;
    background-color: #f3f4f6;
    border-radius: 0px;
    font-family: 'Inter';
  }
  #inline {
    display: inline-block;
    margin-left: 2%;
    margin-top: 0%;
    margin-bottom: 0%;
  }
}
