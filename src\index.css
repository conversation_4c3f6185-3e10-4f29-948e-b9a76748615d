/* src/index.css */
@font-face {
  font-family: "Montserrat";
  src: local("Montserrat"), url(./Fonts/Montserrat-Regular.ttf) format("truetype");
}

.container {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

body {
  margin: 0;
  padding: 0;
  font-family: "Caslon", Caslon;
  overflow-y: hidden;
}
.unselectable {
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.LogoImageDiv {
  position: absolute;
  height: 10%;
  width: 10%;
  top: 2%;
  left: 45%;
}
.LogoImage {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.App {
  width: 100%;
  height: 100vh;
  box-sizing: border-box; /** add this **/
  -moz-box-sizing: border-box; /** add this **/
  -webkit-box-sizing: border-box; /** add this **/
  -ms-box-sizing: border-box; /** add this **/
  outline: none;
}

.FingerPanel {
  position: absolute;
  background-color: transparent;
  left: 0%;
  right: 0%;
  z-index: 999999;
  height: 20vh;
  bottom: 15vh;
}

.Finger {
  position: relative;
  background-color: transparent;
  margin: auto;
  padding-left: 0px;
  padding-right: 0px;
  width: 100px;
  height: 100px;
  animation: fingermoving 9s 4;
  top: -50%;
}

@keyframes fingermoving {
  0% {
    padding-left: 0px;
    padding-right: 0px;
  }
  25% {
    padding-left: 0px;
    padding-right: 0px;
  }
  50% {
    padding-left: 0px;
    padding-right: 300px;
  }
  75% {
    padding-left: 300px;
    padding-right: 0px;
  }
  100% {
    padding-left: 0px;
    padding-right: 0px;
  }
}

.rotate {
  background-color: transparent;
  width: 100px;
  height: 100px;
  margin-right: 0px;
  margin-left: 0px;
  margin-top: 0px;
  margin-bottom: 0px;
  pointer-events: none;
  user-drag: none;
  user-select: none;
  -moz-user-select: none;
  -webkit-user-drag: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

.ControlPanel {
  width: 100%;
  height: 8em;
  font-size: 14px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 5;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
  -ms-flex-align: start;
  align-items: flex-start;
  /* border-bottom: 0.5px solid rgba(0, 0, 0, 0.5); */
  outline: none;
}

.LeftShoe {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 10em;
  height: 100%;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  background-color: transparent;
  text-align: center;
  white-space: normal;

  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  border-style: none solid none none;
  border-width: 1px 0.5px 1px 1px;
  border-color: rgba(122, 122, 122, 0.31) hsla(0, 0%, 100%, 0.5) rgba(122, 122, 122, 0.31) rgba(122, 122, 122, 0.31);
  border-radius: 0px;
  background-image: url("./Images/left_F-1.svg");
  background-position: 80% 110%;
  background-size: 25px;
  background-repeat: no-repeat;
  font-family: Montserrat, sans-serif;
  color: black;
  font-size: 15px;
  font-weight: 400;
  text-align: center;

  padding: 9px 15px;
  outline: none;
}

.LeftShoe:hover {
  background-color: black;
  color: hsla(0, 0%, 100%, 0.5);
}

.LeftShoe:focus {
  outline: none;
}

.RightShoe {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 10em;
  height: 100%;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  background-color: transparent;
  text-align: center;
  white-space: normal;

  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  border: 0;
  border-right: 0.5px solid hsla(0, 0%, 100%, 0.5);
  background-image: url("./Images/Right_f-1.svg");
  background-position: 20% 110%;
  background-size: 25px;
  background-repeat: no-repeat;
  font-family: Montserrat, sans-serif;
  color: black;
  font-size: 15px;
  font-weight: 400;

  padding: 9px 15px;
  outline: none;
}

.RightShoe:hover {
  background-color: black;
  color: hsla(0, 0%, 100%, 0.9);
}

.RightShoe:focus {
  outline: none;
}

.Reset {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 8em;
  height: 60%;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: black;
  padding-right: 25px;
  padding-left: 0px;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  border: 0px;
  border-right: 1px solid #fff;
  background-image: url("./Images/refresh-1.svg");
  background-position: 25% 50%;
  background-size: 25px;
  background-repeat: no-repeat;
  opacity: 0.5;
  font-family: Montserrat, sans-serif;
  color: #fff;
  font-size: 15px;
  font-weight: 400;
  text-align: right;
  outline: none;
  border-radius: 20px;
  margin: 1%;
}

.Reset:focus {
  outline: none;
}
.Reset:hover {
  opacity: 0.9;
}

.SaveReview {
  position: absolute;
  left: auto;
  top: 0%;
  right: 0%;
  bottom: 0%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 10em;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  border-left: 0.5px solid hsla(0, 0%, 100%, 0.5);
  background-color: hsla(0, 0%, 100%, 0.9);
  font-family: Montserrat, sans-serif;
  color: #000;
  font-weight: 400;
  text-align: center;

  border: 0px;
  outline: none;
}

.SaveReview:focus {
  outline: none;
}

.ProductDetail {
  position: absolute;
  right: 0%;
  top: 6em;
  bottom: 7.4em;
  width: 20em;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  color: #000;
  background-color: hsla(0, 0%, 100%, 0.9);
  border-left: 0.5px solid hsla(0, 0%, 100%, 0.5);
  outline: none;
}

.Customization {
  position: absolute;
  left: 0;
  top: auto;
  right: 0;
  bottom: 0;
  z-index: 5;
  height: 9em;
  background-color: transparent;
  outline: none;
}

.TabsMenu {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;

  position: relative;
  outline: none;
}

.Materials {
  margin-right: 5px;
  padding-right: 10px;
  padding-left: 10px;
  background-color: black;
  color: hsla(0, 0%, 100%, 0.5);
  text-align: center;

  max-width: 100%;
  display: inline-block;

  position: relative;
  display: inline-block;
  vertical-align: top;
  text-decoration: none;
  padding: 9px 10px;
  text-align: left;
  cursor: pointer;

  font-family: Montserrat, sans-serif;
  font-weight: 600;
  text-align: center;

  font-size: 14px;
  border: 0px;
  outline: none;
}

.Materials:focus {
  outline: none;
}

.Colours {
  margin-right: 5px;
  padding-right: 10px;
  padding-left: 10px;
  background-color: black;
  /* color: hsla(0, 0%, 100%, 0.5); */
  color: #fff;
  text-align: center;
  /* width: 8em; */
  max-width: 100%;
  display: inline-block;
  position: relative;
  display: inline-block;
  vertical-align: top;
  text-decoration: none;
  padding: 9px 10px;
  text-align: left;
  cursor: pointer;
  font-family: Montserrat, sans-serif;
  font-weight: 600;
  text-align: center;
  font-size: 15px;
  border: 0px;
  outline: none;
  /* border-radius: 20px; */
}

.Colours:focus {
  outline: none;
}

.Stickers {
  margin-right: 5px;
  padding-right: 10px;
  padding-left: 10px;
  background-color: black;
  color: hsla(0, 0%, 100%, 0.5);
  text-align: center;

  max-width: 100%;
  display: inline-block;

  position: relative;
  display: inline-block;
  vertical-align: top;
  text-decoration: none;
  padding: 9px 10px;
  text-align: left;
  cursor: pointer;

  font-family: Montserrat, sans-serif;
  font-weight: 600;
  text-align: center;

  font-size: 14px;
  border: 0px;
  outline: none;
}

.Stickers:focus {
  outline: none;
}

.Writing {
  margin-right: 5px;
  padding-right: 10px;
  padding-left: 10px;
  background-color: black;
  color: hsla(0, 0%, 100%, 0.5);
  text-align: center;

  max-width: 100%;
  display: inline-block;

  position: relative;
  display: inline-block;
  vertical-align: top;
  text-decoration: none;
  padding: 9px 10px;
  text-align: left;
  cursor: pointer;

  font-family: Montserrat, sans-serif;
  font-weight: 600;
  text-align: center;

  font-size: 14px;
  border: 0px;

  outline: none;
}

.Writing:focus {
  outline: none;
}

.TabsContent {
  border-top: 0.5px solid rgba(0, 0, 0, 0.5);
  height: 100%;
  position: relative;
  display: block;
  outline: none;
  overflow-x: hidden;
  overflow-y: hidden;
  white-space: nowrap;
}

.MaterialsContent {
  height: 100%;
  position: relative;
}

.ColoursContent {
  height: 100%;
  width: 100%;
  margin: auto;
  position: relative;
}

.StickersContent {
  height: 100%;
  position: relative;
}

.WritingContent {
  height: 100%;
  position: relative;
  color: rgb(232, 232, 232);
  margin-top: 25px;
}

.buttonMaterials {
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  color: rgb(232, 232, 232);
  background-color: black;
  border-style: solid;
  border-width: 1px;
  border-radius: 4px;
  border-color: rgba(255, 255, 255, 0.3);
  margin-right: 3px;
  margin-left: 3px;
  margin-top: 25px;
  outline: none;
}

.buttonMaterials:focus {
  outline: none;
}

.buttonColours {
  height: 3.5em;
  width: 3.5em;
  border-style: none;
  /* border-style: solid;
  border-width: 1px;
  border-radius: 4px;
  border-color: rgba(255, 255, 255, 0.3); */
  margin-right: 5px;
  margin-left: 5px;
  margin-top: 20px;
  background-position: center;
  background-size: cover;
  outline: none;
  /* display: inline-flex; */
}

.buttonColours:focus {
  border-style: solid;
  border-width: 4px;
  border-radius: 4px;
  border-color: grey;
}
@media screen and (max-width: 479px) {
  .Reset {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 6em;
    height: 40%;
    align-items: center;
    background-color: black;
    /* padding-right: 25px; */
    padding-left: 0px;
    -webkit-box-pack: end;
    -webkit-justify-content: flex-end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    border: 0px;
    border-right: 1px solid #fff;
    background-image: url("./Images/refresh-1.svg");
    background-position: 90% 50%;
    background-size: 14px;
    background-repeat: no-repeat;
    opacity: 0.5;
    font-family: Montserrat, sans-serif;
    color: #fff;
    font-size: 12px;
    font-weight: 400;
    text-align: left;
    outline: none;
    border-radius: 20px;
    margin: 2%;
  }
  .LogoImageDiv {
    position: absolute;
    height: 10%;
    width: 30%;
    top: 2%;
    left: 35%;
  }
  .TabsContent {
    width: 100%;
    overflow-x: hidden;
    height: 100%;
    white-space: initial;
    position: absolute !important;
    top: 35% !important;
  }
  .Customization {
    position: absolute;
    left: 0;
    top: auto;
    right: 0;
    bottom: 2% !important;
    z-index: 5;
    height: 9em;
    background-color: transparent;
    outline: none;
  }
  .buttonColours {
    height: 4em;
    width: 4.5em;
    /* height: 30% !important;
    width: 17% !important; */
    border-style: none;
    margin-right: 5px;
    margin-left: 5px;
    margin-top: 7px !important;
    background-position: center;
    background-size: cover;
    outline: none;
  }
  .Colours {
    /*width: 6em;*/
    /* font-size: 12px; */
  }
}
