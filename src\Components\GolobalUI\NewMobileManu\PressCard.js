import React, { useState } from "react";
import "./NewMobileManu.css";

function PressCard(props) {

    return (
        <div className="pressCardPage" style={{display: props.pressDisplay, 
            animation: props.pressAnimation}}>
            <div style={{position: "absolute", width: "100%", height: "40%", top: "0%"}} onClick={()=> props.pressSwitch()}>
            </div>
            <div style={{position: "absolute", width: "100%", height: "55%", top: "45%"}} onClick={()=> props.pressSwitch()}>
            </div>
            <div className="pressCard" style={{display: props.pressDisplay, 
                animation: props.pressAnimation}}>
                <div className="pressCardTitle">
                    Press
                </div>
                <div className="pressCardContent">
                <span><EMAIL></span><br></br>
                {/* <span><EMAIL></span> */}
                </div>
            </div>
        </div>
    );
}

export default PressCard;
