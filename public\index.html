<!DOCTYPE html>
<html lang="en">
  <head>
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script
      async
      src="https://www.googletagmanager.com/gtag/js?id=UA-277808276-1"
    ></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());
      gtag("config", "UA-277808276-1");
    </script>
    <meta name="google-site-verification" content="MXlMWIAWDegHLIadrrCJyre5dhEgKw4ldFVpur4U5ag" />
    <meta charset="utf-8" />
    <link rel="icon" type="image/svg+xml" href="%PUBLIC_URL%/logobottom.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
    <meta name="theme-color" content="#000000" />
    <link rel="apple-touch-icon" type="image/svg+xml" href="%PUBLIC_URL%/ModularCx-192x192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>INCORP</title>
    <meta name="description" content="Signifying 'In body' & we are all 'Incorporated' INCORP unites the body and mind by infusing art, beauty and celebration into our daily life. Our pure essential oil fragrance line 'I, YOU and We' was created to cleanse, support and guide.">
    <meta name="keywords" content="INCORP World, fragrance, perfume, scent, essential oils, aroma therapy, sculpture, collaboration, Kira Lillie, Sissel Tolaas, Figure II, Ugo Cacciatori, Vanessa Lillie, Emmanuel Crivelli, Pandora Graessl, Marco Panconesi, Kai Lillie, Samuel Reis, ModularCX, Aurélien Mabilat, Pol Agusti, Torso Solutions, Ohlman Consorti">
    <script src="https://code.jquery.com/pep/0.4.3/pep.js"></script>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
