// src/App.js
import React, { useEffect } from "react";
import "./Shop.css";
import Cart from "../../Cart";
import CustomPlayPause from "../MediaPlayer/CustomPlayPause ";
import { add, set } from "lodash";
import useState from "react-usestateref";
import { Media, Player, controls } from "react-media-player";
const { PlayPause, MuteUnmute, CurrentTime, Progress } = controls;

// import ""

function Shop(props) {
  const [ibigcounter, setibigcounter, ibigcounterref] = useState(0);
  const [ismallcounter, setismallcounter, ismallcounterref] = useState(0);
  const [youbigcounter, setyoubigcounter, youbigcounterref] = useState(0);
  const [yousmallcounter, setyousmallcounterl, yousmallcounterref] =
    useState(0);
  const [webigcounter, setwewebigcounter, webigcounterref] = useState(0);
  const [wesmallcouner, setwesmallcounerl, wesmallcounerref] = useState(0);
  const [Obsidiancounter, setObsidiancounter, Obsidiancounterref] = useState(0);
  const [Opalcounter, setOpalcounter, Opalcounterref] = useState(0);
  const [Tigercounter, setTigercounter, Tigercounterref] = useState(0);
  const [blackonyx, setblackonyx, blackonyxref] = useState(0);
  const [opal, setopal, opalref] = useState(0);
  const [tiger, settiger, tigerref] = useState(0);
  const [quantityI33ml, setquantityI33ml, quantityI33mlref] = useState(0);
  const [quantityYOU33ml, setquantityYOU33ml, quantityYOU33mlref] = useState(0);
  const [quantityWE33ml, setquantityWE33ml, quantityWE33mlref] = useState(0);
  const [quantityI5ml, setquantityI5ml, quantityI5mlref] = useState(0);
  const [quantityYOU5ml, setquantityYOU5ml, quantityYOU5mlref] = useState(0);
  const [quantityWE5ml, setquantityWE5ml, quantityWE5mlref] = useState(0);
  const [quantityObsidian, setquantityObsidian, quantityObsidianref] = useState(0);
  const [quantityOpal, setquantityOpal, quantityOpalref] = useState(0);
  const [quantityTiger, setquantityTiger, quantityTigerref] = useState(0);
  const [addButtonIBig, setaddButtonIBig] = useState("<EMAIL>");
  const [addButtonISmall, setaddButtonISmall] = useState("<EMAIL>");
  const [addButtonYouBig, setaddButtonYouBig] = useState("<EMAIL>");
  const [addButtonYouSmall, setaddButtonYouSmall] = useState("<EMAIL>");
  const [addButtonWeBig, setaddButtonWeBig] = useState("<EMAIL>");
  const [addButtonWeSmall, setaddButtonWeSmall] = useState("<EMAIL>");
  const [addButtonObsidian, setaddButtonObsidian] = useState("<EMAIL>");
  const [addButtonOpal, setaddButtonOpal] = useState("<EMAIL>");
  const [addButtonTiger, setaddButtonTiger] = useState("<EMAIL>");

  const [ToolOpacity, setToolOpacity] = useState(0.5);
  const [MessageToolsDisplay, setMessageToolsDisplay] = useState("");
  // constructor(props) {
  //   super(props);
  // }
  function decodeGraphQLId(encodedId) {

    // Decode base64 string
    const decoded = atob(encodedId);
  
    // Convert to unicode string
    const id = decodeURIComponent(escape(decoded));
  
    return id;
  
  }
  
  useEffect(() => {
    // This gets called after every render, by default
    // console.log(props.shopData, "shopDatashopDatashopData");

    if (props.shopData != undefined) {
      for (var i = 0; i < props.shopData.products.edges.length; i++) {
        if (
          props.shopData.products.edges[i].node.id ==
          decodeGraphQLId("Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0Lzc1Nzk0NTIxNzg2OTA=")
        ) {
          setquantityI33ml(
            props.shopData.products.edges[i].node.variants.edges[0].node.quantityAvailable
          );
        } else if (
          props.shopData.products.edges[i].node.id ==
          decodeGraphQLId("Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0Lzc1Nzk0NTMyNjAwMzQ=")
        ) {
          setquantityYOU33ml(
            props.shopData.products.edges[i].node.variants.edges[0].node.quantityAvailable
          );
        } else if (
          props.shopData.products.edges[i].node.id ==
          decodeGraphQLId("Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0Lzc1Nzk0NTQ5NjM5NzA=")
        ) {
          setquantityWE33ml(
            props.shopData.products.edges[i].node.variants.edges[0].node.quantityAvailable
          );
        } else if (
          props.shopData.products.edges[i].node.id ==
          decodeGraphQLId("Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0Lzc1ODM2MjE0NDc5Mzg=")
        ) {
          setquantityI5ml(
            props.shopData.products.edges[i].node.variants.edges[0].node.quantityAvailable
          );
        } else if (
          props.shopData.products.edges[i].node.id ==
          decodeGraphQLId("Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0Lzc1ODM2MjExMjAyNTg=")
        ) {
          setquantityYOU5ml(
            props.shopData.products.edges[i].node.variants.edges[0].node.quantityAvailable
          );
        } else if (
          props.shopData.products.edges[i].node.id ==
          decodeGraphQLId("Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0Lzc1ODM2MjIwMDQ5OTQ=")
        ) {
          setquantityWE5ml(
            props.shopData.products.edges[i].node.variants.edges[0].node.quantityAvailable
          );
        } else if (props.shopData.products.edges[i].node.id ==
          decodeGraphQLId("Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0Lzc1ODM2NDc1NjQwMzQ=")
        ) {

          setquantityObsidian(
            props.shopData.products.edges[i].node.variants.edges[0].node.quantityAvailable
          );
        } else if (props.shopData.products.edges[i].node.id ==
          decodeGraphQLId("Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0Lzc1ODM2NTM1OTMzNDY=")
        ) {
          setquantityOpal(
            props.shopData.products.edges[i].node.variants.edges[0].node.quantityAvailable
          );
        }
        // console.log(props.shopData.products.edges[i].node)
        // else if(props.shopData.products.edges[i].node.id=="Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0Lzc1Nzk0NTIxNzg2OTA="){
        //   setquantityI33ml(props.shopData.products.edges[i].node.variants.edges[0].node.quantityAvailable)

        // }
        // else if(props.shopData.products.edges[i].node.id=="Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0Lzc1Nzk0NTIxNzg2OTA="){
        //   setquantityI33ml(props.shopData.products.edges[i].node.variants.edges[0].node.quantityAvailable)

        // }
        // console.log(props.shopData.products.edges[i].node.title)
      }
    }
    // (the first one, and every one after that)
    if (props.totalitems > 0) {
      setToolOpacity(1);
      setMessageToolsDisplay("none");
    } else if (props.totalitems == 0) {
      setToolOpacity(0.5);
      setMessageToolsDisplay("");
    }
    // If you want to implement componentWillUnmount,
    // return a function from here, and React will call
    // it prior to unmounting.
    window.addEventListener("buy-one", buyOne);
    window.addEventListener("remove-item-from-cart", removeItemFromCart);
  });

  // useEffect = (() =>{
  //   window.addEventListener("remove-item-from-cart", removeItemFromCart);
  //   return () => {
  //     window.removeEventListener("remove-item-from-cart", removeItemFromCart);
  //   };
  // })

  const Changerimg = (color, product) => {
    if (color == "black") {
      if (product == "IBig"){
        setaddButtonIBig("add <EMAIL>");
      }
      if (product == "ISmall"){
        setaddButtonISmall("add <EMAIL>");
      }
      if (product == "YouBig"){
        setaddButtonYouBig("add <EMAIL>");
      }
      if (product == "YouSmall"){
        setaddButtonYouSmall("add <EMAIL>");
      }
      if (product == "WeBig"){
        setaddButtonWeBig("add <EMAIL>");
      }
      if (product == "WeSmall"){
        setaddButtonWeSmall("add <EMAIL>");
      }
      if (product == "Obsidian"){
        setaddButtonObsidian("add <EMAIL>");
      }
      if (product == "Opal"){
        setaddButtonOpal("add <EMAIL>");
      }
      if (product == "Tiger"){
        setaddButtonTiger("add <EMAIL>");
      }
    }
    if (color == "white") {
      if (product == "IBig"){
        setaddButtonIBig("<EMAIL>");
      }
      if (product == "ISmall"){
        setaddButtonISmall("<EMAIL>");
      }
      if (product == "YouBig"){
        setaddButtonYouBig("<EMAIL>");
      }
      if (product == "YouSmall"){
        setaddButtonYouSmall("<EMAIL>");
      }
      if (product == "WeBig"){
        setaddButtonWeBig("<EMAIL>");
      }
      if (product == "WeSmall"){
        setaddButtonWeSmall("<EMAIL>");
      }
      if (product == "Obsidian"){
        setaddButtonObsidian("<EMAIL>");
      }
      if (product == "Opal"){
        setaddButtonOpal("<EMAIL>");
      }
      if (product == "Tiger"){
        setaddButtonTiger("<EMAIL>");
      }
    }
  }

  const addtoCart = (lineItemId, item) => {
    if (item == 7) {
      // console.log(props.ibigcounter);
      // if (
      //   ibigcounter +
      //     ismallcounter +
      //     youbigcounter +
      //     webigcounter +
      //     wesmallcouner +
      //     props.ibigcounter +
      //     props.ismallcounter +
      //     props.youbigcounter +
      //     props.yousmallcounter +
      //     props.webigcounter +
      //     props.wesmallcouner +
      //     yousmallcounter -
      //     (Obsidiancounter + Opalcounter) >
      //   0
      // ) {
      setObsidiancounter(Obsidiancounter + 1);
      // }
    }
    if (item == 8) {
      // if (
      //   ibigcounter +
      //     ismallcounter +
      //     youbigcounter +
      //     webigcounter +
      //     wesmallcouner +
      //     props.ibigcounter +
      //     props.ismallcounter +
      //     props.youbigcounter +
      //     props.yousmallcounter +
      //     props.webigcounter +
      //     props.wesmallcouner +
      //     yousmallcounter -
      //     (Obsidiancounter + Opalcounter) >
      //   0
      // ) {
      setOpalcounter(Opalcounter + 1);
      // }
    }
    if (item == 8) {
      setTigercounter(Tigercounter + 1);
    }
    if (item == 1) {
      setibigcounter(ibigcounter + 1);
    } else if (item == 2) {
      setismallcounter(ismallcounter + 1);
    } else if (item == 3) {
      setyoubigcounter(youbigcounter + 1);
    } else if (item == 4) {
      setyousmallcounterl(yousmallcounter + 1);
    } else if (item == 5) {
      setwewebigcounter(webigcounter + 1);
    } else if (item == 6) {
      setwesmallcounerl(wesmallcouner + 1);
    }
    props.addVariantToCart(lineItemId, 1);
  };

  const removetoCart = (lineItemId, item) => {
    if (item == 1 && ibigcounter > 0) {
      setibigcounter(ibigcounter - 1);
      props.updateLineItemInCart(lineItemId, ibigcounterref);
      // console.log(ibigcounterref)
    } else if (item == 2 && ismallcounter > 0) {
      setismallcounter(ismallcounter - 1);
      props.updateLineItemInCart(lineItemId, ismallcounterref);
    } else if (item == 3 && youbigcounter > 0) {
      setyoubigcounter(youbigcounter - 1);
      props.updateLineItemInCart(lineItemId, youbigcounterref);
    } else if (item == 4 && yousmallcounter > 0) {
      setyousmallcounterl(yousmallcounter - 1);
      props.updateLineItemInCart(lineItemId, yousmallcounterref);
    } else if (item == 5 && webigcounter > 0) {
      setwewebigcounter(webigcounter - 1);
      props.updateLineItemInCart(lineItemId, webigcounterref);
    } else if (item == 6 && wesmallcouner > 0) {
      setwesmallcounerl(wesmallcouner - 1);
      props.updateLineItemInCart(lineItemId, wesmallcounerref);
    } else if (item == 7 && Obsidiancounter > 0) {
      setObsidiancounter(Obsidiancounter - 1)
      // console.log(lineItemId, Obsidiancounterref)
      props.updateLineItemInCart(lineItemId, Obsidiancounterref);

    } else if (item == 8 && Opalcounter > 0) {
      setOpalcounter(Opalcounter - 1)
      props.updateLineItemInCart(lineItemId, Opalcounterref);

    }
  };

  const addToolToCart = (lineItemId, Type) => {
    if (ToolOpacity > 0.6) {
      if (Type == 1) {
        setblackonyx(blackonyx + 1);
        props.addVariantToCart(lineItemId, blackonyx);
      } else if (Type == 2) {
        setopal(opal + 1);
        props.addVariantToCart(lineItemId, opal);
      }
    }
  };

  const removeTooltoCart = (lineItemId, item) => {
    if (item == 1 && blackonyx > 0) {
      setblackonyx(blackonyx - 1);
      props.updateLineItemInCart(lineItemId, blackonyxref);
      // console.log(blackonyxref)
    } else if (item == 2 && opal > 0) {
      setopal(opal - 1);
      props.updateLineItemInCart(lineItemId, opalref);
    }
  };
  // const updateLineItemInCart = (lineItemId) => {
  //   const variables = { checkoutId:checkout.id, lineItemIds: [lineItemId] };
  //   lineItemRemoveMutation({ variables });
  // };

  const buyOne = (event) =>{
    if (event.detail === "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE2ODAxNjEzMA=="){
      setObsidiancounter(Obsidiancounter + 1);
    } else if (event.detail === "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE5Mjc1NTk3MA=="){
      setOpalcounter(Opalcounter + 1);
    } else if (event.detail === "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE1OTEzNjAwMg=="){
      setibigcounter(ibigcounter + 1);
    } else if (event.detail === "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM3MTQ4OTY2MTE4Ng=="){
      setyoubigcounter(youbigcounter + 1);
    } else if (event.detail === "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM3MTQ5NTM5NTU4Ng=="){
      setwewebigcounter(webigcounter + 1);
    } else if (event.detail === "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODAzMzQ3MDcyMg=="){
      setismallcounter(ismallcounter + 1);
    } else if (event.detail === "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE2MDExOTA0Mg=="){
      setyousmallcounterl(yousmallcounter + 1);
    }  else if (event.detail === "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE1OTg4OTY2Ng=="){
      setwesmallcounerl(wesmallcouner + 1);
    } else if (event.detail === "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80Mzc3Nzk4MDg1ODYyNg=="){
      setTigercounter(Tigercounter + 1);
    }
  }

  const removeItemFromCart = (event) =>{
    // console.log(event.detail)
    if (event.detail === "Obsidian Tool"){
      setObsidiancounter(0);
    } else if (event.detail === "Opal Tool"){
      setOpalcounter(0);
    } else if (event.detail === "I / 33ml"){
      setibigcounter(0);
    } else if (event.detail === "YOU /  33ml"){
      setyoubigcounter(0);
    } else if (event.detail === "WE / 33ml"){
      setwewebigcounter(0);
    } else if (event.detail === "I  / 5ml"){
      setismallcounter(0);
    } else if (event.detail === "YOU / 5ml"){
      setyousmallcounterl(0);
    }  else if (event.detail === "WE / 5ml"){
      setwesmallcounerl(0);
    } else if (event.detail === "Tigers eye Tool"){
      setTigercounter(0);
    }
  }

  // render() {
  const StartPlaying = () => { };

  return (
    <div className="Shop">
      <div className="ShopPageContent">
        <div className="product">
          <div className="ProductTitle">
            <span>
              <b>I</b>
              <div className="VoiceContainer">
                <Media>
                  <div className="media">
                    <CustomPlayPause />
                    <Player src="I (vol adj & cut)- 9722, 5.48 PM.mp3" />
                    <div className="media-controls"></div>
                  </div>
                </Media>
              </div>
            </span>
            <p>
              I am focused.
              <br /> I am energized
            </p>
          </div>
          <div className="ProductImage">
            <div className="displayproduct">
              {" "}
              <img src="I_trans.png" style={{cursor: "pointer"}} onClick={() => props.goToProduct("I Big")} alt="" />
              <span>33ml &nbsp; €1,415</span>
              {/* <div className="counterEdited">
                <button
                  className="reduceEdited"
                  onClick={() =>
                    removetoCart(

                      "Z2lkOi8vc2hvcGlmeS9DaGVja291dExpbmVJdGVtLzQyMzg4MTU5MTM2MDAyMD9jaGVja291dD01NzZkMTQ1MGQ1ZTVjN2M1NTdmNDdkM2NkYWJlNjA5Ng==",
                      1
                    )
                  }>
                  -
                </button>
                <span style={{marginTop: "5%"}}>{ibigcounter}</span>
                <button
                  className="increaseEdited"
                  onClick={() =>
                    addtoCart(
                      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE1OTEzNjAwMg==",
                      1
                    )
                  }>
                  +
                </button>
              </div> */}
              <img className="addButton" src={addButtonIBig} style={{cursor: "pointer"}}
                onMouseEnter={() => Changerimg("black", "IBig")}
                onMouseLeave={() => Changerimg("white", "IBig")}
               onClick={() =>
                    addtoCart(
                      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE1OTEzNjAwMg==",
                      1
                    )
                  } alt="" />
              <span
                style={{
                  position: "absolute",
                  bottom: "0",
                  marginBottom: "12%",
                  fontStyle: "italic",
                }}>
                {quantityI33ml}/50 available{" "}
              </span>
              <span
                style={{
                  position: "absolute",
                  bottom: "0",
                  marginBottom: "8%",
                  fontStyle: "italic",
                  fontSize: "0.68vw"
                }}>
                Limited edition of 200
              </span>
            </div>
            <div className="displayproductsmall">
              <img id="prettysmall" src="ISmall.png" style={{cursor: "pointer"}} onClick={() => props.goToProduct("I Small")}/>
              <span>5ml &nbsp; €565</span>
              {/* <div className="counterEditedsmall">
                <button
                  className="reduceEdited"
                  onClick={() =>
                    removetoCart(
                      "Z2lkOi8vc2hvcGlmeS9DaGVja291dExpbmVJdGVtLzQyMzg4MDMzNDcwNzIyMD9jaGVja291dD03YWU5MjhjYjMyNWRmMzk0MTkyMjNhMGNlOWFkZTg1Ng==",
                      2
                    )
                  }>
                  -
                </button>
                <span className="numbercounter" style={{marginTop: "5%"}}>{ismallcounter}</span>
                <button
                  className="increaseEdited"
                  onClick={() =>
                    addtoCart(
                      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODAzMzQ3MDcyMg==",
                      2
                    )
                  }>
                  +
                </button>
              </div> */}
              <img className="addButtonV2" src={addButtonISmall} style={{cursor: "pointer"}}
                onMouseEnter={() => Changerimg("black", "ISmall")}
                onMouseLeave={() => Changerimg("white", "ISmall")}
               onClick={() =>
                    addtoCart(
                      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODAzMzQ3MDcyMg==",
                      2
                    )
                  } alt="" />
              <span
                style={{
                  position: "absolute",
                  bottom: "0",
                  marginBottom: "12%",
                  fontStyle: "italic",
                }}>
                {quantityI5ml}/35 available{" "}
              </span>
              <span
                style={{
                  position: "absolute",
                  bottom: "0",
                  marginBottom: "8%",
                  fontStyle: "italic",
                  fontSize: "0.68vw"

                }}>
                Limited edition of 1000
              </span>
            </div>
          </div>
          <div style={{ fontStyle: "italic" }} className="ProductDiscription">
            {/* With CLEANSING Thyme I eliminate distraction while SUPPORTING with
            grounding Vetiver and healing Myrrh. Antioxidant Melissa vitalizes,
            GUIDING me towards my authentic inner self. <br /> <br />
            Limited edition of 33ml / 200 - 5ml with chain / 1000
            <br /> <br /> */}
            <div className="DetailsNewTitles">
              <div className="NewTitle">
                *MUSK<br /> & ROOTS
              </div>
              <div className="NewDesc">
                CLEANSE w/ Thyme<br />
                SUPPORT w/ Vetiver, Myrrh<br />
                GUIDE w/ Melissa
              </div>
            </div>
            <div className="ForAllMSG">
              Made with 100% pure and 100% natural Chemotyped cruently free Essential oils. All raw materials analyzed HPLChromatography and Mass Spectrometry, the highest quality contrlos.
              {/* Pure essential oils mixed with Apricot oil & stabilized with vitamin E. Total of 6 ingredients. Made with 100% pure and 100% natural Chemotyped cruelty free Essential oils. */}
            </div>
          </div>
        </div>
        <div className="product">
          <div className="ProductTitle">
            <span>
              <b> YOU</b>
              <div className="VoiceContainer">
                <Media>
                  <div className="media">
                    <CustomPlayPause />
                    <Player src="You2 (vol adj & cut) - 9_7_22, 5.59 PM.mp3" />{" "}
                  </div>
                </Media>
              </div>
            </span>
            <p>
              YOU are unconditional love.
              <br /> YOU are light
            </p>
          </div>
          <div className="ProductImage">
            <div className="displayproduct">
              <img id="editimg" src="You_trans.png" alt="" style={{cursor: "pointer"}} onClick={() => props.goToProduct("You Big")}/>
              <span>33ml &nbsp; €1,485</span>
              {/* <div className="counterEdited">
                <button
                  className="reduceEdited"
                  onClick={() =>
                    removetoCart(
                      "Z2lkOi8vc2hvcGlmeS9DaGVja291dExpbmVJdGVtLzQyMzcxNDg5NjYxMTg2MD9jaGVja291dD0zODMxYjAyYjM4YjQxNjdmZWFjYmY5NjBmYTIzNzAyYw==",
                      3
                    )
                  }>
                  -
                </button>
                <span style={{marginTop: "5%"}}>{youbigcounter}</span>
                <button
                  className="increaseEdited"
                  onClick={() =>
                    addtoCart(
                      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM3MTQ4OTY2MTE4Ng==",
                      3
                    )
                  }>
                  +
                </button>
              </div> */}
              <img className="addButton" src={addButtonYouBig} style={{cursor: "pointer"}}
                onMouseEnter={() => Changerimg("black", "YouBig")}
                onMouseLeave={() => Changerimg("white", "YouBig")}
               onClick={() =>
                    addtoCart(
                      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM3MTQ4OTY2MTE4Ng==",
                      3
                    )
                  } alt="" />
              <span
                style={{
                  position: "absolute",
                  bottom: "0",
                  marginBottom: "12%",
                  fontStyle: "italic",
                }}>
                {quantityYOU33ml}/50 available{" "}
              </span>
              <span
                style={{
                  position: "absolute",
                  bottom: "0",
                  marginBottom: "8%",
                  fontStyle: "italic",
                  fontSize: "0.68vw"

                }}>
                Limited edition of 200
              </span>
            </div>
            <div className="displayproductsmall">
              <img id="prettysmall" src="YouSmall.png" alt="" style={{cursor: "pointer"}} onClick={() => props.goToProduct("You Small")}/>
              <span>5ml &nbsp; €635</span>
              {/* <div className="counterEditedsmall">
                <button
                  className="reduceEdited"
                  onClick={() =>
                    removetoCart(
                      "Z2lkOi8vc2hvcGlmeS9DaGVja291dExpbmVJdGVtLzQyMzg4MTYwMTE5MDQyMD9jaGVja291dD0xNDVhYzYzMzY1YzI4MGZjMjEyZmVkNzA1YTNiZmUzNQ==",
                      4
                    )
                  }>
                  -
                </button>
                <span style={{marginTop: "5%"}}>{yousmallcounter}</span>
                <button
                  className="increaseEdited"
                  onClick={() =>
                    addtoCart(
                      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE2MDExOTA0Mg==",
                      4
                    )
                  }>
                  +
                </button>
              </div> */}
              <img className="addButtonV2" src={addButtonYouSmall} style={{cursor: "pointer"}}
                onMouseEnter={() => Changerimg("black", "YouSmall")}
                onMouseLeave={() => Changerimg("white", "YouSmall")}
               onClick={() =>
                    addtoCart(
                      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE2MDExOTA0Mg==",
                      4
                    )
                  } alt="" />
              <span
                style={{
                  position: "absolute",
                  bottom: "0",
                  marginBottom: "12%",
                  fontStyle: "italic",
                }}>
                {quantityYOU5ml}/35 available{" "}
              </span>
              <span
                style={{
                  position: "absolute",
                  bottom: "0",
                  marginBottom: "8%",
                  fontStyle: "italic",
                  fontSize: "0.68vw"

                }}>
                Limited edition of 1000
              </span>
            </div>
          </div>
          <div style={{ fontStyle: "italic" }} className="ProductDiscription">
            {/* With CLEANSING Lime and Coriander; you stimulate your body and mind.
            You SUPPORT emotional stability with Sandalwood, opening your heart
            with Rose and GUIDING you towards divine inner love.
            <br /> <br />
            Limited edition of 33ml / 200 - 5ml with chain / 1000
            <br /> <br /> */}
            <div className="DetailsNewTitles">
              <div className="NewTitle">
                *WOOD<br /> & ROSE
              </div>
              <div className="NewDesc">
                CLEANSE w/ Lime, Coriander<br />
                SUPPORT w/ Sandalwood<br />
                GUIDE w/ Rose
              </div>
            </div>
            <div className="ForAllMSG">
              Made with 100% pure and 100% natural Chemotyped cruently free Essential oils. All raw materials analyzed HPLChromatography and Mass Spectrometry, the highest quality contrlos.
            </div>
          </div>
        </div>
        <div className="product">
          <div className="ProductTitle">
            <span>
              <b>WE</b>
              <div className="VoiceContainer">
                <Media>
                  <div className="media">
                    <CustomPlayPause />
                    <Player src="we (vol adj & cut)- 9722, 5.49 PM.mp3" />
                  </div>
                </Media>
              </div>
            </span>
            <p>
              WE are all encompassing.
              <br /> WE are independent
            </p>
          </div>
          <div className="ProductImage">
            <div className="displayproduct">
              <img id="editimg" src="We_trans.png" style={{cursor: "pointer"}} onClick={() => props.goToProduct("We Big")}/>
              <span>33ml &nbsp; €1,385</span>
              {/* <div className="counterEdited">
                <button
                  className="reduceEdited"
                  onClick={() =>
                    removetoCart(
                      "Z2lkOi8vc2hvcGlmeS9DaGVja291dExpbmVJdGVtLzQyMzcxNDk1Mzk1NTg2MD9jaGVja291dD0wOWNkNGM5YWRhY2UxMDRkZDg5MmNjY2U2NzRmNjM0OA==",
                      5
                    )
                  }>
                  -
                </button>
                <span style={{marginTop: "5%"}}>{webigcounter}</span>
                <button
                  className="increaseEdited"
                  onClick={() =>
                    addtoCart(
                      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM3MTQ5NTM5NTU4Ng==",
                      5
                    )
                  }>
                  +
                </button>
              </div> */}
              <img className="addButton" src={addButtonWeBig} style={{cursor: "pointer"}}
                onMouseEnter={() => Changerimg("black", "WeBig")}
                onMouseLeave={() => Changerimg("white", "WeBig")}
               onClick={() =>
                    addtoCart(
                      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM3MTQ5NTM5NTU4Ng==",
                      5
                    )
                  } alt="" />
              <span
                style={{
                  position: "absolute",
                  bottom: "0",
                  marginBottom: "12%",
                  fontStyle: "italic",
                }}>
                {quantityWE33ml}/50 available{" "}
              </span>
              <span
                style={{
                  position: "absolute",
                  bottom: "0",
                  marginBottom: "8%",
                  fontStyle: "italic",
                  fontSize: "0.68vw"

                }}>
                Limited edition of 200
              </span>
            </div>
            <div className="displayproductsmall">
              <img id="prettysmall" src="WeSmall.png" style={{cursor: "pointer"}} onClick={() => props.goToProduct("We Small")}/>
              <span>5ml &nbsp; €535</span>
              {/* <div className="counterEditedsmall">
                <button
                  className="reduceEdited"
                  onClick={() =>
                    removetoCart(
                      "Z2lkOi8vc2hvcGlmeS9DaGVja291dExpbmVJdGVtLzQyMzg4MTU5ODg5NjY2MD9jaGVja291dD0zYzBiMzU5NjcxOWY3ODQxYmNmOTU2ZTg4Zjk2NjUzYQ==",
                      6
                    )
                  }>
                  -
                </button>
                <span style={{marginTop: "5%"}}>{wesmallcouner}</span>
                <button
                  className="increaseEdited"
                  onClick={() =>
                    addtoCart(
                      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE1OTg4OTY2Ng==",
                      6
                    )
                  }>
                  +
                </button>
              </div> */}
              <img className="addButtonV2" src={addButtonWeSmall} style={{cursor: "pointer"}}
                onMouseEnter={() => Changerimg("black", "WeSmall")}
                onMouseLeave={() => Changerimg("white", "WeSmall")}
               onClick={() =>
                    addtoCart(
                      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE1OTg4OTY2Ng==",
                      6
                    )
                  } alt="" />
              <span
                style={{
                  position: "absolute",
                  bottom: "0",
                  marginBottom: "12%",
                  fontStyle: "italic",
                }}>
                {quantityWE5ml}/35 available{" "}
              </span>
              <span
                style={{
                  position: "absolute",
                  bottom: "0",
                  marginBottom: "8%",
                  fontStyle: "italic",
                  fontSize: "0.68vw"

                }}>
                Limited edition of 1000
              </span>
            </div>
          </div>
          <div style={{ fontStyle: "italic" }} className="ProductDiscription">
            {/* With CLEANSING Coriander, Orange and Thyme; we ignite your creative
            mind and act as an aphrodisiac. We SUPPORT tranquility with Vetiver
            while Roman Chamomile GUIDES us towards inner equilibrium.
            <br />
            <br />
            Limited edition of 33ml / 200 - 5ml with chain / 1000
            <br />
            <br /> */}
            <div className="DetailsNewTitles">
              <div className="NewTitle">
                *RAIN<br /> & ASH
              </div>
              <div className="NewDesc">
                CLEANSE w/ Coriander, Orange, Thyme<br />
                SUPPORT w/ Vetiver<br />
                GUIDE w/ Chamomile
              </div>
            </div>
            <div className="ForAllMSG">
              Made with 100% pure and 100% natural Chemotyped cruently free Essential oils. All raw materials analyzed HPLChromatography and Mass Spectrometry, the highest quality contrlos.
            </div>
          </div>
        </div>
        <div className="product" style={{ border: "none" }}>
          <div className="ProductTitle" style={{ opacity: "1" }}>
            <span>
              <b>TOOLS</b>
            </span>
            <p>Hand carved stone massage tools</p>
            <div className="ForAllMSG" style={{textAlign: "left"}}>
              Shaped to fit into the 33ml bottle; used for acupressure, massage or as a spoon for caviar.
            </div>
          </div>
          {/* <div
            className="MessageTools"
            style={{ display: MessageToolsDisplay }}>
            Tools are available with any perfumes
          </div> */}
          <div className="ProductImage" style={{ opacity: "1", gridTemplateColumns: "repeat(3, 1fr)" }}>
            <div className="displayproduct">
              <img id="toolimg1" style={{ cursor: "pointer" }} src="obsidian tool.png" onClick={() => props.goToProduct("Tool Obsidian")}/>
              <span style={{ marginBottom: "17%", marginLeft: "20%" }}>Obsidian €125</span>
              {/* <div className="counterEdited" style={{ marginBottom: "23%", marginLeft: "20%" }}>
                <button
                  className="reduce"
                  onClick={() =>
                    removetoCart(
                      "Z2lkOi8vc2hvcGlmeS9DaGVja291dExpbmVJdGVtLzQyMzg4MTY4MDE2MTMwMD9jaGVja291dD00YjA0ODNjYjlkMzMwOTA4ZGU4MjA0NDdjMWQzYTJiMw==",
                      7
                    )
                  }>
                  -
                </button>
                <span className="numbercounter" style={{marginTop: "5%"}}>{Obsidiancounter}</span>
                <button
                  className="increase"
                  onClick={() =>
                    addtoCart(
                      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE2ODAxNjEzMA==",
                      7
                    )
                  }>
                  +
                </button>
              </div> */}
              <img className="addButtonV3" src={addButtonObsidian} style={{cursor: "pointer"}}
                onMouseEnter={() => Changerimg("black", "Obsidian")}
                onMouseLeave={() => Changerimg("white", "Obsidian")}
               onClick={() =>
                    addtoCart(
                      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE2ODAxNjEzMA==",
                      7
                    )
                  } alt="" />
              <span
                style={{
                  position: "absolute",
                  bottom: "0",
                  marginBottom: "12%",
                  marginLeft: "20%",
                  fontStyle: "italic",
                }}>
                {quantityObsidian}/80 available{" "}
              </span>
            </div>
            <div className="displayproduct">
              <img id="toolimg" style={{cursor: "pointer" }} src="Opal_lg.png" onClick={() => props.goToProduct("Tool Opal")}/>
              <span id="editcounter" style={{ marginLeft: "25%", marginBottom: "17%" }}>Opal €145</span>
              {/* <div className="counterEdited" style={{ marginLeft: "25%", marginBottom: "23%" }} id="editcounter">
                <button
                  className="reduce"
                  onClick={() =>
                    removetoCart(
                      "Z2lkOi8vc2hvcGlmeS9DaGVja291dExpbmVJdGVtLzQyMzg4MTkyNzU1OTcwMD9jaGVja291dD00YjA0ODNjYjlkMzMwOTA4ZGU4MjA0NDdjMWQzYTJiMw==",
                      8
                    )
                  }>
                  -
                </button>
                <span className="numbercounter" style={{marginTop: "5%"}}>{Opalcounter}</span>
                <button
                  className="increase"
                  onClick={() =>
                    addtoCart(
                      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE5Mjc1NTk3MA==",
                      8
                    )
                  }>
                  +
                </button>
              </div> */}
              <img className="addButtonV4" src={addButtonOpal} style={{cursor: "pointer"}}
                onMouseEnter={() => Changerimg("black", "Opal")}
                onMouseLeave={() => Changerimg("white", "Opal")}
               onClick={() =>
                    addtoCart(
                      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE5Mjc1NTk3MA==",
                      8
                    )
                  } alt="" />
              <span
                style={{
                  position: "absolute",
                  bottom: "0",
                  marginBottom: "12%",
                  fontStyle: "italic",
                  marginLeft: "25%",
                }}>
                {quantityOpal}/80 available{" "}
              </span>
            </div>
            <div className="displayproduct">
              <img id="toolimg2"  src="tigerseyetool_lrg.png" onClick={() => props.goToProduct("Tool Tiger")}/>
              <span id="editcounter" style={{ marginLeft: "30%", marginBottom: "17%" }}>Tigers Eye €165</span>
              {/* <div className="counterEdited" style={{ marginLeft: "30%", marginBottom: "23%" }} id="editcounter">
                <button
                  className="reduce"
                  onClick={() =>
                    removetoCart(
                      "Z2lkOi8vc2hvcGlmeS9DaGVja291dExpbmVJdGVtLzQyMzg4MTkyNzU1OTcwMD9jaGVja291dD00YjA0ODNjYjlkMzMwOTA4ZGU4MjA0NDdjMWQzYTJiMw==",
                      8
                    )
                  }>
                  -
                </button>
                <span className="numbercounter" style={{marginTop: "5%"}}>{Opalcounter}</span>
                <button
                  className="increase"
                  onClick={() =>
                    addtoCart(
                      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE5Mjc1NTk3MA==",
                      8
                    )
                  }>
                  +
                </button>
              </div> */}
              <img className="addButtonV5" src={addButtonTiger} style={{cursor: "pointer"}}
                onMouseEnter={() => Changerimg("black", "Tiger")}
                onMouseLeave={() => Changerimg("white", "Tiger")}
               onClick={() =>
                    addtoCart(
                      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80Mzc3Nzk4MDg1ODYyNg==",
                      9
                    )
                  } alt="" />
              <span
                style={{
                  position: "absolute",
                  bottom: "0",
                  marginBottom: "12%",
                  fontStyle: "italic",
                  marginLeft: "30%",
                }}>
                {quantityOpal}/80 available{" "}
              </span>
            </div>
          </div>
          <div className="ProductDiscription" style={{ opacity: "1" }}>
            <div className="DetailsNewTitles TOOLY">
              <div className="NewTitle">
                *OBSIDIAN
              </div>
              <div className="NewDesc TOOLY">
                is a strongly protective stone, helpful for sensitive
                people. This stone clears confusion and grounds us into the present
                moment.
              </div>
            </div>
            <div className="DetailsNewTitles TOOLY">
              <div className="NewTitle">
                *OPAL
              </div>
              <div className="NewDesc TOOLY">
                enhances cosmic consciousness and induces psychic and mystical
                visions. It stimulates originality and creativity.
              </div>
            </div>
            <div className="DetailsNewTitles TOOLY">
              <div className="NewTitle">
                *TIGERS EYE
              </div>
              <div className="NewDesc TOOLY">
              traditionally carried as a protective amulet, Tigers Eye brings motivation, balance and confidence to the wearer.
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="shopdiv">
        <Cart
          updateLineItemInCart={props.updateLineItemInCart}
          addVariantToCart={props.addVariantToCart}
          checkout={props.checkout}
          isCartOpen={props.isCartOpen}
          handleCartClose={props.handleCartClose}
          customerAccessToken={props.customerAccessToken}
          ShopPageDisplaySwitch={props.ShopPageDisplaySwitch}
          updateLineItemInCartLessDesktop={props.updateLineItemInCartLessDesktop}
          updateLineItemInCartAddMobile={props.updateLineItemInCartAddMobile}
          updateLineItemInCartLessMobile={props.updateLineItemInCartLessMobile}

          ibigcounter={ibigcounter}
          ibigcounterref={ibigcounterref}
          setibigcounter={setibigcounter}
          ismallcounter={ismallcounter}
          ismallcounterref={ismallcounterref}
          setismallcounter={setismallcounter}
          youbigcounter={youbigcounter}
          youbigcounterref={youbigcounterref}
          setyoubigcounter={setyoubigcounter}
          yousmallcounter={yousmallcounter}
          yousmallcounterref={yousmallcounterref}
          setyousmallcounterl={setyousmallcounterl}
          webigcounter={webigcounter}
          webigcounterref={webigcounterref}
          setwewebigcounter={setwewebigcounter}
          wesmallcouner={wesmallcouner}
          wesmallcounerref={wesmallcounerref}
          setwesmallcounerl={setwesmallcounerl}
          Obsidiancounter={Obsidiancounter}
          Obsidiancounterref={Obsidiancounterref}
          setObsidiancounter={setObsidiancounter}
          Opalcounter={Opalcounter}
          Opalcounterref={Opalcounterref}
          setOpalcounter={setOpalcounter}
          Tigercounter={Tigercounter}
          Tigercounterref={Tigercounterref}
          setTigercounter={setTigercounter}
        />
      </div>
    </div>
  );
  // }
}

export default Shop;
