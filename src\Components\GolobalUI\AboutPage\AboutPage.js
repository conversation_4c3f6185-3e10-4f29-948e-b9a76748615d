// src/App.js
import React, { Component } from "react";
import "./AboutPage.css";

class AboutPage extends Component {
  state = {
    colors: ["Black", "Blue", "Orange", "Silver", "Red"],
    currentColor: "",
    x: 0,
    y: 0,
    Member1DiscriptionDisplay: "none",
    Member2DiscriptionDisplay: "none",
    Member3DiscriptionDisplay: "none",
    Member4DiscriptionDisplay: "none",
    Member5DiscriptionDisplay: "none",
    Member6DiscriptionDisplay: "none",
    Member7DiscriptionDisplay: "none",
    Member8DiscriptionDisplay: "none",
    Member9DiscriptionDisplay: "none",
    Member10DiscriptionDisplay: "none",
    Member11DiscriptionDisplay: "none",
    Member12DiscriptionDisplay: "none",
    Member13DiscriptionDisplay: "none",
    Member14DiscriptionDisplay: "none",
    Member15DiscriptionDisplay: "none",
  };
  ShowHideMemberDiscription = (action, target) => {
    if (action == true) {
      if (target == "1") {
        this.setState({
          Member1DiscriptionDisplay: "block",
          Member2DiscriptionDisplay: "none",
          Member3DiscriptionDisplay: "none",
          Member4DiscriptionDisplay: "none",
          Member5DiscriptionDisplay: "none",
          Member6DiscriptionDisplay: "none",
          Member7DiscriptionDisplay: "none",
          Member8DiscriptionDisplay: "none",
          Member9DiscriptionDisplay: "none",
          Member10DiscriptionDisplay: "none",
          Member11DiscriptionDisplay: "none",
          Member12DiscriptionDisplay: "none",
          Member13DiscriptionDisplay: "none",
          Member14DiscriptionDisplay: "none",
          Member15DiscriptionDisplay: "none",
        });
      } else if (target == "2") {
        this.setState({
          Member2DiscriptionDisplay: "block",
          Member1DiscriptionDisplay: "none",
          Member3DiscriptionDisplay: "none",
          Member4DiscriptionDisplay: "none",
          Member5DiscriptionDisplay: "none",
          Member6DiscriptionDisplay: "none",
          Member7DiscriptionDisplay: "none",
          Member8DiscriptionDisplay: "none",
          Member9DiscriptionDisplay: "none",
          Member10DiscriptionDisplay: "none",
          Member11DiscriptionDisplay: "none",
          Member12DiscriptionDisplay: "none",
          Member13DiscriptionDisplay: "none",
          Member14DiscriptionDisplay: "none",
          Member15DiscriptionDisplay: "none",
        });
      } else if (target == "3") {
        this.setState({
          Member3DiscriptionDisplay: "block",
          Member1DiscriptionDisplay: "none",
          Member2DiscriptionDisplay: "none",
          Member4DiscriptionDisplay: "none",
          Member5DiscriptionDisplay: "none",
          Member6DiscriptionDisplay: "none",
          Member7DiscriptionDisplay: "none",
          Member8DiscriptionDisplay: "none",
          Member9DiscriptionDisplay: "none",
          Member10DiscriptionDisplay: "none",
          Member11DiscriptionDisplay: "none",
          Member12DiscriptionDisplay: "none",
          Member13DiscriptionDisplay: "none",
          Member14DiscriptionDisplay: "none",
          Member15DiscriptionDisplay: "none",
        });
      } else if (target == "4") {
        this.setState({
          Member4DiscriptionDisplay: "block",
          Member1DiscriptionDisplay: "none",
          Member2DiscriptionDisplay: "none",
          Member3DiscriptionDisplay: "none",
          Member5DiscriptionDisplay: "none",
          Member6DiscriptionDisplay: "none",
          Member7DiscriptionDisplay: "none",
          Member8DiscriptionDisplay: "none",
          Member9DiscriptionDisplay: "none",
          Member10DiscriptionDisplay: "none",
          Member11DiscriptionDisplay: "none",
          Member12DiscriptionDisplay: "none",
          Member13DiscriptionDisplay: "none",
          Member14DiscriptionDisplay: "none",
          Member15DiscriptionDisplay: "none",
        });
      } else if (target == "5") {
        this.setState({
          Member5DiscriptionDisplay: "block",
          Member1DiscriptionDisplay: "none",
          Member2DiscriptionDisplay: "none",
          Member3DiscriptionDisplay: "none",
          Member4DiscriptionDisplay: "none",
          Member6DiscriptionDisplay: "none",
          Member7DiscriptionDisplay: "none",
          Member8DiscriptionDisplay: "none",
          Member9DiscriptionDisplay: "none",
          Member10DiscriptionDisplay: "none",
          Member11DiscriptionDisplay: "none",
          Member12DiscriptionDisplay: "none",
          Member13DiscriptionDisplay: "none",
          Member14DiscriptionDisplay: "none",
          Member15DiscriptionDisplay: "none",
        });
      } else if (target == "6") {
        this.setState({
          Member6DiscriptionDisplay: "block",
          Member1DiscriptionDisplay: "none",
          Member2DiscriptionDisplay: "none",
          Member3DiscriptionDisplay: "none",
          Member4DiscriptionDisplay: "none",
          Member5DiscriptionDisplay: "none",
          Member7DiscriptionDisplay: "none",
          Member8DiscriptionDisplay: "none",
          Member9DiscriptionDisplay: "none",
          Member10DiscriptionDisplay: "none",
          Member11DiscriptionDisplay: "none",
          Member12DiscriptionDisplay: "none",
          Member13DiscriptionDisplay: "none",
          Member14DiscriptionDisplay: "none",
          Member15DiscriptionDisplay: "none",
        });
      } else if (target == "7") {
        this.setState({
          Member7DiscriptionDisplay: "block",
          Member1DiscriptionDisplay: "none",
          Member2DiscriptionDisplay: "none",
          Member3DiscriptionDisplay: "none",
          Member4DiscriptionDisplay: "none",
          Member5DiscriptionDisplay: "none",
          Member6DiscriptionDisplay: "none",
          Member8DiscriptionDisplay: "none",
          Member9DiscriptionDisplay: "none",
          Member10DiscriptionDisplay: "none",
          Member11DiscriptionDisplay: "none",
          Member12DiscriptionDisplay: "none",
          Member13DiscriptionDisplay: "none",
          Member14DiscriptionDisplay: "none",
          Member15DiscriptionDisplay: "none",
        });
      } else if (target == "8") {
        this.setState({
          Member8DiscriptionDisplay: "block",
          Member1DiscriptionDisplay: "none",
          Member2DiscriptionDisplay: "none",
          Member3DiscriptionDisplay: "none",
          Member4DiscriptionDisplay: "none",
          Member5DiscriptionDisplay: "none",
          Member6DiscriptionDisplay: "none",
          Member7DiscriptionDisplay: "none",
          Member9DiscriptionDisplay: "none",
          Member10DiscriptionDisplay: "none",
          Member11DiscriptionDisplay: "none",
          Member12DiscriptionDisplay: "none",
          Member13DiscriptionDisplay: "none",
          Member14DiscriptionDisplay: "none",
          Member15DiscriptionDisplay: "none",
        });
      } else if (target == "9") {
        this.setState({
          Member9DiscriptionDisplay: "block",
          Member1DiscriptionDisplay: "none",
          Member2DiscriptionDisplay: "none",
          Member3DiscriptionDisplay: "none",
          Member4DiscriptionDisplay: "none",
          Member5DiscriptionDisplay: "none",
          Member6DiscriptionDisplay: "none",
          Member7DiscriptionDisplay: "none",
          Member8DiscriptionDisplay: "none",
          Member10DiscriptionDisplay: "none",
          Member11DiscriptionDisplay: "none",
          Member12DiscriptionDisplay: "none",
          Member13DiscriptionDisplay: "none",
          Member14DiscriptionDisplay: "none",
          Member15DiscriptionDisplay: "none",
        });
      } else if (target == "10") {
        this.setState({
          Member10DiscriptionDisplay: "block",
          Member1DiscriptionDisplay: "none",
          Member2DiscriptionDisplay: "none",
          Member3DiscriptionDisplay: "none",
          Member4DiscriptionDisplay: "none",
          Member5DiscriptionDisplay: "none",
          Member6DiscriptionDisplay: "none",
          Member7DiscriptionDisplay: "none",
          Member8DiscriptionDisplay: "none",
          Member9DiscriptionDisplay: "none",
          Member11DiscriptionDisplay: "none",
          Member12DiscriptionDisplay: "none",
          Member13DiscriptionDisplay: "none",
          Member14DiscriptionDisplay: "none",
          Member15DiscriptionDisplay: "none",
        });
      } else if (target == "11") {
        this.setState({
          Member11DiscriptionDisplay: "block",
          Member1DiscriptionDisplay: "none",
          Member2DiscriptionDisplay: "none",
          Member3DiscriptionDisplay: "none",
          Member4DiscriptionDisplay: "none",
          Member5DiscriptionDisplay: "none",
          Member6DiscriptionDisplay: "none",
          Member7DiscriptionDisplay: "none",
          Member8DiscriptionDisplay: "none",
          Member9DiscriptionDisplay: "none",
          Member10DiscriptionDisplay: "none",
          Member12DiscriptionDisplay: "none",
          Member13DiscriptionDisplay: "none",
          Member14DiscriptionDisplay: "none",
          Member15DiscriptionDisplay: "none",
        });
      } else if (target == "12") {
        this.setState({
          Member12DiscriptionDisplay: "block",
          Member1DiscriptionDisplay: "none",
          Member2DiscriptionDisplay: "none",
          Member3DiscriptionDisplay: "none",
          Member4DiscriptionDisplay: "none",
          Member5DiscriptionDisplay: "none",
          Member6DiscriptionDisplay: "none",
          Member7DiscriptionDisplay: "none",
          Member8DiscriptionDisplay: "none",
          Member9DiscriptionDisplay: "none",
          Member10DiscriptionDisplay: "none",
          Member11DiscriptionDisplay: "none",
          Member13DiscriptionDisplay: "none",
          Member14DiscriptionDisplay: "none",
          Member15DiscriptionDisplay: "none",
        });
      } else if (target == "13") {
        this.setState({
          Member12DiscriptionDisplay: "none",
          Member1DiscriptionDisplay: "none",
          Member2DiscriptionDisplay: "none",
          Member3DiscriptionDisplay: "none",
          Member4DiscriptionDisplay: "none",
          Member5DiscriptionDisplay: "none",
          Member6DiscriptionDisplay: "none",
          Member7DiscriptionDisplay: "none",
          Member8DiscriptionDisplay: "none",
          Member9DiscriptionDisplay: "none",
          Member10DiscriptionDisplay: "none",
          Member11DiscriptionDisplay: "none",
          Member13DiscriptionDisplay: "block",
          Member14DiscriptionDisplay: "none",
          Member15DiscriptionDisplay: "none",
        });
      }
      else if (target == "14") {
        this.setState({
          Member1DiscriptionDisplay: "none",
          Member2DiscriptionDisplay: "none",
          Member3DiscriptionDisplay: "none",
          Member4DiscriptionDisplay: "none",
          Member5DiscriptionDisplay: "none",
          Member6DiscriptionDisplay: "none",
          Member7DiscriptionDisplay: "none",
          Member8DiscriptionDisplay: "none",
          Member9DiscriptionDisplay: "none",
          Member10DiscriptionDisplay: "none",
          Member11DiscriptionDisplay: "none",
          Member12DiscriptionDisplay: "none",
          Member13DiscriptionDisplay: "none",
          Member14DiscriptionDisplay: "block",
          Member15DiscriptionDisplay: "none",
        });
      }
      else if (target == "15") {
        this.setState({
          Member1DiscriptionDisplay: "none",
          Member2DiscriptionDisplay: "none",
          Member3DiscriptionDisplay: "none",
          Member4DiscriptionDisplay: "none",
          Member5DiscriptionDisplay: "none",
          Member6DiscriptionDisplay: "none",
          Member7DiscriptionDisplay: "none",
          Member8DiscriptionDisplay: "none",
          Member9DiscriptionDisplay: "none",
          Member10DiscriptionDisplay: "none",
          Member11DiscriptionDisplay: "none",
          Member12DiscriptionDisplay: "none",
          Member13DiscriptionDisplay: "none",
          Member14DiscriptionDisplay: "none",
          Member15DiscriptionDisplay: "block",
        });
      }
    } else if (action == false) {
      if (target == "1") {
        this.setState({ Member1DiscriptionDisplay: "none" });
        console.log("hooooon", this.state.Member1DiscriptionDisplay);
      } else if (target == "2") {
        this.setState({ Member2DiscriptionDisplay: "none" });
      } else if (target == "3") {
        this.setState({ Member3DiscriptionDisplay: "none" });
      } else if (target == "4") {
        this.setState({ Member4DiscriptionDisplay: "none" });
      } else if (target == "5") {
        this.setState({ Member5DiscriptionDisplay: "none" });
      } else if (target == "6") {
        this.setState({ Member6DiscriptionDisplay: "none" });
      } else if (target == "7") {
        this.setState({ Member7DiscriptionDisplay: "none" });
      } else if (target == "8") {
        this.setState({ Member8DiscriptionDisplay: "none" });
      } else if (target == "9") {
        this.setState({ Member9DiscriptionDisplay: "none" });
      } else if (target == "10") {
        this.setState({ Member10DiscriptionDisplay: "none" });
      } else if (target == "11") {
        this.setState({ Member11DiscriptionDisplay: "none" });
      } else if (target == "12") {
        this.setState({ Member12DiscriptionDisplay: "none" });
      } else if (target == "13") {
        this.setState({ Member13DiscriptionDisplay: "none" });
      } else if (target == "14") {
        this.setState({ Member14DiscriptionDisplay: "none" });
      } else if (target == "15") {
        this.setState({ Member15DiscriptionDisplay: "none" });
      }
    }
  };

  constructor(props) {
    super(props);
  }
  _onMouseMove(e) {
    this.setState({
      x: e.screenX,
      y: e.screenY,
    });
    this.props.updateXY(e.screenX,e.screenY);
  }

  render() {
    return (
      <div className="AboutPage" onMouseMove={this._onMouseMove.bind(this)}>
        <div className="aboutcard">
          {/* <button
            className="closeyABOUT"
            onClick={() => this.props.BackBUttonPressed()}
          >
            <img
              src="backdetails.png"
              style={{
                paddingTop: "3px",
                paddingRight: "1px",
              }}
            />{" "}
          </button>{" "} */}
          <div className="aboutcardtitle"> INCORP </div>{" "}
          {/* <div >
                <h1>Mouse coordinates: { this.state.x } { this.state.y }</h1>
              </div>; */}{" "}
          <div className="aboutcarddescription">
            {/* INCORP is a platform for interdisciplinary collaboration, focusing
            creative energy into practices of health and healing.Within this
            permeable loop of friends and phlosophy.INCORP 's fragrance line
            embodies the very essence it was created to empower: An all-natural,
            generous commitment to un - compromised quality and the journey to
            one 's authentic self.{" "} */}
            {/* An interdisciplinary collaboration focused on bringing health and healing into all facets of our daily lives through art, expression, and celebration. 
            <br />
            <br />
            INCORP’s fragrance line: "I, You, and We", has been created to cleanse, support, and guide. 
            <br />
            <br />
            Smell is the only sense in direct communication with memory. A scent can stimulate pathways between our inner core and our outer experience of the world. Today, intimate connections can so easily be lost track of. Our fragrance line was created to bring us back into a present sense of awareness. 
            <br />
            <br />
            INCORP hopes to support those who engage all parts of our platform in deepening their experience of their body through smell, beauty, and celebration. 
            <br />
            <br />
            We are alive for such a short time - our goal is to have this time feel full, vital, and embodied. */}
            <i>Signifying  “In body” & we are all “Incorporated”</i><br></br><br></br> INCORP is an interdisciplinary collaboration focused on bringing health and healing into all facets of our daily lives through art, beauty, and celebration.<br></br><br></br>

            INCORP’s fragrance line: "I, You, and We", has been created to CLEANSE, SUPPORT, and GUIDE.<br></br><br></br>

            Smell is the only sense in direct communication with memory. A scent can stimulate pathways between our inner core and our outer experience of the world. Today, intimate connections can so easily be lost track of. Our fragrance line was created to bring us back into a present sense of awareness.<br></br><br></br>

            INCORP hopes to support those who engage all parts of our platform in deepening their experience of their body through smell, beauty, and celebration.<br></br><br></br>

            We are alive for such a short time - our goal is to have this time feel full, vital, and embodied.
          </div>{" "}
        </div>{" "}
        <div className="AboutPageImages">
          {" "}
          {/* <h4>About</h4> */}{" "}
          <div className="AboutPageContent">
            <div
              className="members first"
              style={{
                left: this.state.x * 0.03 + "px",
                top: this.state.y * 0.03 + "px",
              }}
            >
              <img src="Sissel Tolaas_BW-min.jpg" onClick={() => this.ShowHideMemberDiscription(true, "1")} />
              <h5 onClick={() => this.ShowHideMemberDiscription(true, "1")} > Sissel Tolaas </h5>{" "}
              <span style={{ display: this.state.Member1DiscriptionDisplay }} className="boxShadow">
                <div className="XcontainerA">
                  <button className="CloseContactA" onClick={() => this.ShowHideMemberDiscription(false, "1")}>
                    <img src="close.png" />
                  </button>
                </div>
                <div className="Nametitle"> SISSEL TOLAAS: The Nose </div>
                Sissel Tolaas has been working, researching and experimenting intensively with the topic of smell since 1990. She is a pioneer and
                unique in her approach to smells. She has developed a wide range of revolutionary projects worldwide with smells based upon her own
                knowledge - organic chemistry, linguistics, and the visual arts.Tolaas established the SMELL RE_searchLab Berlin in January 2004,
                supported by IFF Inc. Tolaas has special skills in smell recognition, analysis and reproduction. She has researched and experienced
                smells in many different ways and in multiple diverse contexts and for multiple purposes.{" "}
              </span>{" "}
            </div>{" "}
            <div
              className="members second"
              style={{
                left: this.state.x * 0.02 + "px",
                top: this.state.y * 0.02 + "px",
                zIndex: "1",
              }}
            >
              <img src="Samuel Reis-min.jpg" onClick={() => this.ShowHideMemberDiscription(true, "2")} />
              <h5 onClick={() => this.ShowHideMemberDiscription(true, "2")} > Samuel Reis </h5>{" "}
              <span id="topyres" style={{ display: this.state.Member2DiscriptionDisplay }} className="boxShadow">
                <div className="XcontainerA">
                  <button className="CloseContactA" onClick={() => this.ShowHideMemberDiscription(false, "2")}>
                    <img src="close.png" />
                  </button>
                </div>
                <div className="Nametitle"> SAMUEL REIS: The Bottle </div>
                Samuel Reis is a Portuguese product designer, born in Lagos.Graduated in Industrial Design in 2011, achieved his master degree in
                Product Design at ESAD (Caldas da Rainha) in 2014. Samuel entitles himself as a re - collector, and by observation he explored how to
                use nature as the root of the creative process, aiming to identify characteristic forms and elements that already exist in nature and
                unique properties of the matter. These can be used or integrated in the conception of objects, appealing to nature as a creative agent.{" "}
              </span>{" "}
            </div>{" "}
            <div
              className="members third"
              style={{
                left: this.state.x * 0.02 + "px",
                top: this.state.y * 0.02 + "px",
              }}
            >
              <img src="Kira Lillie_BW-min.jpg" onClick={() => this.ShowHideMemberDiscription(true, "3")} />
              <h5 onClick={() => this.ShowHideMemberDiscription(true, "3")}> Kira Lillie </h5>{" "}
              <span id="kkira" style={{ display: this.state.Member3DiscriptionDisplay }} className="boxShadow">
                <div className="XcontainerA">
                  <button className="CloseContactA" onClick={() => this.ShowHideMemberDiscription(false, "3")}>
                    <img src="close.png" />
                  </button>
                </div>
                <div className="Nametitle"> KIRA LILLIE: The Concept </div>
                Born from a lineage of healers, Kira Lillie’s passion for the earth is infused into all of her projects. She was raised in Santa Cruz
                by parents who practiced alternative medicine including acupuncture, acupressure, and biodynamic craniosacral. Then at 18 she moved to
                Florence, Italy, continuing to Milan, Paris, New York, Berlin and Los Angeles, where she worked in fashion photography, filmmaking,
                and performance art. In Paris 2011, together with her mother Vanessa, Kira launched VK Lillie, a jewelry line which modernizes medicine
                bags. She is currently working on an ongoing multi - sensory food and performance art project called “In Co Lab.” Building on her unique
                and varied background, She is the founder of INCORP, an initiative bridging the worlds of fashion, luxury, and art with alternative
                medicine, healing, and intention.{" "}
              </span>{" "}
            </div>{" "}
            <div
              className="members fourth"
              style={{
                left: this.state.x * 0.04 + "px",
                top: this.state.y * 0.04 + "px",
              }}
            >
              <img src="Emmanuel Crivelli-min.jpg" onClick={() => this.ShowHideMemberDiscription(true, "4")} />
              <h5 onClick={() => this.ShowHideMemberDiscription(true, "4")} > Emmanuel Crivelli </h5>{" "}
              <span style={{ display: this.state.Member4DiscriptionDisplay }} className="boxShadow">
                <div className="XcontainerA">
                  <button className="CloseContactA" onClick={() => this.ShowHideMemberDiscription(false, "4")}>
                    <img src="close.png" />
                  </button>
                </div>
                <div className="Nametitle"> EMMANUEL CRIVELLI: Body Map & Envelope </div>Emmanuel Crivelli is an art director and graphic designer. He founded Dual Room, a
                creative studio based in Switzerland.{" "}
              </span>{" "}
            </div>{" "}
            <div
              className="members fifth"
              style={{
                left: this.state.x * 0.02 + "px",
                top: this.state.y * 0.02 + "px",
              }}
            >
              <img src="Ugo Cacciatori-min.jpg" onClick={() => this.ShowHideMemberDiscription(true, "5")} />
              <h5 onClick={() => this.ShowHideMemberDiscription(true, "5")} > Ugo Cacciatori </h5>{" "}
              <span id="topy" style={{ display: this.state.Member5DiscriptionDisplay }} className="boxShadow">
                <div className="XcontainerA">
                  <button className="CloseContactA" onClick={() => this.ShowHideMemberDiscription(false, "5")}>
                    <img src="close.png" />
                  </button>
                </div>
                <div className="Nametitle"> UGO CACCIATORI: The Top </div>
                Born under the massive Alps of Carrara, Ugo Cacciatori belongs to a dynasty of marble quarry owners and artists. His early passion for
                materials and shapes together with his innate creative skills led him to study Architecture at the University of Florence. Ugo then
                moved to London where, after a first involvement in architectural firms, he decided to challenge himself in the fashion industry. At
                the end of the Nineties Ugo’s first jewelry collection was presented in Milan at the same time he started consultancy work for
                Valentino, Giambattista Valli, Romeo Gigli, Marni, Fendi and several others. The desire of a hidden place to create brought him to
                Lerici, a little fisherman village on the border between Tuscany and Liguria with a landscape that inspired the likes of Lord Byron
                and Percy Bysshe Shelley. This shifted Ugo’s energy, focusing on fewer projects and collaborations, such as the creative direction of
                Santa Croce, a brand of the Prada Group, and the development of a jewelry line for Diesel Black Gold, sharing time between Milan and
                New York for over a decade. He recently added Los Angeles as a creative base where to express his experience while enjoying the
                Californian lifestyle.{" "}
              </span>{" "}
            </div>{" "}
            <div
              className="members sixth"
              style={{
                left: this.state.x * 0.04 + "px",
                top: this.state.y * 0.04 + "px",
              }}
            >
              <img src="Marco Panconesi-min.jpg" onClick={() => this.ShowHideMemberDiscription(true, "6")} />
              <h5 onClick={() => this.ShowHideMemberDiscription(true, "6")} > Marco Panconesi </h5>{" "}
              <span id="lefty" style={{ display: this.state.Member6DiscriptionDisplay }} className="boxShadow">
                <div className="XcontainerA">
                  <button className="CloseContactA" onClick={() => this.ShowHideMemberDiscription(false, "6")}>
                    <img src="close.png" />
                  </button>
                </div>
                <div className="Nametitle"> MARCO PANCONESI: The Tool </div>
                Marco Panconesi is a designer and artisan based in Paris. 
Born in Florence and with deep knowledge of traditional Italian jewellery-making, Panconesi approaches jewellery as spirited objects to be cherished as living artifacts. Each piece is designed to be an extension of the body - reconnecting people to the emotional qualities of what they wear is at the forefront of Panconesi’s philosophy. 
In additional to his namesake label, he has collaborated with celebrated design houses and brands including Balenciaga, Givenchy, Cartier, Mugler, Fendi, Swarovski among others.{" "}
              </span>{" "}
            </div>{" "}
            <div
              className="members thirteen"
              style={{
                left: this.state.x * 0.04 + "px",
                top: this.state.y * 0.04 + "px",
                zIndex: "1"
              }}
            >
              <img src="Pol Agusti-min.jpg" onClick={() => this.ShowHideMemberDiscription(true, "7")} />
              <h5 onClick={() => this.ShowHideMemberDiscription(true, "7")} > Pol Agusti </h5>{" "}
              <span id="middly" style={{ display: this.state.Member7DiscriptionDisplay }} className="boxShadow">
                <div className="XcontainerA">
                  <button className="CloseContactA" onClick={() => this.ShowHideMemberDiscription(false, "7")}>
                    <img src="close.png" />
                  </button>
                </div>
                <div className="Nametitle"> POL AGUSTI: Photography </div>From Barcelona. Lives in Mexico City. Photographer; loves bodies and nature. Capricorn
                chic. Gay and hot.{" "}
              </span>{" "}
            </div>{" "}
            <div
              className="members seventh"
              style={{
                left: this.state.x * 0.02 + "px",
                top: this.state.y * 0.02 + "px",
              }}
            >
              <img src="Vanessa Lillie_bwOK-min.jpg" onClick={() => this.ShowHideMemberDiscription(true, "8")} />
              <h5 onClick={() => this.ShowHideMemberDiscription(true, "8")} > Vanessa Lillie </h5>{" "}
              <span id="topyvan" style={{ display: this.state.Member8DiscriptionDisplay }} className="boxShadow">
                <div className="XcontainerA">
                  <button className="CloseContactA" onClick={() => this.ShowHideMemberDiscription(false, "8")}>
                    <img src="close.png" />
                  </button>
                </div>
                <div className="Nametitle"> VANESSA LILLIE: The Concept </div>
                Has dedicated her life to the study and practice of healing. She currently has a private practice in Boulder, Colorado, where she was
                born and raised. Over the past 30 years she’s studied the teachings of healing masters all over the world, including in the Yucatan
                and Guatemala. Today her multidisciplinary practice centers on biodynamic craniosacral, acupressure, massage, singing bowls, essential
                oils, and yoga. Together with her daughter Kira Lillie, she also creates medicine bags; leather pouches filled with stones and
                crystals, a collaboration they’ve named VK Lillie.{" "}
              </span>{" "}
            </div>{" "}
            <div
              className="members ninth"
              style={{
                left: +this.state.x * 0.05 + "px",
                top: +this.state.y * 0.05 + "px",
              }}
            >
              <img src="JR _ Lola_BW-min.jpg" onClick={() => this.ShowHideMemberDiscription(true, "9")} />
              <h5 onClick={() => this.ShowHideMemberDiscription(true, "9")} > Figure II </h5>{" "}
              <span id="custo" style={{ display: this.state.Member9DiscriptionDisplay }} className="boxShadow">
                <div className="XcontainerA">
                  <button className="CloseContactA" onClick={() => this.ShowHideMemberDiscription(false, "9")}>
                    <img src="close.png" />
                  </button>
                </div>
                <div className="Nametitle"> FIGURE II: Special Counsel</div>
                Figure II is the creative partnership of Lola Raban - Oliva and JR Etienne, whose multidisciplinary work spans film, art, and
                fashion. Based between Paris and New York, the duo have developed a unique mode of modern visual storytelling, using technology in
                inventive ways from designing avatars for runway shows to creating art galleries in virtual reality. As creative directors for all of
                KENZO’s communications, they’ve made waves in the fashion space showcasing new talents and partnering with iconic filmmakers such as
                Spike Jonze and Kahlil Joseph.{" "}
              </span>{" "}
            </div>{" "}
            <div
              className="members tinth"
              style={{
                left: +this.state.x * 0.03 + "px",
                top: +this.state.y * 0.03 + "px",
              }}
            >
              <img src="Pandora Graessl_Bw-min.jpg" onClick={() => this.ShowHideMemberDiscription(true, "10")} />
              <h5 onClick={() => this.ShowHideMemberDiscription(true, "10")} > Pandora Graessl </h5>{" "}
              <span id="topyy1" style={{ display: this.state.Member10DiscriptionDisplay }} className="boxShadow">
                <div className="XcontainerA">
                  <button className="CloseContactA" onClick={() => this.ShowHideMemberDiscription(false, "10")}>
                    <img src="close.png" />
                  </button>
                </div>
                <div className="Nametitle"> PANDORA GRAESSL: Photography </div>
                Pandora Graessl (FR, CH) is a multidisciplinary artist and producer based in Mexico City. 
Graessl founded her own creative Studio in 2015, with which she built a versatile portfolio ranging from production to consulting, set design, curation, happenings and installations and collaborated with brands such as Dior, Helmut Lang, Hood by Air, Saint-Laurent or Camper before evolving into developing her personal work.
Spawned by the desire to understand our journey on Earth, her practice unfolds in the relationship between human mind-bodies and the Natural dimension.
A nomadic life and an insatiable curiosity for the planet has fostered her global creative vision that draws from a plurality of references, a deep interest in ancestral religions and the powerful realm of dreams & spirits.{" "}
              </span>{" "}
            </div>{" "}
            <div
              className="members elevnth"
              style={{
                left: +this.state.x * 0.02 + "px",
                top: +this.state.y * 0.02 + "px",
              }}
            >
              <img src="ModularCX-min.jpg" onClick={() => this.ShowHideMemberDiscription(true, "11")} />
              <h5 onClick={() => this.ShowHideMemberDiscription(true, "11")} > ModularCX </h5>{" "}
              <span id="modycx" style={{ display: this.state.Member11DiscriptionDisplay }} className="boxShadow">
                <div className="XcontainerA">
                  <button className="CloseContactA" onClick={() => this.ShowHideMemberDiscription(false, "11")}>
                    <img src="close.png" />
                  </button>
                </div>
                <div className="Nametitle"> ModularCX: The Website </div>
                Our Goal is Simple, we're on a mission to transform your digital experience. Our technology-led solutions power exciting interactive
                3D shopping experiences, connecting consumers to the brands they love. We elevate your static 2D online experience to rich interactive
                3D. <br /> <br />
                <b>
                  <a href="https://www.modularcx.co.uk/" target="_blank" style={{textDecoration: "underline"}}>
                    Visit ModularCX{" "}
                  </a>{" "}
                </b>{" "}
              </span>{" "}
            </div>{" "}
            <div
              className="members twelve"
              style={{
                left: this.state.x * 0.04 + "px",
                top: this.state.y * 0.04 + "px",
                zIndex: "2",
              }}
            >
              <img src="incorpTeamPhoto-AurelienMabilat2-min.jpg" onClick={() => this.ShowHideMemberDiscription(true, "12")} />
              <h5 onClick={() => this.ShowHideMemberDiscription(true, "12")} > Aurélien Mabilat </h5>{" "}
              <span id="modycxau" style={{ display: this.state.Member12DiscriptionDisplay, width: "130%" }} className="boxShadow">
                <div className="XcontainerA">
                  <button className="CloseContactA" onClick={() => this.ShowHideMemberDiscription(false, "12")}>
                    <img src="close.png" />
                  </button>
                </div>
                <div className="Nametitle"> AURÉLIEN MABILAT: UI/UX </div>
                Aurélien Mabilat is an Interaction designer, Art Director and artist based between Paris and Geneva. He loves working in the fields of
                science, health, art & culture, education, and fun. As a teacher, transmission and exchange are at the core of his design practice.
                His art practice is a central part of his spiritual journey. He explores drawing as a poetic mean to question form and non-form and to
                transcribe emotions and physical sensations manifesting in everyday life. <br /> <br />
                <b>
                  <a href="https://aurelienmabilat.com/" target="_blank" style={{textDecoration: "underline"}}>
                    Visit Aurélien Mabilat{" "}
                  </a>{" "}
                </b>{" "}
              </span>{" "}
            </div>
            <div
              className="members eighth"
              style={{
                left: this.state.x * 0.02 + "px",
                top: this.state.y * 0.02 + "px",
              }}
            >
              <img src="unnamed-min.jpg" onClick={() => this.ShowHideMemberDiscription(true, "13")} />
              <h5 onClick={() => this.ShowHideMemberDiscription(true, "13")} > Kai Lillie </h5>
              <span id="modycxkai" style={{ display: this.state.Member13DiscriptionDisplay }} className="boxShadow">
                <div className="XcontainerA">
                  <button className="CloseContactA" onClick={() => this.ShowHideMemberDiscription(false, "13")}>
                    <img src="close.png" />
                  </button>
                </div>
                <div className="Nametitle">KAI LILLIE: Sound & Consulting</div>
                Kai Lillie, MA, MFT: Kai is a writer and depth psychotherapist focussed on supporting people exploring those aspects of themselves
                that might otherwise stay hidden. Kai engages people in unpacking the potent stories we weave of our pasts so that we can create the
                stories we want to live out in our futures.
              </span>{" "}
            </div>{" "}
            <div
              className="members fourteen"
              style={{
                left: this.state.x * 0.04 + "px",
                top: this.state.y * 0.04 + "px",
                zIndex: "1",
              }}
            >
              <img src="Ohlman-min.jpg" onClick={() => this.ShowHideMemberDiscription(true, "14")} />
              <h5 onClick={() => this.ShowHideMemberDiscription(true, "14")} > Ohlman Consorti</h5>
              <span id="ohlmanspan" style={{ display: this.state.Member14DiscriptionDisplay }} className="boxShadow">
                <div className="XcontainerA">
                  <button className="CloseContactA" onClick={() => this.ShowHideMemberDiscription(false, "14")}>
                    <img src="close.png" />
                  </button>
                </div>
                <div className="Nametitle">OHLMAN CONSORTI:  &nbsp;&nbsp;&nbsp;&nbsp; Brand Identity</div>
                Ohlman Consorti is a Paris-based consultancy that specializes in advertising and digital media for brands and institutions with clients including
                        Vogue Paris, Hermès, A.P.C., Repossi and The Row.
              </span>{" "}
            </div>{" "}
            <div
              className="members fifteen"
              style={{
                left: this.state.x * 0.02 + "px",
                top: this.state.y * 0.02 + "px",
                zIndex: "1",
              }}
            >
              <img src="Torso Solutions-min.png" onClick={() => this.ShowHideMemberDiscription(true, "15")} />
              <h5 onClick={() => this.ShowHideMemberDiscription(true, "15")} > Torso Solutions</h5>
              <span id="torsospan" style={{ display: this.state.Member15DiscriptionDisplay }} className="boxShadow">
                <div className="XcontainerA">
                  <button className="CloseContactA" onClick={() => this.ShowHideMemberDiscription(false, "15")}>
                    <img src="close.png" />
                  </button>
                </div>
                <div className="Nametitle">TORSO SOLUTIONS: Image  &nbsp; &nbsp; &nbsp; &nbsp;& Photography</div>
                David Toro and Solomon Chase are a New York-based duo working within photography and moving-image under the moniker TORSO. They also
                        have a contemporary art practice exhibiting artwork and curating museum exhibitions as two of the four members of the collective DIS. Most
                        recently they’ve launched dis.art, a streaming video platform for radical education, and they will curate the upcoming Geneva Biennale of Moving
                        Images.
              </span>{" "}
            </div>{" "}
          </div>{" "}
          {/* <button className="TVButton">WATCH INCORP TV.</button> */}{" "}
        </div>{" "}
      </div>
    );
  }
}

export default AboutPage;
