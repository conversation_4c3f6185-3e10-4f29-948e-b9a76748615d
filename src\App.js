// src/App.js
import React, { useState, useEffect } from "react";
import "./App.css";
import Scene3d from "./Components/Scene3d";
import Scene3dMobile from "./Components/Scene3dMobile";
import ControlPanel from "./Components/ControlPanel";
import Customization from "./Components/Customization";
import ProductDetail from "./Components/ProductDetail";
import Pictogram from "./Components/GolobalUI/pictogram";
import GlobalUI from "./Components/GolobalUI/GlobalUI";

import { useQuery, useMutation, useLazyQuery } from "@apollo/client";
import Product from "./Components/Product";
import Cart from "./Components/Cart";
import CustomerAuthWithMutation from "./Components/CustomerAuth";
import gql from "graphql-tag";
import {
  useCartEffect,
  createCart,
  cartLinesAdd,
  cartLinesUpdate,
  cartLinesRemove,
  cartBuyerIdentityUpdate,
  GET_CART,
} from "./checkout";

const query = gql`
  query query {
    products(first: 10) {
      pageInfo {
        hasNextPage
        hasPreviousPage
      }
      edges {
        cursor
        node {
          id
          title
          totalInventory
          options {
            id
            name
            values
          }
          variants(first: 10) {
            pageInfo {
              hasNextPage
              hasPreviousPage
            }
            edges {
              node {
                id
                title
                quantityAvailable
                selectedOptions {
                  name
                  value
                }
                image {
                  src
                }
              }
            }
          }
          images(first: 1) {
            pageInfo {
              hasNextPage
              hasPreviousPage
            }
            edges {
              node {
                src
              }
            }
          }
        }
      }
    }
  }
`;

function App(props) {
  // state = {
  //   colors: ["Black", "Blue", "Orange", "Silver", "Red"],
  //   currentColor: "",
  //   coloursMenuColor: "",
  //   coloursMenuBorder: "",
  //   coloursDisplay: "",
  //   productDetail: "none",
  //   controlPanel: "none",
  //   FingerDisplay: "none",
  //   StartDisplay: "none",
  //   ProductDetailsDisplay: "none",
  //   SceneStatus: "OUTSIDE",
  //   loading:true,
  //   P1Big: 0,
  //   P1Small: 0,
  //   P2Big: 0,
  //   P2Small: 0,
  //   P3Big: 0,
  //   P3Small: 0,
  // Products: [
  //   { name: "P1Big", counter: 0 },
  //   { name: "P1Small", counter: 0 },
  //   { name: "P2Big", counter: 0 },
  //   { name: "P2Small", counter: 0 },
  //   { name: "P3Big", counter: 0 },
  //   { name: "P3Small", counter: 0 },
  // ],
  // };
  /**************************************Body Map Hooks! ***********/
  const [replay, setreplay] = useState(false);
  const [replayTruth, setreplayTruth] = useState(false);
  const [replayHeart, setreplayHeart] = useState(false);
  const [replayCenter, setreplayCenter] = useState(false);
  const [replayRelief, setreplayRelief] = useState(false);
  const [replayEnergize, setreplayEnergize] = useState(false);
  const [replayGround, setreplayGround] = useState(false);
  const [ProductAddedtitle, setProductAddedtitle] = useState("");

  const [BodyMapAnimation1, setBodyMapAnimation1] = useState("");
  const [BodyMapAnimation2, setBodyMapAnimation2] = useState("");
  const [BodyMapAnimation3, setBodyMapAnimation3] = useState("");
  const [BodyMapAnimation4, setBodyMapAnimation4] = useState("");
  const [BodyMapAnimation5, setBodyMapAnimation5] = useState("");
  const [BodyMapAnimation6, setBodyMapAnimation6] = useState("");
  const [BodyMapAnimation7, setBodyMapAnimation7] = useState("");

  const [MindDisplay, setMindDisplay] = useState("none");
  const [TruthDisplay, setTruthDisplay] = useState("none");
  const [HeartDisplay, setHeartDisplay] = useState("none");
  const [CenterDisplay, setCenterDisplay] = useState("none");
  const [ReliefDisplay, setReliefDisplay] = useState("none");
  const [EnergizeDisplay, setEnergizeDisplay] = useState("none");
  const [GroundDisplay, setGroundDisplay] = useState("none");
  const [NotificationDisplay, setNotificationDisplay] = useState("none");
  /**************************************   END Body Map Hooks! ***********/
  const [colors, setcolors] = useState([
    "Black",
    "Blue",
    "Orange",
    "Silver",
    "Red",
  ]);
  const [currentColor, setcurrentColor] = useState("");
  const [coloursMenuColor, setcoloursMenuColor] = useState("");
  const [coloursDisplay, setcoloursDisplay] = useState(false);
  const [productDetail, setproductDetail] = useState("none");
  const [controlPanel, setcontrolPanel] = useState("none");
  const [StartDisplay, setStartDisplay] = useState("none");
  const [FingerDisplay, setFingerDisplay] = useState("none");
  const [ProductDetailsDisplay, setProductDetailsDisplay] = useState("none");
  /***************small cards on bottles display******************* */
  const [ProductDetailsBottleDisplay, setProductDetailsBottleDisplay] =
    useState("none");
  const [ProductDetailsBottlecupDisplay, setProductDetailsBottlecupDisplay] =
    useState("none");
  const [ProductDetailsBottleToolDisplay, setProductDetailsBottleToolDisplay] =
    useState("none");
  const [
    ProductDetailsBottleSmallDisplay,
    setProductDetailsBottleSmallDisplay,
  ] = useState("none");
  const [
    ProductDetailsBottleSmallBodyDisplay,
    setProductDetailsBottleSmallBodyDisplay,
  ] = useState("none");
  /***************END OF Small cards on bottles display******************* */
  const [SceneStatus, setSceneStatus] = useState("OUTSIDE");
  const [loading, setloading] = useState("");
  const [P1Big, setP1Big] = useState(0);
  const [P1Small, setP1Small] = useState(0);
  const [P2Big, setP2Big] = useState(0);
  const [P2Small, setP2Small] = useState(0);
  const [P3Big, setP3Big] = useState(0);
  const [P3Small, setP3Small] = useState(0);
  const [Products, setProducts] = useState([
    { name: "P1Big", counter: 0 },
    { name: "P1Small", counter: 0 },
    { name: "P2Big", counter: 0 },
    { name: "P2Small", counter: 0 },
    { name: "P3Big", counter: 0 },
    { name: "P3Small", counter: 0 },
  ]);

  const [windowX, setWindowX] = useState(0);
  const [windowY, setWindowY] = useState(0);

  const [RotateDeviceDisplay, setRotateDeviceDisplay] = useState("none");
  const [ReloadText, setReloadText] = useState("Please rotate your device");

  const [menuButtonsDisplay, setmenuButtonsDisplay] = useState("none");

  const [totalitems, settotalitems] = useState(0);

  const [idofmodelbig, setidofmodelbig] = useState("");
  const [idofmodelsmall, setidofmodelsmall] = useState("");

  const [pictogramDisplay, setpictogramDisplay] = useState("none");
  const [currentModel, setcurrentModel] = useState("body1");
  const [bodyUnderline, setbodyUnderline] = useState("1px solid");
  const [bodySmallUnderline, setbodySmallUnderline] = useState("none");
  const [toolObsidian, settoolObsidian] = useState("none");
  const [toolOpal, settoolOpal] = useState("none");
  const [toolTiger, settoolTiger] = useState("none");

  const [InfoImageSRC, setInfoImageSRC] = useState("BTN - hide.png");

  const [isCartOpen, setCartOpen] = useState(false);
  const [isNewCustomer, setNewCustomer] = useState(false);
  const [isCustomerAuthOpen, setCustomerAuthOpen] = useState(false);
  const [showAccountVerificationMessage, setAccountVerificationMessage] =
    useState(false);
  const [checkout, setCheckout] = useState({
    lines: { edges: [] },
    cost: {
      totalAmount: { amount: 0 },
      subtotalAmount: { amount: 0 },
    },
    checkoutUrl: "",
  });
  const [animationEnd, setAnimationEnd] = useState(false);
  const [cameraAnimationEnd, setcameraAnimationEnd] = useState(false);
  const [cameraAnimationEndTimeout, setcameraAnimationEndTimeout] =
    useState(4000);
  const [ArrowsDisplay, setArrowsDisplay] = useState("none");
  /********PRODUCT INFORMATION******** **/
  const [PDITitle, setPDITitle] = useState("I");
  const [handImage33ml, sethandImage33ml] = useState("I_ 33ml_s.png");
  const [handImage5ml, sethandImage5ml] = useState("I_ 5ml_s.png");
  const [handImagetool, sethandImagetool] = useState("obsidian_tool_s.png");
  const [QuantityLimitedStatment, setQuantityLimitedStatment] = useState(
    "Limited edition of 33ml / 200 - 5ml / 1000",
  );
  const [QuantityLimitedMob1Statment, setQuantityLimitedMob1Statment] =
    useState("Limited edition - 33ml / 200 each");
  const [QuantityLimitedMob2Statment, setQuantityLimitedMob2Statment] =
    useState("5ml / 1000 each");
  const [PDSound, setPDSound] = useState(
    "I (vol adj & cut)- 9722, 5.48 PM.mp3",
  );
  const [PDICardLine, setPDICardLine] = useState(
    "I am focused. I am energized",
  );
  const [PDIDescription, setPDIDescription] = useState(
    "With CLEANSING Thyme I eliminate distraction while SUPPORTING with grounding Vetiver and healing Myrrh. Antioxidant Melissa vitalizes, GUIDING me towards my authentic inner self.",
  );
  /******** END OF PRODUCT INFORMATION******** **/
  //deployment commit
  const [customerAccessToken, setCustomerAccessToken] = useState(null);
  const [
    createCartMutation,
    {
      data: createCartData,
      loading: createCartLoading,
      error: createCartError,
    },
  ] = useMutation(createCart);

  const [
    cartLinesAddMutation,
    {
      data: cartLinesAddData,
      loading: cartLinesAddLoading,
      error: cartLinesAddError,
    },
  ] = useMutation(cartLinesAdd);

  const [
    cartLinesUpdateMutation,
    {
      data: cartLinesUpdateData,
      loading: cartLinesUpdateLoading,
      error: cartLinesUpdateError,
    },
  ] = useMutation(cartLinesUpdate);

  const [
    cartLinesRemoveMutation,
    {
      data: cartLinesRemoveData,
      loading: cartLinesRemoveLoading,
      error: cartLinesRemoveError,
    },
  ] = useMutation(cartLinesRemove);

  const [
    cartBuyerIdentityUpdateMutation,
    {
      data: cartBuyerIdentityUpdateData,
      loading: cartBuyerIdentityUpdateLoading,
      error: cartBuyerIdentityUpdateError,
    },
  ] = useMutation(cartBuyerIdentityUpdate);

  const {
    loading: shopLoading,
    error: shopError,
    data: shopData,
  } = useQuery(query);

  const [getCart, { data: cartQueryData }] = useLazyQuery(GET_CART);
  // console.log(shopData);
  useEffect(() => {
    const variables = { input: {} };
    createCartMutation({ variables }).then(
      (res) => {
        console.log("Cart created successfully:", res);
        if (res.data && res.data.cartCreate && res.data.cartCreate.cart) {
          console.log("Cart ID:", res.data.cartCreate.cart.id);
        }
      },
      (err) => {
        console.log("create cart error", err);
      },
    );
  }, [createCartMutation]);
  if (shopError) {
    console.log(shopError);
  }
  useCartEffect(createCartData, "cartCreate", setCheckout);
  useCartEffect(cartLinesAddData, "cartLinesAdd", setCheckout);
  useCartEffect(cartLinesUpdateData, "cartLinesUpdate", setCheckout);
  useCartEffect(cartLinesRemoveData, "cartLinesRemove", setCheckout);
  useCartEffect(
    cartBuyerIdentityUpdateData,
    "cartBuyerIdentityUpdate",
    setCheckout,
  );

  // Handle cart query response to update state when Apollo warning occurs
  useEffect(() => {
    if (cartQueryData && cartQueryData.cart) {
      setCheckout(cartQueryData.cart);
    }
  }, [cartQueryData]);

  const handleCartClose = () => {
    setCartOpen(false);
  };

  const switchMenuButtonsDisplay = () => {
    setmenuButtonsDisplay("");
  };

  const updateCurrentModel = (model) => {
    setcurrentModel(model);
    if (model === "body1" || model === "body2" || model === "body3") {
      setbodyUnderline("1px solid");
      setbodySmallUnderline("none");
      settoolObsidian("none");
      settoolOpal("none");
      settoolTiger("none");
    } else if (model === "bodySmall") {
      setbodyUnderline("none");
      setbodySmallUnderline("1px solid");
      settoolObsidian("none");
      settoolOpal("none");
      settoolTiger("none");
    } else if (model === "toolObsidian") {
      setbodyUnderline("none");
      setbodySmallUnderline("none");
      settoolObsidian("1px solid");
      settoolOpal("none");
      settoolTiger("none");
    } else if (model === "toolOpal") {
      setbodyUnderline("none");
      setbodySmallUnderline("none");
      settoolObsidian("none");
      settoolOpal("1px solid");
      settoolTiger("none");
    } else if (model === "toolTiger") {
      setbodyUnderline("none");
      setbodySmallUnderline("none");
      settoolObsidian("none");
      settoolOpal("none");
      settoolTiger("1px solid");
    }
  };

  const BodyMapDisplaySwitcher = (target) => {
    if (target == "Mind" && replay == false) {
      setreplay(true);
      setMindDisplay("");
      setBodyMapAnimation1("BodyMap MindDIVMask");
      setTimeout(() => {
        // setBodyMapAnimation1("scale-down-ver-center 0.5s cubic-bezier(0, 0.460, 0.450, 1) both ");
        setBodyMapAnimation1("BodyMap MindDIVMaskReverse");
      }, 8000);
      setTimeout(() => {
        setMindDisplay("none");
        setreplay(false);
      }, 8500);
      // setBodyMapAnimation1("scale-up-ver-center 0.5s cubic-bezier(0, 0.575, 0.565, 1) both");
    } else if (target == "Truth" && replayTruth == false) {
      setreplayTruth(true);
      setTruthDisplay("");
      setTimeout(() => {
        // setBodyMapAnimation2("scale-down-ver-center 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both");
        setBodyMapAnimation2("BodyMap TruthDIVMaskReverse");
      }, 6000);
      setTimeout(() => {
        setTruthDisplay("none");
        setreplayTruth(false);
      }, 6500);
      // setBodyMapAnimation2("scale-up-ver-center 0.5s cubic-bezier(0.390, 0.575, 0.565, 1.000) both");
      setBodyMapAnimation2("BodyMap TruthDIVMask");
    } else if (target == "Heart" && replayHeart == false) {
      setreplayHeart(true);
      setHeartDisplay("");
      setTimeout(() => {
        // setBodyMapAnimation3("scale-down-ver-center 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both");
        setBodyMapAnimation3("BodyMap HeartDIVMaskReverse");
      }, 5000);
      setTimeout(() => {
        setHeartDisplay("none");
        setreplayHeart(false);
      }, 5500);
      // setBodyMapAnimation3("scale-up-ver-center 0.5s cubic-bezier(0.390, 0.575, 0.565, 1.000) both");
      setBodyMapAnimation3("BodyMap HeartDIVMask");
    } else if (target == "Center" && replayCenter == false) {
      setreplayCenter(true);
      setCenterDisplay("");
      setTimeout(() => {
        // setBodyMapAnimation4("scale-down-ver-center 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both");
        setBodyMapAnimation4("BodyMap CenterDIVMaskReverse");
      }, 5000);
      setTimeout(() => {
        setCenterDisplay("none");
        setreplayCenter(false);
      }, 5500);
      // setBodyMapAnimation4("scale-up-ver-center 0.5s cubic-bezier(0.390, 0.575, 0.565, 1.000) both");
      setBodyMapAnimation4("BodyMap CenterDIVMask");
    } else if (target == "Relief" && replayRelief == false) {
      setreplayRelief(true);
      setReliefDisplay("");
      setTimeout(() => {
        // setBodyMapAnimation5("scale-down-ver-center 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both");
        setBodyMapAnimation5("BodyMap ReliefDIVMaskReverse");
      }, 4500);
      setTimeout(() => {
        setReliefDisplay("none");
        setreplayRelief(false);
      }, 5000);
      // setBodyMapAnimation5("scale-up-ver-center 0.5s cubic-bezier(0.390, 0.575, 0.565, 1.000) both");
      setBodyMapAnimation5("BodyMap ReliefDIVMask");
    } else if (target == "Energize" && replayEnergize == false) {
      setreplayEnergize(true);
      setEnergizeDisplay("");
      setTimeout(() => {
        // setBodyMapAnimation6("scale-down-ver-center 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both");
        setBodyMapAnimation6("BodyMap EnergizeDIVMaskReverse");
      }, 6000);
      setTimeout(() => {
        setEnergizeDisplay("none");
        setreplayEnergize(false);
      }, 6500);
      // setBodyMapAnimation6("scale-up-ver-center 0.5s cubic-bezier(0.390, 0.575, 0.565, 1.000) both");
      setBodyMapAnimation6("BodyMap EnergizeDIVMask");
    } else if (target == "Ground" && replayGround == false) {
      setreplayGround(true);
      setGroundDisplay("");
      setTimeout(() => {
        // setBodyMapAnimation7("scale-down-ver-center 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both");
        setBodyMapAnimation7("BodyMap GroundDIVMaskReverse");
      }, 6500);
      setTimeout(() => {
        setGroundDisplay("none");
        setreplayGround(false);
      }, 7000);
      // setBodyMapAnimation7("scale-up-ver-center 0.5s cubic-bezier(0.390, 0.575, 0.565, 1.000) both");
      setBodyMapAnimation7("BodyMap GroundDIVMask");
    }
  };
  const openCustomerAuth = (event) => {
    if (event.target.getAttribute("data-customer-type") === "new-customer") {
      setNewCustomer(true);
      setCustomerAuthOpen(true);
    } else {
      setNewCustomer(false);
      setCustomerAuthOpen(true);
    }
  };

  const accountVerificationMessage = () => {
    setAccountVerificationMessage(true);
    setTimeout(() => {
      setAccountVerificationMessage(false);
    }, 5000);
  };

  const closeCustomerAuth = () => {
    setCustomerAuthOpen(false);
  };

  const addVariantToCart = (variantId, quantity) => {
    console.log("addVariantToCart called with:", {
      variantId,
      quantity,
      checkoutId: checkout.id,
    });

    settotalitems(totalitems + quantity);
    setNotificationDisplay("");

    // Ensure we have a valid cart ID
    if (!checkout.id) {
      console.error("No cart ID available. Cart may not be initialized yet.");
      return;
    }

    const variables = {
      cartId: checkout.id,
      lines: [
        {
          merchandiseId: variantId,
          quantity: parseInt(quantity, 10),
        },
      ],
    };

    console.log("Cart mutation variables:", variables);

    cartLinesAddMutation({ variables })
      .then((res) => {
        console.log("Cart lines add response:", res);
        if (res.data && res.data.cartLinesAdd && res.data.cartLinesAdd.cart) {
          const cart = res.data.cartLinesAdd.cart;
          if (cart.lines && cart.lines.edges && cart.lines.edges.length > 0) {
            const addedLine = cart.lines.edges.find(
              (edge) => edge.node.merchandise.id === variantId,
            );
            if (addedLine && addedLine.node.merchandise.product) {
              setProductAddedtitle(addedLine.node.merchandise.product.title);
            }
          }
          setCartOpen(true);
        }
      })
      .catch((err) => {
        // Check if this is just an Apollo Client cache warning, not a real error
        const isApolloWarning =
          err.message &&
          err.message.includes("Cannot convert object to primitive value");
        const hasGraphQLErrors =
          err.graphQLErrors && err.graphQLErrors.length > 0;

        if (isApolloWarning && !hasGraphQLErrors) {
          // This is just a cache warning, the mutation likely succeeded
          console.warn(
            "Apollo Client cache warning (non-critical):",
            err.message,
          );

          // The mutation succeeded despite the warning, so get product title from variant ID
          const getProductTitleFromVariantId = (variantId) => {
            // Map of known variant IDs to product titles based on your app
            const variantToProductMap = {
              "************************************************************":
                "I - 33ml",
              "************************************************************":
                "I - 5ml",
              "************************************************************":
                "YOU - 33ml",
              "************************************************************":
                "YOU - 5ml",
              "************************************************************":
                "WE - 33ml",
              "************************************************************":
                "WE - 5ml",
              "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE2ODAxNjEzMA==":
                "Obsidian Tool",
              "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE5Mjc1NTk3MA==":
                "Opal Tool",
            };
            return variantToProductMap[variantId] || "Product";
          };

          setProductAddedtitle(getProductTitleFromVariantId(variantId));

          // Since the Apollo warning prevented the cart state from updating,
          // manually refresh the cart state using a query
          setTimeout(() => {
            // Create a minimal cart query to refresh the cart state
            const refreshVariables = { cartId: checkout.id, lines: [] };
            cartLinesAddMutation({ variables: refreshVariables })
              .then((refreshRes) => {
                if (
                  refreshRes.data &&
                  refreshRes.data.cartLinesAdd &&
                  refreshRes.data.cartLinesAdd.cart
                ) {
                  // This will trigger useCartEffect to update the cart state
                  console.log("Cart state refreshed after Apollo warning");
                }
              })
              .catch(() => {
                // If refresh fails, that's okay - the cart will update on next successful operation
                console.log(
                  "Cart refresh failed, but cart will update on next operation",
                );
              });
          }, 100);

          setCartOpen(true);
        } else {
          // This is a real error
          console.error("Real error adding to cart:", err);
          console.error("Error details:", err.graphQLErrors);
        }
      });

    setTimeout(() => {
      setNotificationDisplay("none");
    }, 3000);
  };

  const updateLineItemInCart = (lineItemId, quantity) => {
    settotalitems(totalitems - 1);

    console.log(lineItemId);
    const variables = {
      cartId: checkout.id,
      lines: [{ id: lineItemId, quantity: quantity.current }],
    };
    cartLinesUpdateMutation({ variables })
      .then((res) => {
        console.log(res);
      })
      .catch((e) => {
        console.log(e);
      });
  };

  const updateLineItemInCartAddMobile = (lineItemId, quantity) => {
    settotalitems(totalitems + 1);

    const variables = {
      cartId: checkout.id,
      lines: [{ id: lineItemId, quantity: quantity }],
    };
    cartLinesUpdateMutation({ variables });
  };

  const updateLineItemInCartLessDesktop = (lineItemId, quantityNow) => {
    settotalitems(totalitems - quantityNow);

    const variables = {
      cartId: checkout.id,
      lines: [{ id: lineItemId, quantity: 0 }],
    };
    cartLinesUpdateMutation({ variables });
  };

  const updateLineItemInCartLessMobile = (
    lineItemId,
    quantity,
    quantityoflineitem,
  ) => {
    if (quantity == 0) {
      settotalitems(totalitems - quantityoflineitem);
    } else {
      settotalitems(totalitems - 1);
    }

    const variables = {
      cartId: checkout.id,
      lines: [{ id: lineItemId, quantity: quantity }],
    };
    cartLinesUpdateMutation({ variables });
  };

  const removeLineItemInCart = (lineItemId) => {
    const variables = { cartId: checkout.id, lineIds: [lineItemId] };
    cartLinesRemoveMutation({ variables });
  };

  const associateCustomerCheckout = (customerAccessToken) => {
    const variables = {
      cartId: checkout.id,
      buyerIdentity: { customerAccessToken: customerAccessToken },
    };
    cartBuyerIdentityUpdateMutation({ variables }).then((res) => {
      setCustomerAuthOpen(false);
    });
  };

  const emitSectionChangeEvent = (name) => {
    // Arguments: Event Name, Event Options
    let event = new CustomEvent("move-camera", {
      detail: name,
    });
    // Triggers the event on the window object
    window.dispatchEvent(event);
  };

  const moveCamera = (info) => {
    let event = new CustomEvent("moveCamera", {
      detail: {
        info,
      },
    });
    window.dispatchEvent(event);
  };

  const updateXY = (x, y) => {
    setWindowX(x);
    setWindowY(y);
  };

  const loadinglogostop = () => {
    setTimeout(() => {
      document.getElementsByClassName("incorplogoimage")[0].style.display =
        "none";
    }, 1000);

    setTimeout(() => {
      if (window.innerWidth > 601) {
        if (document.getElementsByClassName("INCORPbottom")[0]) {
          document.getElementsByClassName("INCORPbottom")[0].className =
            "INCORPbottomend";
        }
        // document.getElementsByClassName("INCORPbottomend")[0].style.cursor = "default";
        document.getElementsByClassName("INCORPbottomend")[0].style.cursor =
          "grab";
        // document.getElementsByClassName("INCORPbottom")[0].style.animationPlayState="paused"
        document.getElementsByClassName(
          "Controls Right shop",
        )[0].style.display = "";
        document.getElementsByClassName(
          "Controls Right bottom",
        )[0].style.display = "";
        document.getElementsByClassName(
          "Controls Left bottom",
        )[0].style.display = "";
        document.getElementsByClassName("Controls left")[0].style.display = "";
      } else {
        document.getElementsByClassName("INCORPbottom")[0].className =
          "INCORPbottomend";
        // document.getElementsByClassName("INCORPbottomend")[0].style.cursor = "default";
        setTimeout(() => {
          document.getElementsByClassName("INCORPbottomend")[0].style.display =
            "none";
        }, 5000);
        // document.getElementsByClassName("INCORPbottom")[0].style.animationPlayState="paused"

        // document.getElementsByClassName("Controls Right shop")[0].style.display = "";
        // document.getElementsByClassName("Controls Right bottom")[0].style.display = "";
        // document.getElementsByClassName("Controls Left bottom")[0].style.display = "";
        // document.getElementsByClassName("Controls left")[0].style.display = "";
      }

      // setStartDisplay("");
    }, 200);
  };

  const StartAnimation = () => {
    if (SceneStatus == "OUTSIDE") {
      let event = new CustomEvent("Startanimation", {});
      window.dispatchEvent(event);
      setBodyMapAnimation1("BodyMap MindDIVMaskReverse");
      setBodyMapAnimation2("BodyMap TruthDIVMaskReverse");
      setBodyMapAnimation3("BodyMap HeartDIVMaskReverse");
      setBodyMapAnimation4("BodyMap CenterDIVMaskReverse");
      setBodyMapAnimation5("BodyMap ReliefDIVMaskReverse");
      setBodyMapAnimation6("BodyMap EnergizeDIVMaskReverse");
      setBodyMapAnimation7("BodyMap GroundDIVMaskReverse");
      setTimeout(() => {
        setMindDisplay("none");
        setTruthDisplay("none");
        setHeartDisplay("none");
        setCenterDisplay("none");
        setReliefDisplay("none");
        setEnergizeDisplay("none");
        setGroundDisplay("none");
      }, 500);
      setStartDisplay("none");
      setTimeout(() => {
        if (window.innerWidth > 601) {
          setSceneStatus("INSIDE");
          document.getElementsByClassName("INCORPbottomend")[0].style.cursor =
            "";
        } else {
          setTimeout(() => {
            setSceneStatus("INSIDE");
          }, 1000);
        }
      }, 6000);
    } else {
      return;
    }
  };

  const Heartback = () => {
    let event = new CustomEvent("Heartback", {});
    window.dispatchEvent(event);
  };

  const updateSceneStatus = () => {
    setSceneStatus("OUTSIDE");
  };

  const pictogram = (action) => {
    console.log(action);
    if (action === "show") {
      setpictogramDisplay("");
    } else if (action === "hide") {
      setpictogramDisplay("none");
    }
  };

  const annotationToggle = () => {
    let event = new CustomEvent("annotationToggle", {});
    window.dispatchEvent(event);
  };

  const ProductInfoSwitcher = () => {
    annotationToggle();
    if (
      InfoImageSRC == "BTN - show.png" ||
      InfoImageSRC == "BTN - hide Hover show.png"
    ) {
      setInfoImageSRC("BTN - show Hover hide.png");
    } else {
      setInfoImageSRC("BTN - hide Hover show.png");
      return;
    }
  };
  const ProductInfoSwitcherMobile = () => {
    annotationToggle();
    if (InfoImageSRC == "BTN - show.png") {
      setInfoImageSRC("BTN - hide.png");
    } else {
      setInfoImageSRC("BTN - show.png");
      return;
    }
  };

  const ProductInfoSwitcherOff = () => {
    setInfoImageSRC("BTN - hide.png");
  };
  const Changerimg = (target) => {
    if (target == "white") {
      if (
        InfoImageSRC == "BTN - show.png" ||
        InfoImageSRC == "BTN - hide Hover show.png"
      ) {
        setInfoImageSRC("BTN - show.png");
      } else {
        setInfoImageSRC("BTN - hide.png");
      }
    } else if (target == "black") {
      if (
        InfoImageSRC == "BTN - show.png" ||
        InfoImageSRC == "BTN - hide Hover show.png"
      ) {
        setInfoImageSRC("BTN - hide Hover show.png");
      } else {
        setInfoImageSRC("BTN - show Hover hide.png");
      }
    }
  };

  // Function for Changing product Details INFO :)
  const ProductDetailsChanger = (product) => {
    // console.log(product)
    if (product == "I") {
      setPDITitle("I");
      sethandImage33ml("I_ 33ml_s.png");
      sethandImage5ml("I_ 5ml_s.png");
      setPDICardLine("I am focused. I am energized.");
      setPDIDescription(
        "With CLEANSING Thyme I eliminate distraction while SUPPORTING with grounding Vetiver and healing Myrrh. Antioxidant Melissa vitalizes, GUIDING me towards my authentic inner self.",
      );
      setidofmodelbig(
        "************************************************************",
      );
      setidofmodelsmall(
        "************************************************************",
      );
      setPDSound("I (vol adj & cut)- 9722, 5.48 PM.mp3");
      setQuantityLimitedStatment("Limited edition of 33ml / 200 - 5ml / 1000");
      setQuantityLimitedMob1Statment("Limited edition - 33ml / 200 each");
      setQuantityLimitedMob2Statment("5ml / 1000 each");
    } else if (product == "YOU") {
      setPDITitle("YOU");
      sethandImage33ml("YOU_33ml_s.png");
      sethandImage5ml("YOU _ 5ml_s.png");
      setPDICardLine("YOU are unconditional love. YOU are light.");
      setPDIDescription(
        "With CLEANSING Lime and Coriander; you stimulate your body and mind. You SUPPORT emotional stability with Sandalwood, opening your heart with Rose and GUIDING you towards divine inner love.",
      );
      setidofmodelbig(
        "************************************************************",
      );
      setidofmodelsmall(
        "************************************************************",
      );
      setPDSound("You2 (vol adj & cut) - 9_7_22, 5.59 PM.mp3");
      setQuantityLimitedStatment("Limited edition of 33ml / 200 - 5ml / 1000");
      setQuantityLimitedMob1Statment("Limited edition - 33ml / 200 each");
      setQuantityLimitedMob2Statment("5ml / 1000 each");
    } else if (product == "WE") {
      setPDITitle("WE");
      sethandImage33ml("WE_ 33ml_s.png");
      sethandImage5ml("WE_ 5ml_s.png");
      setPDICardLine("WE are all encompassing. WE are independent.");
      setPDIDescription(
        "With CLEANSING Coriander, Orange and Thyme; we ignite your creative mind and act as an aphrodisiac. We SUPPORT tranquility with Vetiver while Roman Chamomile GUIDES us towards inner equilibrium.",
      );
      setidofmodelbig(
        "************************************************************",
      );
      setidofmodelsmall(
        "************************************************************",
      );
      setPDSound("we (vol adj & cut)- 9722, 5.49 PM.mp3");
      setQuantityLimitedStatment("Limited edition of 33ml / 200 - 5ml / 1000");
      setQuantityLimitedMob1Statment("Limited edition - 33ml / 200 each");
      setQuantityLimitedMob2Statment("5ml / 1000 each");
    } else if (product == "toolObsidian") {
      setPDITitle("Tool");
      sethandImagetool("obsidian_tool_s.png");
      setPDICardLine(
        "Shaped to fit into the 33ml bottle; used for acupressure, massage or as a spoon for caviar.",
      );
      setPDIDescription(
        "Obsidian is a strongly protective stone, helpful for sensitive people. This stone clears confusion and grounds us into the present moment.",
      );
    } else if (product == "toolOpal") {
      setPDITitle("Tool");
      sethandImagetool("Opal_tool_s.png");
      setPDICardLine(
        "Shaped to fit into the 33ml bottle; used for acupressure, massage or as a spoon for caviar.",
      );
      setPDIDescription(
        "Opal enhances cosmic consciousness and induces psychic and mystical visions. It stimulates originality and creativity.",
      );
    } else if (product == "toolTiger") {
      setPDITitle("Tool");
      sethandImagetool("obsidian_tool_s.png");
      setPDICardLine(
        "Shaped to fit into the 33ml bottle; used for acupressure, massage or as a spoon for caviar.",
      );
      setPDIDescription("Tiger tool.");
    }
  };
  const AboutAnimation = () => {
    let event = new CustomEvent("Aboutanimation", {});
    setBodyMapAnimation1("BodyMap MindDIVMaskReverse");
    setBodyMapAnimation2("BodyMap TruthDIVMaskReverse");
    setBodyMapAnimation3("BodyMap HeartDIVMaskReverse");
    setBodyMapAnimation4("BodyMap CenterDIVMaskReverse");
    setBodyMapAnimation5("BodyMap ReliefDIVMaskReverse");
    setBodyMapAnimation6("BodyMap EnergizeDIVMaskReverse");
    setBodyMapAnimation7("BodyMap GroundDIVMaskReverse");
    setTimeout(() => {
      setMindDisplay("none");
      setTruthDisplay("none");
      setHeartDisplay("none");
      setCenterDisplay("none");
      setReliefDisplay("none");
      setEnergizeDisplay("none");
      setGroundDisplay("none");
    }, 500);
    window.dispatchEvent(event);
    setStartDisplay("none");
    setSceneStatus("INSIDE");
  };

  const ShopAnimation = () => {
    let event = new CustomEvent("Shopanimation", {});
    window.dispatchEvent(event);
    setStartDisplay("none");
  };

  const Homeanimation = () => {
    let event = new CustomEvent("Homeanimation", {});
    window.dispatchEvent(event);
    // document.getElementsByClassName("INCORPbottomend")[0].style.cursor = "default";
    document.getElementsByClassName("INCORPbottomend")[0].style.cursor = "grab";
  };
  const Contactanimation = (action) => {
    let event = new CustomEvent("Contactanimation", {
      detail: {
        action,
      },
    });
    window.dispatchEvent(event);
  };

  const Productsanimation = () => {
    if (SceneStatus == "OUTSIDE") {
      StartAnimation();
    } else {
      let event = new CustomEvent("Productsanimation", {});
      window.dispatchEvent(event);
    }
  };

  const startdisplayfunction = () => {
    setStartDisplay("");
  };

  const startdisplayOfffunction = () => {
    setStartDisplay("none");
  };

  const ProductDisplayFunction = () => {
    setTimeout(() => {
      setProductDetailsDisplay("");
    }, 200);
  };

  const ProductDisplaySwitch = (action) => {
    if (action == "open") {
      setTimeout(() => {
        setProductDetailsDisplay("");
      }, 200);
    } else if (action == "close") {
      setProductDetailsDisplay("none");
      setProductDetailsBottleDisplay("none");
      setProductDetailsBottleToolDisplay("none");
      setProductDetailsBottleSmallDisplay("none");
      setProductDetailsBottleSmallBodyDisplay("none");
      setProductDetailsBottlecupDisplay("none");
      ArrowsDisplayFunction("on");
      TurnoffAnnotations(1);
      // console.log(PDITitle);
      if (PDITitle == "I") {
        setidofmodelbig(
          "************************************************************",
        );
        setidofmodelsmall(
          "************************************************************",
        );
      } else if (PDITitle == "WE") {
        setidofmodelbig(
          "************************************************************",
        );
        setidofmodelsmall(
          "************************************************************",
        );
      } else if (PDITitle == "YOU") {
        setidofmodelbig(
          "************************************************************",
        );
        setidofmodelsmall(
          "************************************************************",
        );
      }
    }
  };
  const ProductannotationsSwitchOff = () => {
    setProductDetailsBottleDisplay("none");
    setProductDetailsBottleToolDisplay("none");
    setProductDetailsBottleSmallDisplay("none");
    setProductDetailsBottleSmallBodyDisplay("none");
    setProductDetailsBottlecupDisplay("none");
  };

  const ProductDisplayBottleSwitch = (target, action) => {
    console.log(target, action);
    if (window.innerWidth < 601) {
      if (action == "open") {
        let event = new CustomEvent("AnnotationBackgroundMobileShow", {});
        window.dispatchEvent(event);
      }
    }
    if (target == "Body") {
      if (action == "open") {
        setProductDetailsBottleDisplay("");
        setProductDetailsBottleToolDisplay("none");
        setProductDetailsBottleSmallDisplay("none");
        setProductDetailsBottlecupDisplay("none");
        setProductDetailsBottleSmallBodyDisplay("none");
      } else if (action == "close") {
        setProductDetailsBottleDisplay("none");
      }
    } else if (target == "Tool") {
      if (action == "open") {
        setProductDetailsBottleToolDisplay("");
        setProductDetailsBottleDisplay("none");
        setProductDetailsBottleSmallDisplay("none");
        setProductDetailsBottlecupDisplay("none");
        setProductDetailsBottleSmallBodyDisplay("none");
      } else if (action == "close") {
        setProductDetailsBottleToolDisplay("none");
      }
    } else if (target == "SmallBottle") {
      if (action == "open") {
        setProductDetailsBottleSmallDisplay("");
        setProductDetailsBottleDisplay("none");
        setProductDetailsBottleToolDisplay("none");
        setProductDetailsBottlecupDisplay("none");
        setProductDetailsBottleSmallBodyDisplay("none");
      } else if (action == "close") {
        setProductDetailsBottleSmallDisplay("none");
      }
    } else if (target == "SmallBottleBody") {
      if (action == "open") {
        setProductDetailsBottleSmallDisplay("none");
        setProductDetailsBottleDisplay("none");
        setProductDetailsBottleToolDisplay("none");
        setProductDetailsBottlecupDisplay("none");
        setProductDetailsBottleSmallBodyDisplay("");
      } else if (action == "close") {
        setProductDetailsBottleSmallBodyDisplay("none");
      }
    } else if (target == "Cover") {
      if (action == "open") {
        setProductDetailsBottlecupDisplay("");
        setProductDetailsBottleDisplay("none");
        setProductDetailsBottleToolDisplay("none");
        setProductDetailsBottleSmallDisplay("none");
        setProductDetailsBottleSmallBodyDisplay("none");
      } else if (action == "close") {
        setProductDetailsBottlecupDisplay("none");
      }
    }
  };

  const colorPressed = (color) => {
    setcurrentColor(color);
    changeColor(color);
  };

  const changeColor = (color) => {
    let event = new CustomEvent("change-color", {
      detail: {
        color,
      },
    });
    window.dispatchEvent(event);
  };

  const TurnoffAnnotations = (product) => {
    let event = new CustomEvent("turnoffannotations", {
      detail: {
        product,
      },
    });
    window.dispatchEvent(event);
  };

  const toggleDisplay = () => {
    setFingerDisplay("");
  };

  const handleClick = () => {
    setFingerDisplay("none");
  };

  const animationEndFunction = () => {
    setAnimationEnd(true);
  };

  const cameraAnimationEndFunction = () => {
    setcameraAnimationEnd(true);
  };

  const cameraAnimationEndTimeoutFunction = (timeout) => {
    setcameraAnimationEndTimeout(timeout);
  };

  const reset = () => {
    changeColor("Black");
    emitSectionChangeEvent("target");
  };

  const ArrowsDisplayFunction = (state) => {
    if (state == "on") {
      setArrowsDisplay("");
    } else if (state == "off") {
      setArrowsDisplay("none");
    }
  };

  const closeMenu = () => {
    let event = new CustomEvent("closeMenu", {});
    window.dispatchEvent(event);
  };

  const SetProductCounterP1Big = (action) => {
    if (action == "reduce") {
      if (P1Big == 0) {
        return;
      }
      setP1Big(P1Big - 1);
    } else if (action == "increase") {
      setP1Big(P1Big + 1);
    }
  };
  const SetProductCounterP1Small = (action) => {
    if (action == "reduce") {
      if (P1Small == 0) {
        return;
      }
      setP1Small(P1Small - 1);
    } else if (action == "increase") {
      setP1Small(P1Small + 1);
    }
  };

  // if (shopLoading) {
  //   return <p>Loading ...</p>;
  // }

  // if (shopError) {
  //   return <p>{shopError.message}</p>;
  // }

  // useEffect(() => {
  //   if (window.innerWidth < 1000 && window.innerHeight < 601 && window.innerWidth > window.innerHeight) {
  //     return (
  //       <div className="App IncorpFont">
  //         Please rotate your device
  //       </div>
  //     )
  //   }
  // }, [window.innerWidth]);

  const checkOrientationChange = () => {
    setTimeout(() => {
      if (
        window.innerWidth < 1000 &&
        window.innerHeight < 601 &&
        window.innerWidth > window.innerHeight
      ) {
        setRotateDeviceDisplay("");
      } else {
        setReloadText("Reloading ...");
        window.location.reload();
      }
    }, 100);
  };

  window.addEventListener("orientationchange", checkOrientationChange);

  if (
    window.innerWidth < 1000 &&
    window.innerHeight < 601 &&
    window.innerWidth > window.innerHeight
  ) {
    return <div className="App IncorpFont">{ReloadText}</div>;
  } else {
    return (
      <div className="App">
        <div
          className="App IncorpFont"
          style={{ display: RotateDeviceDisplay }}
        >
          {ReloadText}
        </div>
        <GlobalUI
          shopData={shopData}
          ProductAddedtitle={ProductAddedtitle}
          totalitems={totalitems}
          idofmodelbig={idofmodelbig}
          idofmodelsmall={idofmodelsmall}
          StartAnimation={StartAnimation}
          AboutAnimation={AboutAnimation}
          ShopAnimation={ShopAnimation}
          Homeanimation={Homeanimation}
          Productsanimation={Productsanimation}
          Contactanimation={Contactanimation}
          annotationToggle={annotationToggle}
          Heartback={Heartback}
          StartDisplay={StartDisplay}
          ProductDetailsDisplay={ProductDetailsDisplay}
          ProductDisplaySwitch={ProductDisplaySwitch}
          P1Big={P1Big}
          P1Small={P1Small}
          P2Big={P2Big}
          P2Small={P2Small}
          P3Big={P3Big}
          P3Small={P3Small}
          SetProductCounterP1Big={SetProductCounterP1Big}
          SetProductCounterP1Small={SetProductCounterP1Small}
          addVariantToCart={addVariantToCart}
          checkout={checkout}
          removeLineItemInCart={removeLineItemInCart}
          updateLineItemInCart={updateLineItemInCart}
          updateLineItemInCartAddMobile={updateLineItemInCartAddMobile}
          updateLineItemInCartLessMobile={updateLineItemInCartLessMobile}
          updateLineItemInCartLessDesktop={updateLineItemInCartLessDesktop}
          isCartOpen={isCartOpen}
          handleCartClose={handleCartClose}
          customerAccessToken={customerAccessToken}
          cameraAnimationEnd={cameraAnimationEnd}
          cameraAnimationEndTimeout={cameraAnimationEndTimeout}
          ProductDetailsBottleDisplay={ProductDetailsBottleDisplay}
          ProductDetailsBottlecupDisplay={ProductDetailsBottlecupDisplay}
          ProductDetailsBottleToolDisplay={ProductDetailsBottleToolDisplay}
          ProductDisplayBottleSwitch={ProductDisplayBottleSwitch}
          ProductDetailsBottleSmallDisplay={ProductDetailsBottleSmallDisplay}
          ProductDetailsBottleSmallBodyDisplay={
            ProductDetailsBottleSmallBodyDisplay
          }
          PDITitle={PDITitle}
          PDICardLine={PDICardLine}
          PDIDescription={PDIDescription}
          QuantityLimitedStatment={QuantityLimitedStatment}
          QuantityLimitedMob1Statment={QuantityLimitedMob1Statment}
          QuantityLimitedMob2Statment={QuantityLimitedMob2Statment}
          PDSound={PDSound}
          MindDisplay={MindDisplay}
          TruthDisplay={TruthDisplay}
          HeartDisplay={HeartDisplay}
          CenterDisplay={CenterDisplay}
          ReliefDisplay={ReliefDisplay}
          EnergizeDisplay={EnergizeDisplay}
          GroundDisplay={GroundDisplay}
          NotificationDisplay={NotificationDisplay}
          SceneStatus={SceneStatus}
          // removeLineItemInCarts={removeLineItemInCart}
          BodyMapAnimation1={BodyMapAnimation1}
          BodyMapAnimation2={BodyMapAnimation2}
          BodyMapAnimation3={BodyMapAnimation3}
          BodyMapAnimation4={BodyMapAnimation4}
          BodyMapAnimation5={BodyMapAnimation5}
          BodyMapAnimation6={BodyMapAnimation6}
          BodyMapAnimation7={BodyMapAnimation7}
          updateXY={updateXY}
          menuButtonsDisplay={menuButtonsDisplay}
          InfoImageSRC={InfoImageSRC}
          ProductInfoSwitcher={ProductInfoSwitcher}
          Changerimg={Changerimg}
          ProductInfoSwitcherMobile={ProductInfoSwitcherMobile}
          bodyUnderline={bodyUnderline}
          bodySmallUnderline={bodySmallUnderline}
          toolObsidian={toolObsidian}
          toolOpal={toolOpal}
          toolTiger={toolTiger}
          currentModel={currentModel}
          handImage33ml={handImage33ml}
          handImage5ml={handImage5ml}
          handImagetool={handImagetool}
          BodyMapDisplaySwitcher={BodyMapDisplaySwitcher}
        />
        {/* <Pictogram pictogramDisplay={pictogramDisplay}/> */}
        {window.innerWidth > 601 ? (
          <Scene3d
            loadinglogostop={loadinglogostop}
            toggleDisplay={toggleDisplay}
            handleClick={handleClick}
            SceneStatus={SceneStatus}
            ProductDisplayFunction={ProductDisplayFunction}
            animationEndFunction={animationEndFunction}
            animationEnd={animationEnd}
            cameraAnimationEndFunction={cameraAnimationEndFunction}
            cameraAnimationEndTimeoutFunction={
              cameraAnimationEndTimeoutFunction
            }
            ArrowsDisplay={ArrowsDisplay}
            BodyMapDisplaySwitcher={BodyMapDisplaySwitcher}
            ArrowsDisplayFunction={ArrowsDisplayFunction}
            ProductDetailsChanger={ProductDetailsChanger}
            ProductDisplayBottleSwitch={ProductDisplayBottleSwitch}
            startdisplayfunction={startdisplayfunction}
            ProductDisplaySwitch={ProductDisplaySwitch}
            updateSceneStatus={updateSceneStatus}
            startdisplayOfffunction={startdisplayOfffunction}
            ProductannotationsSwitchOff={ProductannotationsSwitchOff}
            windowX={windowX}
            windowY={windowY}
            switchMenuButtonsDisplay={switchMenuButtonsDisplay}
            pictogram={pictogram}
            ProductInfoSwitcherOff={ProductInfoSwitcherOff}
            currentModel={currentModel}
            updateCurrentModel={updateCurrentModel}
            PDSound={PDSound}
          />
        ) : (
          <Scene3dMobile
            loadinglogostop={loadinglogostop}
            toggleDisplay={toggleDisplay}
            handleClick={handleClick}
            SceneStatus={SceneStatus}
            ProductDisplayFunction={ProductDisplayFunction}
            animationEndFunction={animationEndFunction}
            animationEnd={animationEnd}
            cameraAnimationEndFunction={cameraAnimationEndFunction}
            cameraAnimationEndTimeoutFunction={
              cameraAnimationEndTimeoutFunction
            }
            ArrowsDisplay={ArrowsDisplay}
            BodyMapDisplaySwitcher={BodyMapDisplaySwitcher}
            ArrowsDisplayFunction={ArrowsDisplayFunction}
            ProductDetailsChanger={ProductDetailsChanger}
            ProductDisplayBottleSwitch={ProductDisplayBottleSwitch}
            startdisplayfunction={startdisplayfunction}
            ProductDisplaySwitch={ProductDisplaySwitch}
            updateSceneStatus={updateSceneStatus}
            startdisplayOfffunction={startdisplayOfffunction}
            ProductannotationsSwitchOff={ProductannotationsSwitchOff}
            windowX={windowX}
            windowY={windowY}
            switchMenuButtonsDisplay={switchMenuButtonsDisplay}
            pictogram={pictogram}
            ProductInfoSwitcherOff={ProductInfoSwitcherOff}
            closeMenu={closeMenu}
            currentModel={currentModel}
            updateCurrentModel={updateCurrentModel}
          />
        )}
        {/* <Cart
          removeLineItemInCart={removeLineItemInCart}
          updateLineItemInCart={updateLineItemInCart}
          checkout={checkout}
          isCartOpen={isCartOpen}
          handleCartClose={handleCartClose}
          customerAccessToken={customerAccessToken}
        /> */}
      </div>
    );
  }
}

export default App;
