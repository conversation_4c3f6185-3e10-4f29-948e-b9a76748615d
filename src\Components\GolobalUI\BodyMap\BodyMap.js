// src/App.js
import React, { Component } from "react";
import "./BodyMap.css";

class BodyMap extends Component {
  constructor(props) {
    super(props);
  }

  render() {
    return (
      <>
        <div className="BodyMap MindDIV" style={{ display: this.props.MindDisplay }}>
          <div className="BodyMapTitle">1. Mind; clear thinking </div>
          <div className="BodyMapDescription">
            One thumb width above the center of your eyebrow, in line with your pupil, press into the little dip on your forehead.
          </div>
          <div className="BodyMapImage MindIMG"><img src="Point_1_sm.png" alt="" /></div>
        </div>
        {/* <div className={this.props.BodyMapAnimation1} style={{ display: this.props.MindDisplay }}>
        </div> */}
        <div className="BodyMap TruthDiv" style={{ display: this.props.TruthDisplay }}>
          <div className="BodyMapTitle">2. Truth; clear communication</div>
          <div className="BodyMapDescription">At the top of your breastbone in the notch, pressing slightly into the soft tissue.</div>
          <div className="BodyMapImage TruthIMG"><img src="Point_2_sm.png" alt="" /></div>
        </div>
        {/* <div className={this.props.BodyMapAnimation2} style={{ display: this.props.TruthDisplay }}>
        </div> */}
        <div className="BodyMap HeartDiv" style={{ display: this.props.HeartDisplay }}>
          <div className="BodyMapTitle">3. Heart; loving self</div>
          <div className="BodyMapDescription">On the center of your breastbone at the level of an average nipple line.</div>
          <div className="BodyMapImage HeartIMG"><img src="Point_3_sm.png" alt="" /></div>
        </div>
        {/* <div className={this.props.BodyMapAnimation3} style={{ display: this.props.HeartDisplay }}>
        </div> */}
        <div className="BodyMap CenterDiv" style={{ display: this.props.CenterDisplay }}>
          <div className="BodyMapTitle">4. Center; balancing self</div>
          <div className="BodyMapDescription">Find the most sensitive spot 2–4 finger widths below your navel.</div>
          <div className="BodyMapImage CenterIMG"><img src="Point_4_sm.png" alt="" /></div>
        </div>
        {/* <div className={this.props.BodyMapAnimation4} style={{ display: this.props.CenterDisplay }}>
        </div> */}
        <div className="BodyMap ReliefDiv" style={{ display: this.props.ReliefDisplay }}>
          <div className="BodyMapTitle">5. Relief; pain killer</div>
          <div className="BodyMapDescription">In the center of the web between the thumb and index finger.</div>
          <div className="BodyMapImage ReliefIMG"><img src="Point_5_sm.png" alt="" /></div>
        </div>
        {/* <div className={this.props.BodyMapAnimation5} style={{ display: this.props.ReliefDisplay }}>
        </div> */}
        <div className="BodyMap EnergizeDiv" style={{ display: this.props.EnergizeDisplay }}>
          <div className="BodyMapTitle">6. Energize; recharge</div>
          <div className="BodyMapDescription">4 finger widths below your kneecap and 1 thumb width outside your shinbone.</div>
          <div className="BodyMapImage EnergizeIMG"><img src="Point_6_sm.png" alt="" /></div>
        </div>
        {/* <div className={this.props.BodyMapAnimation6} style={{ display: this.props.EnergizeDisplay }}>
        </div> */}
        <div className="BodyMap GroundDiv" style={{ display: this.props.GroundDisplay }}>
          <div className="BodyMapTitle">7. Ground; be here now</div>
          <div className="BodyMapDescription">At the highest point of your inside foot arch, go slightly toward your big toe off of the joint.</div>
          <div className="BodyMapImage GroundIMG"><img src="Point_7_sm.png" alt="" /></div>
        </div>
        {/* <div className={this.props.BodyMapAnimation7} style={{ display: this.props.GroundDisplay }}>
        </div> */}
      </>
    );
  }
}

export default BodyMap;
