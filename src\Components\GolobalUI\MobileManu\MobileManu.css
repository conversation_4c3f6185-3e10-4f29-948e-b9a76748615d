.bm-menu-wrap{
    width: 60% !important;
}
/* Position and sizing of burger button */
.bm-burger-button {
    position: fixed;
    width: 23px;
    height: 17px;
    left: 12px;
    top: 12px;
    z-index: 3;
  }
  /* Color/shape of burger icon bars */
  .bm-burger-bars {
    /* background: #373a47; */
    background: black;
    height: 1.25px !important;
  }
  
  /* Color/shape of burger icon bars on hover*/
  .bm-burger-bars-hover {
    background: black;
  }
  
  /* Position and sizing of clickable cross button */
  .bm-cross-button {
    height: 24px;
    width: 24px;
  }
  
  /* Color/shape of close button cross */
  .bm-cross {
    background: #bdc3c7;
  }
  
  /*
  Sidebar wrapper styles
  Note: Beware of modifying this element as it can break the animations - you should not need to touch it in most cases
  */
  .bm-menu-wrap {
    position: fixed;
    height: 100%;
  }
  
  /* General sidebar styles */
  .bm-menu {
    /* background: #373a47;
    padding: 2.5em 1.5em 0;
    font-size: 1.15em; */
    background: #e7e9eb;
    overflow-y: hidden !important;
    padding: 2.5em 1.5em 0;
  }
  
  /* Morph shape necessary with bubble or elastic */
  .bm-morph-shape {
    fill: #373a47;
  }
  
  /* Wrapper for item list */
  .bm-item-list {
    /* color: #b8b7ad;
    padding: 0.8em; */
    color: black !important;
    padding: 0em !important;
    height: auto !important;
    width: 100%;
    background-color: transparent;
  }
  
  /* Individual item */
  .bm-item {
    display: inline-block;
    color: black;
    padding: 20%;
    font-size: 1.2em;
    border: none !important;
    outline: none;
  }
  .outer-container a {
    border: none !important;
  }
  .outer-container a:active {
    border:1px solid black;
  }
  .outer-container a:focus {
    border: none !important;
  }
  .bm-item:hover {
    border:1px solid black !important;
  }
  
  /* Styling of overlay */
  .bm-overlay {
    background: rgba(0, 0, 0, 0.3);
  }