import React from "react";
import ReactDOM from "react-dom";
import App from "./App";
import {
  ApolloClient,
  createHttpLink,
  InMemoryCache,
  ApolloProvider,
} from "@apollo/client";
import { setContext } from "@apollo/client/link/context";
// import './app.css';

const httpLink = createHttpLink({
  uri: "https://incorp-world.myshopify.com/api/graphql",
});

const middlewareLink = setContext(() => ({
  headers: {
    "X-Shopify-Storefront-Access-Token": "e863bded64aa058e94adc7ab7affece7",
  },
}));

const client = new ApolloClient({
  link: middlewareLink.concat(httpLink),
  cache: new InMemoryCache({
    typePolicies: {
      Cart: {
        fields: {
          lines: {
            merge(existing = { edges: [] }, incoming) {
              // Always return the incoming data for cart lines to avoid cache conflicts
              return incoming || existing;
            },
          },
        },
      },
    },
  }),
});

ReactDOM.render(
  <ApolloProvider client={client}>
    <App />
  </ApolloProvider>,
  document.getElementById("root"),
);
