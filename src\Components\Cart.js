import React, { useState } from "react";

import LineItem from "./LineItem";
// import "../cart.css"
import "./cart.css";
function Cart(props) {
  const [Policy1height, setPolicy1height] = useState("50%");
  const [Policy1status, setPolicy1status] = useState("closed");
  const [policy1img, setpolicy1img] = useState("plus (1).png");

  const [Policy2height, setPolicy2height] = useState("50%");
  const [Policy2status, setPolicy2status] = useState("closed");
  const [policy2img, setpolicy2img] = useState("plus (1).png");

  const [itemsCartheight, setitemsCartheight] = useState("calc(65vh - 235px)");

  const [PolicyP1, setPolicyP1] = useState("none");
  const [PolicyP2, setPolicyP2] = useState("none");
  const openCheckout = () => {
    window.open(props.checkout.checkoutUrl);
  };
  const SwitchDisplayPolices = (action) => {
    if (action == "D1") {
      if (Policy1status == "closed") {
        setPolicy1height("8vh");
        setPolicy1status("opened");
        setpolicy1img("minus-sign.png");
        setPolicyP1("");
        if (Policy2status == "opened") {
          setPolicy2height("50%");
          setPolicy2status("closed");
          setpolicy2img("plus (1).png");
          setPolicyP2("none");
        }
      } else if (Policy1status == "opened") {
        setPolicy1height("50%");
        setPolicy1status("closed");
        setpolicy1img("plus (1).png");
        setPolicyP1("none");
      }
    } else if (action == "D2") {
      if (Policy2status == "closed") {
        setPolicy2height("4vh");
        setitemsCartheight("calc(65vh - 275px)");
        setPolicy2status("opened");
        setpolicy2img("minus-sign.png");
        setPolicyP2("");
        if (Policy1status == "opened") {
          setPolicy1height("50%");
          setPolicy1status("closed");
          setpolicy1img("plus (1).png");
          setPolicyP1("none");
        }
      } else if (Policy2status == "opened") {
        setPolicy2height("50%");
        setitemsCartheight("calc(65vh - 235px)");
        setPolicy2status("closed");
        setpolicy2img("plus (1).png");
        setPolicyP2("none");
      }
    }
  };
  let line_items = props.checkout.lines
    ? props.checkout.lines.edges.map((line_item) => {
        // console.log(line_item);
        return (
          <LineItem
            removeLineItemInCart={props.removeLineItemInCart}
            updateLineItemInCart={props.updateLineItemInCart}
            addVariantToCart={props.addVariantToCart}
            key={line_item.node.id.toString()}
            line_item={line_item.node}
            updateLineItemInCartLessDesktop={
              props.updateLineItemInCartLessDesktop
            }
            updateLineItemInCartAddMobile={props.updateLineItemInCartAddMobile}
            updateLineItemInCartLessMobile={
              props.updateLineItemInCartLessMobile
            }
            ibigcounter={props.ibigcounter}
            ibigcounterref={props.ibigcounterref}
            setibigcounter={props.setibigcounter}
            ismallcounter={props.ismallcounter}
            ismallcounterref={props.ismallcounterref}
            setismallcounter={props.setismallcounter}
            youbigcounter={props.youbigcounter}
            youbigcounterref={props.youbigcounterref}
            setyoubigcounter={props.setyoubigcounter}
            yousmallcounter={props.yousmallcounter}
            yousmallcounterref={props.yousmallcounterref}
            setyousmallcounterl={props.setyousmallcounterl}
            webigcounter={props.webigcounter}
            webigcounterref={props.webigcounterref}
            setwewebigcounter={props.setwewebigcounter}
            wesmallcouner={props.wesmallcouner}
            wesmallcounerref={props.wesmallcounerref}
            setwesmallcounerl={props.setwesmallcounerl}
            Obsidiancounter={props.Obsidiancounter}
            Obsidiancounterref={props.Obsidiancounterref}
            setObsidiancounter={props.setObsidiancounter}
            Opalcounter={props.Opalcounter}
            Opalcounterref={props.Opalcounterref}
            setOpalcounter={props.setOpalcounter}
            Tigercounter={props.Tigercounter}
            Tigercounterref={props.Tigercounterref}
            setTigercounter={props.setTigercounter}
          />
        );
      })
    : [];

  return (
    <div className="CartContainer">
      <div className="CartHeader">
        YOUR CART
        <button
          className="CloseCart"
          onClick={() => props.ShopPageDisplaySwitch("close")}
        >
          <img src="closeV3.png" />
        </button>
      </div>
      <div className="CartItems" style={{ maxHeight: itemsCartheight }}>
        <ul> {line_items}</ul>
      </div>
      {/* <div className="CartDetails">
          <div className="CartDetailsHead">Payment Details</div>
        </div> */}
      <div className="CartEditedSection">
        <div className="CartDetailsList">
          <ul style={{ fontSize: "14px" }}>
            <li>
              Subtotal{" "}
              <span>
                €
                {props.checkout.cost && props.checkout.cost.subtotalAmount
                  ? props.checkout.cost.subtotalAmount.amount
                      .toString(10)
                      .split(".")[0].length > 3
                    ? props.checkout.cost.subtotalAmount.amount
                        .toString(10)
                        .substring(
                          0,
                          props.checkout.cost.subtotalAmount.amount
                            .toString(10)
                            .split(".")[0].length - 3,
                        ) +
                      "," +
                      props.checkout.cost.subtotalAmount.amount
                        .toString(10)
                        .substring(
                          props.checkout.cost.subtotalAmount.amount
                            .toString(10)
                            .split(".")[0].length - 3,
                        ) +
                      "0"
                    : props.checkout.cost.subtotalAmount.amount + "0"
                  : 0.0}
              </span>
            </li>
            <li>
              Tax{" "}
              <span>
                €
                {props.checkout.cost && props.checkout.cost.totalTaxAmount
                  ? props.checkout.cost.totalTaxAmount.amount + "0"
                  : 0.0}
              </span>
            </li>
            <li>
              Shipping <span>Calculated at next step</span>
            </li>
          </ul>
        </div>
        <div className="TotalSection">
          <div
            className="totalEdited"
            style={{ fontSize: "14px", color: "#000" }}
          >
            Total
          </div>
          <div
            style={{
              width: "60%",
              display: "flex",
              justifyContent: "flex-end",
              alignItems: "flex-end",
            }}
          >
            <div style={{ fontSize: "8px", marginBottom: "2px" }}>
              Euro&nbsp;&nbsp;
            </div>
            <div style={{ fontSize: "18px", marginRight: "0%" }}>
              €
              {props.checkout.cost && props.checkout.cost.totalAmount
                ? props.checkout.cost.totalAmount.amount
                    .toString(10)
                    .split(".")[0].length > 3
                  ? props.checkout.cost.totalAmount.amount
                      .toString(10)
                      .substring(
                        0,
                        props.checkout.cost.totalAmount.amount
                          .toString(10)
                          .split(".")[0].length - 3,
                      ) +
                    "," +
                    props.checkout.cost.totalAmount.amount
                      .toString(10)
                      .substring(
                        props.checkout.cost.totalAmount.amount
                          .toString(10)
                          .split(".")[0].length - 3,
                      ) +
                    "0"
                  : props.checkout.cost.totalAmount.amount + "0"
                : 0.0}
            </div>
          </div>
        </div>
        <div className="CartBodyMessage">
          By purchasing <b>INCORP</b> products you become part of the pionners
          building a self sustaining healing arts platform.
        </div>
        <div className="CartOpenSection">
          {/* <div className="Policy" >
              <button onClick={() => SwitchDisplayPolices("D1")}>
                CANCELLATION POLICY <img src={policy1img} />
              </button>
              <p style={{ height: Policy1height ,display:PolicyP1 }}>
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus quis arcu vitae risus luctus ornare non ut erat. Nulla facilisi. Nam venenatis, ligula scelerisque maximus maximus, erat nibh hendrerit dolor, vel sollicitudin leo massa eleifend dolor.
              </p>       
            </div> */}
          <div className="Policy">
            <button onClick={() => SwitchDisplayPolices("D2")}>
              RETURN policy <img src={policy2img} />
            </button>
            <p style={{ height: Policy2height, display: PolicyP2 }}>
              INCORP products are unique art pieces each numbered and are non
              refundable.
            </p>
          </div>
          {/* <div className="Policy" style={{ height: Policy2height }}>
              <button>
                RETURNS POLICY
              </button>
            </div> */}
        </div>
      </div>
      <div className="CartFooter">
        <button className="CartCheckout" onClick={openCheckout}>
          Checkout
        </button>
      </div>
    </div>

    //   {line_items}
    // $ {props.checkout.subtotalPrice}$ {props.checkout.totalTax}$
    //  {props.checkout.totalPrice}

    //   <button className="Cart__checkout button" onClick={openCheckout}>Checkout</button>
  );
}

export default Cart;
