import React, { useState } from "react";

import LineItem from "../../LineItem";
import "./MobileCart.css";
function MobileCart(props) {
  const [Policy1height, setPolicy1height] = useState("50%");
  const [Policy1status, setPolicy1status] = useState("closed");
  const [policy1img, setpolicy1img] = useState("plus (1).png");

  const [Policy2height, setPolicy2height] = useState("50%");
  const [Policy2status, setPolicy2status] = useState("closed");
  const [policy2img, setpolicy2img] = useState("plus (1).png");

  const openCheckout = () => {
    window.open(props.checkout.checkoutUrl);
  };
  const SwitchDisplayPolices = (action) => {
    if (action == "D1") {
      if (Policy1status == "closed") {
        setPolicy1height("12vh");
        setPolicy1status("opened");
        setpolicy1img("minus-sign.png");
        if (Policy2status == "opened") {
          setPolicy2height("50%");
          setPolicy2status("closed");
          setpolicy2img("plus (1).png");
        }
      } else if (Policy1status == "opened") {
        setPolicy1height("50%");
        setPolicy1status("closed");
        setpolicy1img("plus (1).png");
      }
    } else if (action == "D2") {
      if (Policy2status == "closed") {
        setPolicy2height("20vh");
        setPolicy2status("opened");
        setpolicy2img("minus-sign.png");
        if (Policy1status == "opened") {
          setPolicy1height("50%");
          setPolicy1status("closed");
          setpolicy1img("plus (1).png");
        }
      } else if (Policy2status == "opened") {
        setPolicy2height("50%");
        setPolicy2status("closed");
        setpolicy2img("plus (1).png");
      }
    }
  };
  let line_items = props.checkout.lines
    ? props.checkout.lines.edges.map((line_item) => {
        // console.log(line_item);
        return (
          <LineItem
            removeLineItemInCart={props.removeLineItemInCart}
            updateLineItemInCart={props.updateLineItemInCart}
            key={line_item.node.id.toString()}
            line_item={line_item.node}
          />
        );
      })
    : [];

  return (
    <div className="MobileCartContainer">
      <div className="CartHeader">
        <button
          className="MobileCloseCart"
          onClick={() => props.DisplayMobileCart("close")}
        >
          <img src="close.png" />
        </button>
      </div>
      <div className="MobileCartBody">
        <div className="MobileCartItems">
          <ul> {line_items}</ul>
        </div>
        <div className="CartDetails">
          <div className="CartDetailsHead">Payment Details</div>
          <div className="CartDetailsList">
            <ul>
              <li>
                Total{" "}
                <span>
                  €
                  {props.checkout.cost && props.checkout.cost.subtotalAmount
                    ? props.checkout.cost.subtotalAmount.amount
                    : 0}
                </span>
              </li>
              <li>
                Shipping Charges <span>€{props.checkout.totalTax}</span>
                <p>includes long distance fee of for 16km</p>
              </li>
              <li id="dashed">
                <b>Total</b>{" "}
                <span>
                  <b>
                    €
                    {props.checkout.cost && props.checkout.cost.totalAmount
                      ? props.checkout.cost.totalAmount.amount
                      : 0}
                  </b>
                </span>
              </li>
            </ul>
          </div>
        </div>
        <div className="CartBodyMessage">
          By purchasing <b>INCORP</b> products you become part of the pionners
          building a self sustaining healing arts platform.
        </div>
        <div className="CartOpenSection">
          <div className="Policy" style={{ height: Policy1height }}>
            <button onClick={() => SwitchDisplayPolices("D1")}>
              CANCELLATION POLICY <img src={policy1img} />
            </button>
          </div>
          <div className="Policy" style={{ height: Policy2height }}>
            <button onClick={() => SwitchDisplayPolices("D2")}>
              RETURNS POLICY <img src={policy2img} />
            </button>
          </div>
        </div>
      </div>
      <div className="MobileCartFooter">
        <button className="MobileCartCheckout" onClick={openCheckout}>
          Checkout
        </button>
      </div>
    </div>
  );
}

export default MobileCart;
