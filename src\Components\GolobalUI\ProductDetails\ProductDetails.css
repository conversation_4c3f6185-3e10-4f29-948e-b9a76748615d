@font-face {
  font-family: "Inter";
  src: url(../../../Fonts/Inter-Light-BETA.ttf);
}

.card {
  width: 100%;
  /* height: 100%; */
  margin-left: auto;
  margin-right: auto;
  margin-top: 0%;
  padding: 4%;
  box-sizing: border-box;
}
.cardtitle {
  width: 100%;
  /* height: 6%; */
  font-weight: 500;
  text-align: left;
  /* text-transform: uppercase; */
  font-size: 1.1vw;
  font-weight: bold;
  margin-left: auto;
  margin-right: auto;
  margin-top: 1%;
  padding-bottom: 4%;
  border-bottom: 0.5px solid black;
}
#titleedited {
  height: 8%;
}
.cardline {
  font-style: italic;
  text-align: left;
  font-weight: 500;
  margin-top: 4%;
  margin-bottom: 4%;
  /* font-size: 0.8vw; */
  font-size: 1vw;
}
.editionMSG {
  margin-top: 7%;
}
.carddescription {
  margin-top: 8%;
  text-align: left;
  /* line-height: 1vw; */
  line-height: 1.1vw;
  font-size: 0.85vw;
  flex-wrap: wrap;
}
.carddescription p {
  color: #878787;
}
.cardproduct {
  display: grid;
  grid-column: 1fr 1fr;
  margin-top: 5%;
  width: 100%;
  height: auto;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: 1fr;
  grid-column-gap: 0px;
  grid-row-gap: 0px;
}
.cardproduct button {
  background-color: transparent;
  border: none;
  cursor: pointer;
}
#editedone {
  width: 20px;
  height: 20px;
}
.closey {
  position: absolute;
  cursor: pointer;
  float: right;
  right: 0;
  margin-right: 3%;
  top: 0;
  margin-top: 3%;
  width: 30px;
  height: 30px;
  padding: 0%;
  outline: none;
  background-color: #f5f5f5;
  border: 0;
  border-radius: 22px;
  padding: 2px;
}
.closey img {
  width: 55%;
  border-radius: 50px;
}
.ProductDetails {
  position: absolute;
  width: 17%;
  /* height: 60%; */
  /* height: 50%; */
  top: 47%;
  left: 14%;
  padding: 0.5em;
  background-color: #ffffff;
  /* -webkit-box-shadow: 10px 11px 10px 0px rgba(0, 0, 0, 0.21);
  box-shadow: 10px 11px 10px 0px rgba(0, 0, 0, 0.21); */
}
.ProductDetails p {
}
.counter {
  position: relative;
  width: 30%;
  height: 40%;
  display: flex;
  margin-top: 5%;
  justify-content: center;
  text-align: center;
  align-items: center;
  background-color: #f5f5f5;
  padding: 5px;
}
.counter button {
  /* height: 100% !important; */
  width: 25%;
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 0%;
  margin: 0%;
  justify-content: center;
  text-align: center;
}
.counter span {
  height: auto;
  width: 50%;
  text-align: center !important;
  justify-content: center;
  margin: 0 auto;
  align-items: center;
  /* padding: 15% 0 !important; */
  box-sizing: border-box;
  font-size: 0.8vw;
}
.ContainerSize1 {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: repeat(2, 1fr);
  grid-column-gap: 0px;
  grid-row-gap: 0px;
  padding: 0%;
  align-items: left;
  /* text-align: left; */
  line-height: 1vw;
}
.ToolMSGAV {
  grid-column: span 2;
  margin: 4%;
  white-space: nowrap;
  font-size: 0.9vw;
  margin-left: 0px;
  text-align: left;
}
.ItemDetailsNumbers {
  width: 50%;
  font-size: 0.8vw;
  white-space: nowrap;
  float: left;
  margin-top: 0%;
  text-align: left;
}
#fontyy {
  font-size: 0.7vw;
  flex-wrap: wrap;
  width: 80%;
  height: 40%;
  color: #878787;
}

.reduce {
  /* display: inline;
  width: 36%; */
  float: left;
}
.increase {
  float: right;
  justify-content: center;
  align-items: center;
  /* display: inline;
  width: 36%; */
}
.CarddFooter {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  height: auto;
  bottom: 0;
  margin-top: 8%;
  margin-bottom: 8%;
  /* padding: 4%; */
  box-sizing: border-box;
}
.CarddFooter p {
  position: absolute;
  margin-left: 65px;
  font-size: 0.8em;
  margin-top: 1.15em;
}
.InfoCardNew {
  display: flex;
  justify-content: left;
  align-items: flex-end;  text-align: left;
  /* width: 50%; */
  width: 60%;
  padding: 0;
  /* font-size: 1vw; */
  font-size: 1.2vw;
}
.EYEButton {
  /* width: 20px;
  height: 20px; */
  width: 0.85vw;
  height: 0.85vw;
  background-color: transparent;
  padding: 0%;
  border: none;
  outline: none;
  margin-left: 7%;
  cursor: pointer;
  /* border-radius: 50%;
  border: 0.5px solid #878787 !important; */
}
.EYEButton img {
  width: 110%;
  margin-top: 10%;
  margin-left: auto;
  margin-right: auto;
}
.InfoCardNew button {
  /* width: 20px;
  height: 20px; */
  /* width: 0.85vw;
  height: 1.175vw; */
  width: 1.1vw;
  height: 1.2vw;
  background-color: transparent;
  padding: 0%;
  border: none;
  outline: none;
  /* margin-left: 6%; */
  margin-left: 9%;
  cursor: pointer;
  /* border-radius: 50%;
  border: 0.5px solid #878787; */
}
.ingredients {
  font-family: Inter;
  color: #A1A1A1 !important;
  font-style: normal;
  font-weight: 400;
  font-size: 0.75vw;
  margin-top: 6%;
}
.InfoCardNew button img {
  /* width: 175%; */
  width: 170%;
  /* margin-top: 10%; */
  /* margin-top: -1px; */
  /* margin-top: -4px; */
  margin-top: -27%;
  margin-left: auto;
  margin-right: auto;
}
.annotationToggle {
  width: 40%;
  height: 100%;
  background-color: black;
  color: #fff;
  padding: 0.2em;
  cursor: pointer;
  position: absolute;
  display: inline;
  border: none;
  left: 0;
  bottom: 0%;
}
.annotationToggle:hover {
  background-color: #fff;
  color: black;
}
.cart {
  width: 27%;
  /* height: 100%; */
  height: 223%;
  background-color: black;
  color: #fff;
  padding: 0.2em;
  cursor: pointer;
  position: absolute;
  display: inline;
  border: none;
  right: 0;
  bottom: 0;
  font-family: Caslon;
  font-size: 1vw;
  margin-right: 4%;
  /* bottom: -78%; */
  bottom: -67%;
}
.cart:hover {
  background-color: #fff;
  color: black;
}
.bottlediv {
  position: absolute;
  width: 16%;
  height: 38%;
  top: 48%;
  left: 60%;
  padding: 0.5em;
  background-color: #ffffff;
  animation: scale-up-ver-center 0.5s cubic-bezier(0.39, 0.575, 0.565, 1) both;
}
.cup {
  position: absolute;
  width: 16%;
  /* height: 32%; */
  top: 16%;
  left: 60%;
  padding: 0.5em;
  background-color: #ffffff;
  animation: scale-up-ver-center 0.5s cubic-bezier(0.39, 0.575, 0.565, 1) both;
}
.Tooldiv {
  position: absolute;
  width: 16%;
  /* height: 32%; */
  top: 50%;
  left: 60%;
  padding: 0.5em;
  background-color: #ffffff;
  animation: scale-up-ver-center 0.5s cubic-bezier(0.39, 0.575, 0.565, 1) both;
}
.SmallBdiv {
  position: absolute;
  width: 16%;
  /* height: 32%; */
  top: 30%;
  left: 60%;
  padding: 0.5em;
  background-color: #ffffff;
  animation: scale-up-ver-center 0.5s cubic-bezier(0.39, 0.575, 0.565, 1) both;
}
.SmallBBodydiv {
  position: absolute;
  width: 16%;
  height: 38%;
  top: 48%;
  left: 60%;
  padding: 0.5em;
  background-color: #ffffff;
  animation: scale-up-ver-center 0.5s cubic-bezier(0.39, 0.575, 0.565, 1) both;
}
.youtubeframe {
  position: absolute;
  height: 100%;
}
.VideoContainer {
  width: 92%;
  height: 100%;
  margin-right: 10% !important;
  margin-top: 4%;
  position: relative !important;
  margin-left: auto;
  margin-right: auto;
  display: flex;
}
.PlayPauseButton {
  width: 25%;
  border: 1px solid black;
  background-color: transparent;
  margin-left: 5%;
  margin-right: 5%;
  cursor: pointer;
}
.MuteUnmuteButton {
  width: 25%;
  border: 1px solid black;
  background-color: transparent;
  margin-left: 5%;
  margin-right: 5%;
  cursor: pointer;
}
.media-controls {
  width: 92% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  text-align: center !important;
  margin-top: 2% !important;
  margin-right: 10% !important;
}
.toolsFlex {
  margin-top: 7%;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}
.toolsCustButtonsDesktop {
  height: 25px;
  width: 25px;
}
@media only screen and (max-width: 600px) {
  .ProductDetails {
    display: none;
    position: absolute;
    width: 42%;
    height: 40%;
    top: 50%;
    left: 2%;
    padding: 0.5em;
  }
  .carddescription {
    margin-top: 8%;
    text-align: left;
    font-weight: normal;
    line-height: 5vw;
    font-size: 3.95vw;
    flex-wrap: nowrap;
    height: 30%;
    width: 100%;
  }
  .carddescription p {
    color: #000;
    margin: 0%;
  }
  .cardtitle {
    width: 100%;
    height: 5%;
    font-weight: normal;
    text-align: left;
    /* text-transform: uppercase; */
    /* font-size: 4vw; */
    font-size: 4.5vw;
    margin-left: auto;
    margin-right: auto;
    margin-top: 1%;
    padding-bottom: 1%;
    border-bottom: 0.5px solid black;
  }
  .cardline {
    font-style: normal;
    text-align: left;
    font-weight: 500;
    /* margin-top: 3%; */
    margin-top: 7%;
    font-size: 4vw;
  }
  .cardproduct {
    display: grid;
    grid-column: 1fr 1fr 1fr;
    margin-top: 8%;
    width: 100%;
    height: auto;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: 1fr;
    grid-column-gap: 0px;
    grid-row-gap: 0px;
  }
  .cardproduct span {
    font-size: 3vw;
  }
  #fontyy {
    font-size: 2.8vw;
    flex-wrap: wrap;
    width: 80%;
    height: 40%;
    color: #878787;
    padding: 8%;
    /* white-space: nowrap; */
    align-items: center;
    text-align: center;
    display: block !important;
  }
  .cart {
    width: 40%;
    height: 10%;
    background-color: black;
    color: #fff;
    padding: 0.2em;
    cursor: pointer;
    position: absolute;
    display: inline;
    border: none;
    right: 0%;
    bottom: 0%;
    margin-bottom: 0.5em;
    margin-right: 10%;
    font-size: 3vw;
    white-space: nowrap;
  }
  .counter {
    /* position: relative;
    width: 80%;
    margin-left: auto;
    margin-right: auto;
    display: block;
    margin-top: 10%;
    background-color: #f5f5f5; */
    display: grid;
    grid-column: 1fr 1fr 1fr;
    margin-top: 10%;
    width: 80%;
    height: auto;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: 1fr;
    grid-column-gap: 0px;
    grid-row-gap: 0px;
    padding: 1%;
    text-align: center;
    justify-content: center;
  }
  .closey {
    position: absolute;
    cursor: pointer;
    float: right;
    right: 0;
    margin-right: 3%;
    top: 0;
    margin-top: 3%;
    width: 22px;
    height: 22px;
    padding: 0%;
    outline: none;
    background-color: #f5f5f5;
    border: 0;
    border-radius: 22px;
  }
  .closey img {
    width: 64%;
    border-radius: 50px;
  }
  .counter button {
    background-color: transparent;
    border: none;
    cursor: pointer;
    padding: 0%;
    margin: 2%;
    justify-content: center;
    text-align: center;
  }
  .cup {
    position: absolute;
    width: 80%;
    /* height: 43%; */
    left: 7%;
    top: 22%;
    background-color: #fff;
    padding: 3%;
    /* border-radius: 10px; */
    z-index: 10;
  }
  .bottlediv {
    position: absolute;
    width: 80%;
    height: 51.7%;
    left: 7%;
    top: 22%;
    background-color: #fff;
    padding: 3%;
    /* border-radius: 10px; */
    z-index: 10;
  }
  .Tooldiv {
    position: absolute;
    width: 80%;
    /* height: 43%; */
    left: 7%;
    top: 22%;
    background-color: #fff;
    padding: 3%;
    /* border-radius: 10px; */
    z-index: 10;
  }
  .SmallBdiv {
    position: absolute;
    width: 80%;
    /* height: 43%; */
    left: 7%;
    top: 22%;
    background-color: #fff;
    padding: 3%;
    /* border-radius: 10px; */
    z-index: 10;
  }
  .SmallBBodydiv {
    position: absolute;
    width: 80%;
    height: 51.7%;
    left: 7%;
    top: 22%;
    background-color: #fff;
    padding: 3%;
    /* border-radius: 10px; */
    z-index: 10;
  }
  .ingredients {
    font-family: Inter;
    color: #A1A1A1 !important;
    font-style: normal;
    font-weight: 400;
    margin-top: 6%;
    width: 100%;
    font-size: 3.5vw;
    margin-top: 7% !important;
  }
  .ingredientsTool {
    font-family: "Caslon !important";
    color: #000 !important;
    font-style: italic;
    font-weight: 400;
    margin-top: 6%;
    width: 100%;
    font-size: 3.5vw;
    margin-top: 7% !important;
  }
  .ingredientsMid{
    font-family: "CaslonBold !important";
    color: "#000";
    font-style: normal;
    font-weight: 400;
    margin-top: 6%;
    width: 100%;
    font-size: 3.5vw;
  }
  .ingredientsBot{
    font-family: Inter;
    color: #A1A1A1 !important;
    font-style: normal;
    font-weight: 400;
    margin-top: 6%;
    width: 100%;
    font-size: 3.5vw;
  }
}
.switch {
  position: absolute;
  display: inline-block;
  width: 60px;
  height: 25px;
  bottom: 0;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.3s;
  border-radius: 34px;
  height: fit-content;
}

.slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 4px;
  bottom: 3px;
  background-color: white;
  -webkit-transition: 0.4s;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #000;
}

input:focus + .slider {
  box-shadow: 0 0 1px #000;
}

input:checked + .slider:before {
  -webkit-transform: translateX(28px);
  -ms-transform: translateX(28px);
  transform: translateX(32px);
}
/* .InfoMessage {
  display: flex;
  width: 30%;
  align-items: center;
  text-align: center;
  justify-content: center;
  font-size: 0.8em;
  margin-left: 2%;
  white-space: nowrap;
} */

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes fadeOut {
  0% { opacity: 1; }
  100% { opacity: 0; }
}

.AnnotationsMobile {
  position: absolute;
  width: 35px;
  height: 35px;
  border: none;
  top: 23%;
  right: 7.5%;
}
.annotationEye{
  width: 35px;
  height: 35px;
  border: none;
}
.ToolCustomisation {
  /* display: flex;
  flex-direction: column;
  position: absolute;
  width: 50px;
  height: 101px;
  border: none;
  top: 68%;
  left: 4%;
  z-index: 2; */
  display: flex;
  flex-direction: row;
  position: absolute;
  width: 115px;
  height: 60px;
  border: none;
  top: 72.5%;
  left: calc(50% - 57.5px);
  z-index: 1;
  /* background-color: whitesmoke; */
  justify-content: space-around;
  align-items: center;
}
.toolsCustButtons {
  width: 40px;
  height: 40px;
  border: none;
}
.ProductDetailsMobile {
  position: absolute;
  width: 35px;
  height: 35px;
  border: none;
  top: 23%;
  left: 7%;
}
.ProductDetailsContentMobile{
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0%;
  top: 0%;
  background-color: rgba(150, 150, 150, 0.7);
  z-index: 5;
}
.AnnotationsBackground{
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0%;
  top: 0%;
  background-color: rgba(150, 150, 150, 0.7);
  z-index: 5;
}
.cardProductDetails{
  position: absolute;
  width: 74%;
  /* height: 45%; */
  left: 7%;
  top: 22%;
  background-color: #fff;
  padding: 6%;
  /* border-radius: 10px; */
}
.AnnotationsContentMobile{
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0%;
  top: 0%;
  background-color: rgba(150, 150, 150, 0.7);
  z-index: 5;
}