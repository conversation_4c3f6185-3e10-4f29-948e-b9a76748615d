import React, { Component } from "react";
import "./NewMobileManu.css";
import { slide as <PERSON>u } from "react-burger-menu";
import FAQCard from "./FAQCard";
import PressCard from "./PressCard";
import ReturnsCard from "./ReturnsCard";

class NewMobileManu extends Component {
  constructor(props) {
    super(props);
  }

  state = {
    ManuDisplay: "none",
    ShopImageSRC: "shoppingBag.png",
    isOpen: false,
    ManuAnimation: "fadeIn 0.5s",
    FAQDisplay: "none",
    FAQAnimation: "fadeIn 0.5s",
    pressDisplay: "none",
    pressAnimation: "fadeIn 0.5s",
    returnsDisplay: "none"
  };

  FAQSwitch = () => {
    if (this.state.FAQDisplay === "none") {
      this.setState({FAQAnimation: "fadeIn 0.5s"});
        setTimeout(() => {
          this.setState({
            FAQDisplay: "",
          });
        }, 500);
    } else {
      this.setState({FAQAnimation: "fadeOut 0.5s"})
      setTimeout(() => {
        this.setState({ FAQDisplay: "none" });
      }, 500);
    }
  }

  pressSwitch = () => {
    if (this.state.pressDisplay === "none") {
      this.setState({pressAnimation: "fadeIn 0.5s"});
        setTimeout(() => {
          this.setState({
            pressDisplay: "",
          });
        }, 500);
    } else {
      this.setState({pressAnimation: "fadeOut 0.5s"})
      setTimeout(() => {
        this.setState({ pressDisplay: "none" });
      }, 500);
    }
  }

  returnsSwitch = () =>{
    if (this.state.returnsDisplay === "none") {
      this.setState({pressAnimation: "fadeIn 0.5s"});
        setTimeout(() => {
          this.setState({
            returnsDisplay: "",
          });
        }, 500);
    } else {
      this.setState({pressAnimation: "fadeOut 0.5s"})
      setTimeout(() => {
        this.setState({ returnsDisplay: "none" });
      }, 500);
    }
  }

  hide3D = () => {
    let event = new CustomEvent("hide3D", {
    });
    window.dispatchEvent(event);
  };

  unhide3D = () => {
    let event = new CustomEvent("unhide3D", {
    });
    window.dispatchEvent(event);
  };

  showBodyLines = () => {
    let event = new CustomEvent("showBodyLines", {
    });
    window.dispatchEvent(event);
  };

  closeManu = () => {
    this.props.AboutPageDisplaySwitchMobile("close");
    this.props.ShopPageDisplaySwitch("close");
    this.FragDisplaySwitchMobile();
  }

  handleIsOpen = () => {
    // console.log("Show Menu")
    this.hide3D();
    this.props.ContactPageDisplaySwitch("close");
    this.setState({
      ManuAnimation: "fadeIn 0.5s",
      ManuDisplay: ""
    });
    setTimeout(() => {
      this.props.AboutPageDisplaySwitchMobile("close");
      this.props.ShopPageDisplaySwitch("close");
    }, 500);
    // setTimeout(() => {
    //   this.setState({ManuDisplay: ""});
    // }, 500);
    // console.log(this.state.ManuDisplay)
  };

  AboutPageDisplaySwitchMobile = () => {
    this.props.AboutPageDisplaySwitchMobile("open");
    this.setState({ManuAnimation: "fadeOut 0.5s" });
    setTimeout(() => {
      this.setState({ManuDisplay: "none"});
    }, 500);
  }
  ContactPageDisplaySwitchMobile = () => {
    this.props.ContactPageDisplaySwitch("open");
    this.setState({ManuAnimation: "fadeOut 0.5s" });
    setTimeout(() => {
      this.setState({ManuDisplay: "none"});
    }, 500);
  }
  animateOut = () => {
    let event = new CustomEvent("animateOut", {
    });
    this.unhide3D();
    this.setState({ManuAnimation: "fadeOut 0.5s" });
    setTimeout(() => {
      this.setState({ManuDisplay: "none"});
      window.dispatchEvent(event);
    }, 500);
  }
  SHopPageDisplaySwitchMobile = () => {
    // this.showBodyLines();
    this.unhide3D();
    this.props.ShopPageDisplaySwitch("open");
    this.setState({ManuAnimation: "fadeOut 0.5s" });
    setTimeout(() => {
      this.setState({ManuDisplay: "none"});
    }, 500);
  }
  FragDisplaySwitchMobile = () => {
    this.unhide3D();
    this.props.StartAnimation();
    this.setState({ManuAnimation: "fadeOut 0.5s" });
    setTimeout(() => {
      this.setState({ManuDisplay: "none"});
    }, 500);
  }

  componentDidMount() {
    window.addEventListener("closeMenu", this.closeManu);
  }
  render() {
    return (
      <>
        {/* <div className="BurgerIconManu">
          <button onClick={() => this.setState({ ManuDisplay: "" })}><img src="burger-bar.png" /></button>
        </div> */}
        <div style={{
          position: "fixed",
          width: "40px",
          height: "32px",
          left: "4px",
          top: "4px"}}
          onClick={() => this.handleIsOpen()}
          >
        </div>
        <Menu
            outerContainerId={"outer-container"}
            isOpen={this.state.isOpen}
            onOpen={this.handleIsOpen}
            onClose={this.handleIsOpen}
          ></Menu>
        <div className="MobileManuContent" style={{ height: "5.5%", top: "0%", display: this.state.ManuDisplay, animation: this.state.ManuAnimation }}></div>
        <div className="MobileManuContent" style={{ display: this.state.ManuDisplay, animation: this.state.ManuAnimation }}>
          {/* <div className="IconDiv">
            <img src="Logo Transparent.png" />
          </div> */}
          <div className="ListManuDiv">
            <button className="ListItm" onClick={() => this.FragDisplaySwitchMobile()}>fragrances</button>
            <button className="ListItm" onClick={() => this.AboutPageDisplaySwitchMobile()}>about</button>
            <button className="ListItm" onClick={() => this.SHopPageDisplaySwitchMobile()}><div className="CartMCon">
              <div className="CartsecP">cart</div>
              <div className="CartsecP second">
                <img src={this.state.ShopImageSRC} />
                {this.props.totalitems < 10 ? (
                  <span className="newControlBagSpanLessThan10">
                    {this.props.totalitems}
                  </span>
                ) : (
                  <span className="newControlBagSpanGreaterThan10">
                    {this.props.totalitems}
                  </span>
                )}
              </div>
            </div>
            </button>
            <button className="ListItm" onClick={() => this.ContactPageDisplaySwitchMobile()}>contact</button>
            <a className="ListItm" href="https://www.instagram.com/incorp.world/"
              target="_blank">follow</a>
          </div>
          <div className="SpiniconDiv">
            <div className="SpinContainer" onClick={() => this.animateOut()}> <img src="logobottom.png" /></div>
          </div>
          <div className="bottomManu">
            <div className="btmline" onClick={() => this.pressSwitch()}>
              Press
            </div>
            <div className="btmline" onClick={() => this.returnsSwitch()}>
              Returns
            </div>
            <div className="btmline">
              <div className="faqdiv" onClick={() => this.FAQSwitch()}>
                FAQ
              </div>
              <div className="devdiv" style={{textAlign: "right"}}>
              <a href="https://www.modularcx.co.uk/" target="_blank" style={{color: "#000"}}>
                Dev by ModularCX{" "}
              </a>{" "}
              </div>
            </div>
            <div className="btmline">
              <div className="devdiv">
                <img src="copyright.png" /> INCORP 2022
              </div>
              <div className="webdiv" style={{textAlign: "right"}}>
              <a href="https://aurelienmabilat.com/" target="_blank" style={{color: "#000"}}>
                Website by Aurélien Mabilat{" "}
              </a>{" "}
              </div>
            </div>
          </div>
        </div>
        <FAQCard FAQDisplay={this.state.FAQDisplay} FAQAnimation={this.state.FAQAnimation} FAQSwitch={this.FAQSwitch}/>
        <PressCard pressDisplay={this.state.pressDisplay} pressAnimation={this.state.pressAnimation} pressSwitch={this.pressSwitch}/>
        <ReturnsCard returnsDisplay={this.state.returnsDisplay} pressAnimation={this.state.pressAnimation} returnsSwitch={this.returnsSwitch}/>
      </>
    );
  }
}

export default NewMobileManu;
