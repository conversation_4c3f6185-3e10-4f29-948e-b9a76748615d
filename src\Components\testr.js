await SceneLoader.AppendAsync("", "./Hebox_V3.glb", this.scene);

 
let product3 = this.scene.getMeshByName("__root__");
product3.name="product3"
product3.setEnabled(false)
var body3=this.scene.getMeshByName("body_B")
body3.name="body3"
var cover3=this.scene.getMeshByName("lid_B")
cover3.name="cover3"
var liquid3=this.scene.getMeshByName("liquid_B")
liquid3.name="liquid3"
var helix3=this.scene.getMeshByName("helix_B")
helix3.name="helix3"

var liq3=liquid3.material
var heli3=helix3.material

body3.visibility=0;
cover3.visibility=0;
helix3.visibility=0;
liquid3.visibility=0


product3.name = "product3";
product3.scaling.x = 0.5;
product3.scaling.y = 0.5;
product3.scaling.z = 0.5;
product3.rotation = new Vector3(Tools.ToRadians(-90), Tools.ToRadians(350.8), Tools.ToRadians(0));

product3.position=new Vector3(0.511, 1.686,1.037);

this.scene.getMaterialByName("Body").name = "productBody3";
let productBody3 = this.scene.getMaterialByName("productBody3");
productBody3.albedoColor = new Color3.FromInts(255, 255, 255);
productBody3.metallic = 0.17;
productBody3.emissiveColor = new Color3.FromInts(211, 211,211).toLinearSpace();
productBody3.clearCoat.isEnabled = true;
productBody3.roughness=0.33
productBody3.clearCoat.roughness=0
productBody3.clearCoat.indexOfRefraction=1.6
productBody3.backFaceCulling = true;
productBody3.metallicF0Factor=1;
productBody3.emissiveTexture = new Texture("Hebox_Refraction.jpg", this.scene, false, false);
productBody3.enableSpecularAntiAliasing = true;

cover3.material.albedoColor = new Color3.FromInts(255,255, 255);
cover3.material.albedoTexture = new Texture(
  "Cover_Albedo.jpg",
  this.scene,
  false,
  false
);
cover3.material.metallic=1
cover3.material.roughness=0.22
cover3.material.metallicF0Factor=1
cover3.material.backFaceCulling=true
cover3.material.enableSpecularAntiAliasing = true;

heli3.albedoColor=new Color3.FromInts(219, 207, 12).toLinearSpace();
heli3.metallic=0
heli3.roughness=0
heli3.metallicF0Factor=1
heli3.backFaceCulling=true

liq3.albedoColor=new Color3.FromInts(255, 255, 255).toLinearSpace();
liq3.albedoColor = new Color3(1, 1 ,1).toLinearSpace();
liq3.clearCoat.isEnabled = true;
liq3.roughness=0.22
liq3.clearCoat.roughness=0
liq3.clearCoat.indexOfRefraction=1.5
liq3.clearCoat.isTintEnabled = true;
liq3.clearCoat.tintColor = new Color3.FromInts(207, 203, 198).toLinearSpace();
liq3.backFaceCulling = true;
liq3.metallicF0Factor=1;
liq3.enableSpecularAntiAliasing = true;



var refractionTexture6 = new RefractionTexture("th7", 1024, this.scene);
refractionTexture6.renderList.push(liquid3)
refractionTexture6.renderList.push(helix3)

refractionTexture6.refractionPlane = new Plane(0,0, 0, 0);


body3.material.diffuseColor = new Color3(1, 1, 1);
body3.material.refractionTexture = refractionTexture6;

liquid3.material.albedoColor = new Color3(0.945, 0.757, 0.502).toLinearSpace();

var refractionTexture7 = new RefractionTexture("th8", 1024, this.scene);
refractionTexture7.renderList.push(cover3);

refractionTexture7.refractionPlane = new Plane(0,0, 0, 0);


liquid3.material.diffuseColor = new Color3(1, 1, 1);
liquid3.material.refractionTexture = refractionTexture7;

body3.material.indexOfRefraction =1.06;
refractionTexture6.depth =1.2 ;

liquid3.material.indexOfRefraction =1.03;
refractionTexture7.depth = 1.2;

var that=this

    for(var i=0;i<product3._children.length;i++){

  product3._children[i].isPickable = true;
  product3._children[i].actionManager = new ActionManager(that.scene);
  product3._children[i].actionManager.registerAction(
    new ExecuteCodeAction(ActionManager.OnPickTrigger, function(
      mesh
    ) {
      // console.log(mesh.source, "firstModel");
             that.props.ProductDisplayFunction()          

    })
  );
}