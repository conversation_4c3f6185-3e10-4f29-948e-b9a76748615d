import React, { Component } from "react";
import { slide as <PERSON>u } from "react-burger-menu";
import "./MobileManu.css";

class MobileManu extends Component {
  constructor(props) {
    super(props);
  }
  showSettings(event) {
    event.preventDefault();
  }
  state = {
    ShopImageSRC: "picto-shoppingBag.svg",
    isOpen: false,
  };

  handleIsOpen = () => {
    this.setState((prevState) => ({
      isOpen: !prevState.isOpen,
    }));
  };

  closeSideBar = () => {
    this.setState({ isOpen: false });
  };
  Changerimg = (target) => {
    if (target == "white") {
      this.setState({ ShopImageSRC: "picto-shoppingBag2.svg" });
    } else if (target == "black") {
      this.setState({ ShopImageSRC: "picto-shoppingBag.svg" });
    }
  };
  AboutPageDisplaySwitchMobile = () =>{
    this.props.AboutPageDisplaySwitch("open");
    this.closeSideBar();
  }
 
  ContactPageDisplaySwitchMobile = () => {
    this.props.ContactPageDisplaySwitch("open");
    this.closeSideBar();
  }
  render() {
    return (
      <>
        <div id="outer-container">
          <Menu
            outerContainerId={"outer-container"}
            isOpen={this.state.isOpen}
            onOpen={this.handleIsOpen}
            onClose={this.handleIsOpen}
          >
            <a
              id="ABOUT"
              className="menu-item"
              onClick={() => this.AboutPageDisplaySwitchMobile()}
            >
              ABOUT
            </a>
            <a
              id="CONTACT"
              className="menu-item"
              onClick={() => this.ContactPageDisplaySwitchMobile()}
            >
              CONTACT
            </a>
            <a
              id="FOLLOW"
              className="menu-item"
              href="https://www.instagram.com/incorp.world/"
              target="_blank"
            >
              FOLLOW
            </a>
          </Menu>
        </div>
        <button
          style={{ display: "" }}
          className="Controls FollowMobile"
          onClick={() => this.props.ShopPageDisplaySwitch("open")}
        >
          <a style={{ height: "14px" }}>FOLLOW</a>
        </button>
      </>
    );
  }
}

export default MobileManu;
