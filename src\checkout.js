import gql from "graphql-tag";
import { useEffect } from "react";

const CartFragment = gql`
  fragment CartFragment on Cart {
    id
    checkoutUrl
    totalQuantity
    cost {
      totalAmount {
        amount
        currencyCode
      }
      subtotalAmount {
        amount
        currencyCode
      }
      totalTaxAmount {
        amount
        currencyCode
      }
    }
    lines(first: 250) {
      edges {
        node {
          id
          quantity
          merchandise {
            ... on ProductVariant {
              id
              title
              image {
                url
              }
              price {
                amount
                currencyCode
              }
              product {
                title
              }
            }
          }
        }
      }
    }
  }
`;

export const GET_PRODUCTS = gql`
  query getProducts {
    products(first: 10) {
      edges {
        node {
          id
          title
          images(first: 10) {
            edges {
              node {
                id
                altText
              }
            }
          }
        }
      }
    }
  }
`;

export const GET_CART = gql`
  query getCart($cartId: ID!) {
    cart(id: $cartId) {
      ...CartFragment
    }
  }
  ${CartFragment}
`;

export const createCart = gql`
  mutation cartCreate($input: CartInput!) {
    cartCreate(input: $input) {
      userErrors {
        message
        field
      }
      cart {
        ...CartFragment
      }
    }
  }
  ${CartFragment}
`;

export const cartLinesAdd = gql`
  mutation cartLinesAdd($cartId: ID!, $lines: [CartLineInput!]!) {
    cartLinesAdd(cartId: $cartId, lines: $lines) {
      userErrors {
        message
        field
      }
      cart {
        ...CartFragment
      }
    }
  }
  ${CartFragment}
`;

export const cartLinesUpdate = gql`
  mutation cartLinesUpdate($cartId: ID!, $lines: [CartLineUpdateInput!]!) {
    cartLinesUpdate(cartId: $cartId, lines: $lines) {
      userErrors {
        message
        field
      }
      cart {
        ...CartFragment
      }
    }
  }
  ${CartFragment}
`;

export const cartLinesRemove = gql`
  mutation cartLinesRemove($cartId: ID!, $lineIds: [ID!]!) {
    cartLinesRemove(cartId: $cartId, lineIds: $lineIds) {
      userErrors {
        message
        field
      }
      cart {
        ...CartFragment
      }
    }
  }
  ${CartFragment}
`;

export const cartBuyerIdentityUpdate = gql`
  mutation cartBuyerIdentityUpdate(
    $cartId: ID!
    $buyerIdentity: CartBuyerIdentityInput!
  ) {
    cartBuyerIdentityUpdate(cartId: $cartId, buyerIdentity: $buyerIdentity) {
      userErrors {
        field
        message
      }
      cart {
        ...CartFragment
      }
    }
  }
  ${CartFragment}
`;

export function useCartEffect(data, key, setDataCallback) {
  useEffect(() => {
    if (data && data[key] && data[key].cart) {
      setDataCallback(data[key].cart);
    }
  }, [data]);
}

// Legacy exports for backward compatibility during migration
export const createCheckout = createCart;
export const checkoutLineItemsAdd = cartLinesAdd;
export const checkoutLineItemsUpdate = cartLinesUpdate;
export const checkoutLineItemsRemove = cartLinesRemove;
export const checkoutCustomerAssociate = cartBuyerIdentityUpdate;
export const useCheckoutEffect = useCartEffect;
