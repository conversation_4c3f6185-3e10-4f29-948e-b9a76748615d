.MobileProductDetails {
  position: absolute;
  width: -webkit-fill-available;
  bottom: 0%;
  height: 28%;
  display: grid;
  justify-content: center;
  justify-items: center;
  grid-template-columns: 0fr;
  grid-template-rows: repeat(3, 1fr);
  grid-column-gap: 0px;
  grid-row-gap: 5px;
  box-sizing: border-box;
}
.MobileProductDetailsTitle {
  height: fit-content;
  position: relative;
  width: auto;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
  font-size: 1em;
  /* font-style: italic; */
  font-weight: 500;
  /* background-color: rgba(240, 240, 240, 1); */
  /* background-color: whitesmoke; */
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  white-space: normal;
  padding: 1%;
  /* margin-top: -92px; */
  /* margin-top: -45px; */
  margin-top: -73px;
  line-height: 20px;
}
.MobileThreeButtonsSwitcher {
  position: relative;
  width: 70%;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
  font-style: italic;
  /* background-color: rgb(252, 248, 240); */
  background-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
  color: black;
  margin-top: -40px;
}
.MobileThreeButtonsSwitcher button {
  background-color: #f5f5f5;
  font-weight: 200;
  font-size: 1em;
  width: 48px;
  padding: 0px;
  margin-left: 2%;
  margin-right: 2%;
  height: 48px;
  border: none;
  cursor: pointer;
  font-family: caslon;
  color: black;
  /* box-shadow: 0px 2px 0px 0px rgba(189, 189, 189, 1);
  -webkit-box-shadow: 0px 2px 0px 0px rgba(189, 189, 189, 1);
  -moz-box-shadow: 0px 2px 0px 0px rgba(189, 189, 189, 1); */
}
.MobileThreeButtonsSwitcher button:hover {
  background-color: #fff;
}
.MobileThreeButtonsSwitcher button:active {
  background-color: #fff;
}
.MobileFooterContainer {
  background-color: #fff;
  display: flex;
  width: 100vw;
  flex-direction: row;
}
.MobileProductCounterContainer {
  display: grid;
  /* grid-template-columns: repeat(4, 1fr); */
  grid-template-columns: auto auto auto auto;
  /* grid-template-rows: 1fr; */
  grid-column-gap: 4px;
  /* grid-row-gap: 6px; */
  grid-row-gap: 4px;
  width: 100%;
  height: 100%;
  background-color: #fff;
  position: relative;
  margin-left: auto;
  font-size: 0.8em;
  align-items: center;
  margin-right: auto;
  /* padding: 8px; */
  padding: 4px;
  z-index: 2;
  box-sizing: border-box;
  white-space: nowrap;
  text-align: left;
}
.MobileProductCounterContainerV2{
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: #fff;
  position: relative;
  margin-left: auto;
  font-size: 0.8em;
  align-items: center;
  margin-right: auto;
  /* padding: 8px; */
  /* padding: 4px 20px;s */
  padding: 4px 5.5vw;
  z-index: 2;
  box-sizing: border-box;
  white-space: nowrap;
  text-align: left;
}
.MobileProductCounterContainerV2Row1{
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  width: 100%;
  height: 100%;
  background-color: #fff;
  position: relative;
  margin-left: auto;
  /* font-size: 0.8em; */
  font-size: 1em;
  align-items: center;
  margin-right: auto;
  /* padding: 8px; */
  padding: 4px;
  z-index: 2;
  box-sizing: border-box;
  white-space: nowrap;
  text-align: left;
}
.MobileProductCounterContainerV2Row2{
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
  background-color: #fff;
  position: relative;
  margin-left: auto;
  /* font-size: 0.8em; */
  font-size: 1em;
  align-items: center;
  margin-right: auto;
  /* padding: 8px; */
  padding: 4px;
  z-index: 2;
  box-sizing: border-box;
  white-space: nowrap;
  text-align: left;
}
.MobileCounterProduct {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: 1fr;
  grid-column-gap: 1px;
  grid-row-gap: 0px;
  /* width: 45px; */
  width: 50px;
  background-color: #f5f5f5;
  position: relative;
  margin-left: 4px;
  align-items: center;
  text-align: center;
  /* height: 25px; */
  height: 30px;
  padding: 1px;
  /* height: 100%; */
  color: black;
  font-family: 'Inter';
}

.MobileCounterProduct button {
  background-color: transparent;
  outline: none;
  border: none;
  height: 100%;
  color: black;
  padding-left: 3px;
  padding-right: 3px;
  font-size: 1.2em;
}
.MobileCounterProduct span {
  width: 1fr;
  margin-left: auto;
  margin-left: 3px;
  margin-right: auto;
  /* margin-top: 3px; */
  text-align: center;
  /* font-size: 0.8em; */
  font-size: 1.1em;
  font-weight: bolder;
}
.MobileBD {
  color: black;
  margin-bottom: 2px;
}
.MobileBI {
  color: black;
  margin-bottom: 2px;
}
.MobileAddCart {
  margin-left: 4px;
  width: 100px;
}
.MobileAddCart button {
  width: 100%;
  height: 100%;
  /* padding: 10%; */
  padding: 23%;
  background-color: black;
  color: #fff;
  border: none;
  outline: none;
  font-size: 0.9em;
  box-sizing: border-box;
  font-family: Caslon !important;
}
