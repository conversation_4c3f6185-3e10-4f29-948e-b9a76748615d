# CI/CD Pipeline of project

## What does the pipeline do?

The frontend pipeline builds, deploys a staging and deployment project and hosts it on the internet.

## How to use the pipeline?

### Triggering the staging pipeline

When a developer has finished their updates and would like to deploy and receive a link , they can run the staging pipeline the following way:

1. Go to [Github](https://github.com) and select the repository
2. Select "Pull Requests" tab
3. Create a new pull request, this pull request should merge with the base "staging-branch" branch

After the merge has been closed, the pipeline should run ( check it out in the Actions page by clicking on the "Actions" tab in the repository), it will generate a link for the
staging app inside the action.
_This pipeline follows the "on: pull-request" strategy for being triggered._

### Getting the Staging link

To get the link for your app being staged, please do the following:

1. Click on the "Actions" tab of the repository in [Github](https://github.com)
2. Select the latest workflow run ( usually is named after the commit message )
3. Click on "amplify-deployment" job ( either on the left side dropdown menu or on the actions' wireframe)
4. Open the dropdown menu called "Deployment of project on AWS Amplify"
5. At the end of this action step, a link should be displayed (e.g: https://exampled3nizd25cqhpaf.amplifyapp.com)

### Triggering the production pipeline

Unlike the staging pipeline, the production pipeline can be optionally deployed via a pull request to "production-branch", or manually through github>actions>PRODUCTION TRIGGER. **Consult** with Youssef Jrab and Mostafa Salloum before triggering the production pipeline.

To Trigger the pipeline manually, please follow these steps:

1. Click on the "Actions" tab of the repository in [Github](https://github.com)
2. On the left-hand side of the page, there is an Action called "PRODUCTION TRIGGER", press it
3. Click on the drop-down box "Run workflow"
4. Choose "Use workflow from: staging-branch"
5. Click on "Run workflow"

After following these steps, the pipeline should be running.

_This pipeline follows the "on: workflow_dispatch" strategy for being triggered._

### Getting the Production link

Follow the same steps of the staging link with these changes in the steps:

1. the workflow run to select is always called "PRODUCTION TRIGGER"

## Miscellaneous changes to the pipeline

### Changing subdomain of project

If you desire to change the subdomain for the production pipeline, please follow these steps:

#### From Github

1. Open [Github](https://github.com)
2. Open the repository
3. Navigate to "Settings"
4. Open the "Secrets and variables" Dropdown underneath "Security" section, followed by Actions
5. Open "Variables"
6. Edit the "AWS_AMPLIFY_PRODUCTION_SUBDOMAIN"
7. Add a value to your liking

#### From VSCode

1. Download an extension called Github Actions
2. Open the tab for the extension
3. On the bottom of the Extension's Panel in Setting, Open the "Variables" dropdown box
4. Click on Edit for "AWS_AMPLIFY_PRODUCTION_SUBDOMAIN"
5. Add a value to your liking

#### From gh cli on your terminal ( not available on al OSes )

1. In this repository, open a file called .ghvariables.env
2. Change the value of "AWS_AMPLIFY_PRODUCTION_SUBDOMAIN" to your liking
3. Run the following command to update the variables: `gh variable set -f ./.ghvariables.env`

N.B: the variables and secrets are stored on the repository-level, so they are global to the project no matter what branch you're on.

## Important remarks concerning the pipeline

- You **CANNOT** run the deployment branch if the staging branch has not been triggered before. To follow industry best practices, we stage each update, test the project, and only then we deploy project to our domain.
- If the pipeline fails, you usually receive an email from github informing you so, or
  you can check it manually in the "Actions" Tab, the workflow would have an "X" in red next to it if a failure occured.
  In such scenario, please inform Jad Beydoun so they can fix the pipeline
- Please be patient for the staging pipeline's build step, sometimes building the project can take a couple of minutes.
- If you would like to rollback the deployed production project to a previous version, please inform Jad Beydoun to do so ( future update for the pipeline may allow this to be done more easily ).

## For the curious ones

### What is happening under the hood ?

The staging and production pipeline deploy the projects on AWS, the services in use are CLI, IAM, S3, Amplify and Route 53.

- CLI for running commands in the pipeline
- IAM for giving the necessary policy for the github action to work ( we are following the STS Role assumption to follow industry best-practices )
- S3 where our deployed built projects reside
- Amplify for simple hosting of the built projects and ensuring they are served globally with Cloudfront as CDN under the hood
- Route 53 for creating and association of our projects to a domain that we own

This pipeline relies heavily on shell scripts and AWS CLI commands to work on github action servers.

## CI/CD Pipeline Members

[Jad Beydoun](https://github.com/MJayybee21)
