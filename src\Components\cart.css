.CartContainer {
  position: relative;
  width: 100%;
  height: 100%;
  margin-top: 0%;
}
.CartHeader {
  /* width: 100%; */
  width: 85%;
  text-transform: capitalize;
  /* font-size: 1.4vw; */
  font-size: 1.1vw;
  text-align: left;
  /* margin-top: 2%; */
  border-bottom: 0.5px solid black;
  padding-bottom: 2.5%;
}
.RemoveCartItem{
  width: 20px;
  height: 20px;
  margin-right: 1%;
  /* background-color: #f5f5f5; */
  /* background-color: #000; */
  background-color: #fff;
  border-radius: 50%;
  border: none;
  /* padding: 1%; */
  padding: 2px;
  cursor: pointer;
}
.RemoveCartItem img{
  width: 65%;
}
.CloseCart {
  width: 25px;
  height: 25px;
  position: absolute;
  right: 0;
  margin-right: 1%;
  /* background-color: #f5f5f5; */
  background-color: #000;
  border-radius: 50%;
  border: none;
  /* padding: 1%; */
  padding: 5px;
  top: -5px;
  cursor: pointer;
}
.CloseCart img {
  width: 60%;
}
.CartBody {
  width: 100%;
}
.CartFooter {
  width: 100%;
  height: 6%;
  position: absolute;
  bottom: 5%;
}
.CartItems {
  height: auto;
  /* max-height: 40vh; */
  /* max-height: 32vh; */
  overflow-y: scroll;
  /* width: 100%; */
  margin-top: 4%;
  margin-bottom: 2%;
  padding: 2%;
}
.CartItems ul {
  margin-block-start: 0em;
  margin-block-end: 0em;
  padding-inline-start: 0%;
  list-style-type: none;
  text-decoration: none;
}
.CartDetails {
  position: relative;
  height: auto;
  width: 100%;
  height: auto;
  text-align: left;
  /* padding-left: 0.5%; */
  padding-left: 0%;
  font-family: 'Inter';
}
.CartDetailsHead {
  margin-top: 4%;
  margin-bottom: 5%;
}
.CartDetailsList {
  position: relative;
  margin-top: 2%;
  margin-bottom: 2%;
  height: 100%;
  border-top: 1px solid #d9d9d9;
  border-bottom: 1px solid #d9d9d9;
  font-family: 'Inter';
}
.CartDetailsList ul {
  margin-block-start: 0.01em;
  margin-block-end: 0.01em;
  padding-inline-start: 0%;
  list-style-type: none;
  text-decoration: none;
}
.CartDetailsList ul li {
  list-style-type: none;
  text-decoration: none;
  margin-top: 4%;
  margin-bottom: 4%;
  color: #454545;
}
.CartDetailsList ul li p {
  font-size: 0.6vw;
  color: #a1a1a1;
  padding: 0%;
  margin: 0%;
  margin-top: 1%;
}
.CartDetailsList span {
  float: right;
  /* margin-right: 5%; */
}
.CartCheckout {
  border: none;
  width: 100%;
  height: 100%;
  background-color: black;
  color: white;
  cursor: pointer;
  text-transform: uppercase;
  font-size: 0.9vw;
}
.CartCheckout:hover {
  background-color: white;
  color: black;
  border: 1px solid black;
}
#dashed {
  padding-top: 5%;
  border-top: 2px dashed #edf1f4;
  margin-top: 50%;
}
.CartEditedSection {
  padding-top: 0%;
  /* border-top: 2px dashed #edf1f4; */
  /* border-top: 1px solid #d9d9d9; */
  position: absolute;
  bottom: 12%;
  font-family: 'Inter';
}
.TotalSection {
  display: flex;
  margin: 5%;
  margin-left: 0%;
  margin-right: 0%;
  margin-bottom: 8%;
  justify-content: space-between;
  align-items: center;
}
.totalEdited {
  width: 30%;
  text-align: left;
}
.CartBodyMessage {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  background-color: #d9d9d9;
  color: black;
  font-weight: 500;
  text-align: left;
  font-size: 0.8vw;
  line-height: 1.1vw;
  padding-top: 2%;
  /* padding-bottom: 8%;
  padding-right: 4%;
  padding-left: 4%;
  margin-top: 2%; */
  font-family: Caslon;
  box-sizing: border-box;
  padding: 5%;
  font-family: 'Inter';
}
.CartBodyMessage span {
  position: absolute;
  right: 0;
  margin-right: 13%;
  margin-top: 3%;
}
.CartBodyMessage input[type="checkbox"] {
  position: absolute;
  right: 0;
  margin-right: 6%;
  margin-top: 3%;
  cursor: pointer;
}
.CartBodyMessage input[type="checkbox"]:checked {
}
.CartOpenSection {
  width: 100%;
  margin-top: 2%;
  height: auto;
  position: relative;
}
.Policy {
  position: relative;
  height: 50%;
  width: 100%;
  padding-top: 2%;
  padding-bottom: 2%;
}
.Policy p {
  font-weight: 500;
    text-align: left;
    font-size: 0.8vw;
    line-height: 1.1vw;
    padding: 2.5%;
  overflow-y: scroll;
  margin-block-start: 0.5em;
    margin-block-end: 0.5em;
    font-family: 'Inter';
}
.Policy button {
  cursor: pointer;
  width: 100%;
  height: 3vh;
  font-size: 0.7vw;
  background-color: transparent;
  color: #454545;
  text-align: left;
  padding-top: 0.5%;
  padding-bottom: 0.5%;
  border: none;
  font-family: 'Inter';
}
.Policy button img {
  width: 4%;
  float: right;
  margin-right: 4%;
}
.CartList {
  width: 100%;
  display: flex;
  margin-left: auto;
  margin-right: auto;
  height: 10vh;
  margin-top: 5%;
  margin-bottom: 5%;
  /* display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: 1fr;
  grid-column-gap: 0px;
  grid-row-gap: 0px; */
}
.CartList ul {
  display: inline;
  margin-block-start: 0.01em;
  margin-block-end: 0.01em;
  padding-inline-start: 6%;
  list-style-type: none;
  text-decoration: none;
}
.ItemImage {
  margin-top: 4%;
  width: 17%;
}
.ItemImage img {
  width: 100%;
}
.ItemDescription {
  display: inline;
  width: 50%;
  text-align: left;
  padding-left: 8%;
}
.ItemDescription span {
  position: relative;
  display: block;
  margin-top: 10%;
  width: 100%;
  bottom: 0;
}
.ItemDescription p {
  color: #a1a1a1;
  margin-block-start: 0em;
  margin-block-end: 0em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  flex-wrap: wrap;
  display: inline;
  font-size: 0.8vw;
}
.ItemCounter {
  width: 30%;
  color: #000;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}
.cartcounter {
  position: relative;
  width: 60%;
  font-size: 0.7vw;
  height: 20%;
  margin-left: 24%;
  margin-right: auto;
  display: block;
  background-color: #f5f5f5;
  bottom: 0%;
  margin-top: 25%;
}
.cartcounter button {
  display: inline;
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 0%;
  margin: 0%;
  align-items: center;
  text-align: center;
  width: 25%;
  height: 100%;
}
.cartreduce {
  float: left;
  color: #000;
}
.cartincrease {
  float: right;
  color: #000;
}
.cartnumbercounter {
  width: 40%;
  display: block;
  position: absolute;
  padding: 0%;
  top: 25%;
  height: 50%;
  margin-left: 30% !important;
  margin-top: 0% !important;
  align-items: center;
  text-align: center;
}
.titleDescription {
  width: 100%;
  display: block;
  margin-top: 2%;
  font-size: 0.8vw;
}
#topppy {
  margin-top: 0.2%;
  font-size: 0.7vw;
  white-space: nowrap;
}
.Line-item__remove {
  cursor: pointer;
  background-color: #f5f5f5;
  border: none;
}

.counterEdited {
  position: relative;
  width: 60%;
  /* height: 20%; */
  height: 1.5vw;
  margin-left: 0%;
  /* margin-right: auto; */
  display: flex;
  margin-top: 25%;
  bottom: 0%;
  margin-bottom: 0%;
  /* margin-bottom: 19%; */
  background-color: #f5f5f5;
  padding: 0;
  justify-content: center;
  text-align: center;
  box-sizing: border-box;
}

.counterEdited button {
  width: 30%;
  height: 100% !important;
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 0%;
  margin: 0%;
  justify-content: center;
  text-align: center;
  margin-top: 0%;
}

.counterEdited span {
  position: relative;
  width: 30%;
  text-align: center !important;
  justify-content: center;
  margin: 0 auto;
  align-items: center;
  padding: 0% 0 !important;
  box-sizing: border-box;
  margin-left: 3%;
  margin-right: 3%;
  margin-top: 0%;
  font-size: 0.7vw;
  display: inline-flex;
}

.reduceEdited {
  float: left;
}

.increaseEdited {
  float: right;
  justify-content: center;
  align-items: center;
  margin-top: 1% !important;
  /* display: inline;
  width: 36%; */
}

.Line-item__price {
}
@media only screen and (max-width: 600px) {
  .ItemCounter {
    width: 30%;
    color: #000;
    display: block;
  }
  .Policy p {
    font-weight: 500;
    text-align: left;
    font-size: 2.5vw;
    line-height: 3.5vw;
    padding: 3%;
    padding-top: 1%;
    overflow-y: hidden;
    margin-block-start: 0.5em;
    margin: 0px;
    margin-block-end: 0.5em;
    font-family: 'Inter';
  }
  .CartBodyMessage span {
    position: absolute;
    right: 0;
    margin-right: 20%;
    margin-top: 6%;
  }
  .CartBodyMessage input[type="checkbox"] {
    position: absolute;
    right: 0;
    margin-right: 14%;
    margin-top: 6%;
    cursor: pointer;
  }
  #dashed {
    padding-top: 5%;
    border-top: 1px dotted grey;
    margin-top: 5%;
    /* background-image: linear-gradient(to right, black 33%, rgba(255,255,255,0) 0%);
background-position: bottom;
background-size: 8px 1px;
background-repeat: repeat-x; */
  }
  .cartcounter {
    position: relative;
    width: 60%;
    font-size: 0.7vw;
    height: 20%;
    margin-left: 24%;
    margin-right: auto;
    display: block;
    background-color: #f5f5f5;
    bottom: 0%;
    margin-top: 20%;
  }
  .ItemDescription span {
    position: relative;
    display: block;
    margin-top: 5%;
    width: 100%;
    bottom: 0;
  }
  .CartItems {
    height: auto;
    max-height: 12vh;
    overflow-y: scroll;
    width: 100%;
    margin-top: 2%;
    margin-bottom: 2%;
    padding: 2%;
  }
  .cartnumbercounter {
    font-size: 3vw;
  }
  #topppy {
    margin-top: 0.2%;
    font-size: 2.8vw;
    white-space: nowrap;
  }
  .titleDescription {
    width: 100%;
    display: block;
    margin-top: 2%;
    font-size: 2.5vw;
  }
  .ItemDescription p {
    color: #a1a1a1;
    margin-block-start: 0em;
    margin-block-end: 0em;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    flex-wrap: wrap;
    display: inline;
    font-size: 3vw;
  }
  .ItemImage {
    margin-top: 2%;
    width: 10%;
  }
  .CloseCart {
    width: 25px;
    height: 25px;
    position: absolute;
    right: 0;
    margin-right: 4%;
    background-color: #f5f5f5;
    border-radius: 50%;
    border: none;
    padding: 1%;
    cursor: pointer;
    top: -47%;
  }
  .CartBodyMessage {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    background-color: #d9d9d9;
    color: black;
    font-weight: 500;
    text-align: left;
    font-size: 2.5vw;
    line-height: 3.5vw;
    /* padding-top: 2%;
    padding-bottom: 8%;
    padding-right: 4%;
    padding-left: 4%; */
    padding: 3%;
    margin-top: 0%;
    font-family: 'Inter';
  }
  .CartBodyMessage span {
    position: absolute;
    right: 0;
    margin-right: 14%;
    margin-top: 6%;
  }
  .CartCheckout {
    width: 100%;
    height: 100%;
    background-color: black;
    color: white;
    cursor: pointer;
    text-transform: uppercase;
    font-size: 0.9em;
  }
  .CartHeader {
    width: 100%;
    text-transform: capitalize;
    font-size: 4vw;
    text-align: left;
    margin-top: 0%;
    border-bottom: 0px solid black;
    padding-bottom: 0%;
  }
  .CartDetails {
    position: relative;
    height: auto;
    width: 100%;
    height: auto;
    text-align: left;
    padding-left: 0.5%;
    font-size: 2.5vw;
    margin-top: 2%;
    font-weight: 600;
  }
  .CartDetailsHead {
    margin-top: 4%;
    margin-bottom: 3%;
    color: #454545;
  }
  .CartDetailsHeadEmpty {
    margin-top: 1%;
    margin-bottom: 0%;
    color: #000;
    word-spacing: 0px;
    font-weight: normal;
    font-size: 3.5vw;
  }
  .CartDetailsList {
    position: relative;
    margin-top: 3%;
    margin-bottom: 0%;
    height: 100%;
    border-top: 1px solid #d9d9d9;
    border-bottom: none;
    font-family: 'Inter';
  }
  .CartDetailsList ul li {
    list-style-type: none;
    text-decoration: none;
    margin-top: 3.5%;
    margin-bottom: 3.5%;
    color: #454545;
  }
  .CartDetailsList ul li p {
    font-size: 3vw;
    color: #a1a1a1;
    padding: 0%;
    margin: 0%;
    margin-top: 1%;
  }
  .Policy button {
    cursor: pointer;
    width: 100%;
    /* height: 5%; */
    font-size: 2.5vw;
    background-color: transparent;
    color: #454545;
    text-align: left;
    padding-top: 1vw;
    padding-bottom: 1vw;
    border: none;
    font-family: 'Inter';
  }
  .CartFooter {
    width: 96%;
    height: auto;
    position: absolute;
    bottom: 0;
    margin-bottom: 8%;
  }
  .CartBody {
    position: relative;
    width: 96%;
  }
}
