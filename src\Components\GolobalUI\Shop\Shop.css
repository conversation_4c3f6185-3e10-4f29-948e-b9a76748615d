.Shop {
  position: absolute;
  height: 86%;
  width: 96%;
  left: 2%;
  top: 7%;
  z-index: 999;
  background-color: rgba(255, 255, 255, 1);
  border-radius: 0px;
  padding: 0%;
  overflow-y: hidden;
  overflow-x: hidden;
  display: flex;
  font-family: Caslon;
  backdrop-filter: blur(8px);
  /* filter: blur(5px); */
  /* -webkit-filter: blur(2px); */
}

.Shop h4 {
  width: 20%;
  display: inline;
  font-size: 1.7em !important;
  margin-top: 12vh !important;
}

.ShopPageContent {
  width: 80%;
  height: 100%;
  margin-left: 0;
  margin-right: auto;
  padding: 0%;
  padding-left: 0%;
  padding-bottom: 2%;
  /* padding-top: 0.5%; */
}

.shopdiv {
  width: 20%;
  height: 100%;
  position: relative;
  display: inline-table;
  right: 0;
  top: 0;
  padding-left: 1.5%;
  padding-right: 1.5%;
  padding-top: 1.75%;
  border-left: 1px solid black;
}

.product {
  width: 100%;
  height: 24%;
  text-align: center;
  align-items: center;
  display: flex;
  margin-top: 0.2%;
  border-bottom: 0.5px solid #000000;
}

.ProductTitle {
  width: 25%;
  /* height: 90%; */
  height: 80%;
  padding-left: 2%;
  /* margin-top: auto;
    margin-bottom: auto; */
}

.ProductTitle span {
  font-weight: normal;
  display: block;
  text-align: left;
  font-size: 1.1vw;
}

.ProductTitle p {
  text-align: left;
  display: block;
  font-size: 1.1vw;
  font-style: italic;
  padding-top: 2.5%;
  border-top: 0.5px solid black;
  line-height: 1.2vw;
  margin-block-start: 2.5%;
}

.ProductImage {
  width: 35%;
  height: 100%;
  position: relative;
  display: grid;
  display: -ms-grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: 1fr;
  grid-column-gap: 0px;
  grid-row-gap: 0px;
}

.ProductImage img {
  position: absolute;
  width: 4.3vw;
  height: auto;
  margin-right: auto;
  margin-left: 15%;
  bottom: 0%;
  margin-bottom: 6%;
}

#editimg {
  width: 4.3vw;
}

#toolimg {
  width: 2.45vw;
  margin-left: 11%;
  margin-bottom: 12%;
  /* margin-top: 25%;
  margin-left: 20%; */
}
#toolimg2 {
  width: 2.38vw;
  margin-left: 16.5%;
  margin-bottom: 12%;
  /* margin-top: 25%;
  margin-left: 20%; */
}

.addButton{
  width: 1.5vw !important;
  margin-left: 40% !important;
  margin-bottom: 21% !important;
}
.addButtonV2{
  width: 1.5vw !important;
  margin-left: 33.3% !important;
  margin-bottom: 21% !important;
}
.addButtonV3{
  width: 1.5vw !important;
  margin-left: 20% !important;
  margin-bottom: 22% !important;
}
.addButtonV4{
  width: 1.5vw !important;
  margin-left: 25% !important;
  margin-bottom: 22% !important;
}
.addButtonV5{
  width: 1.5vw !important;
  margin-left: 30% !important;
  margin-bottom: 22% !important;
}

.ForAllMSG {
  margin-top: 2%;
  color: #a1a1a1;
  font-size: 0.75vw;
  font-style: normal;
}

.MessageTools {
  position: absolute;
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
  padding-top: 0.75%;
  padding-bottom: 0.75%;
  padding-left: 1%;
  padding-right: 1%;
  white-space: nowrap;
  opacity: 1;
  height: 0.5%;
  width: 15%;
  margin-top: 2.2%;
  background-color: #fff;
  margin-left: 23%;
  z-index: 9999;
  font-size: 0.8vw;
}

#toolimg1 {
  /* width: 4.4vw; */
  width: 3.2vw;
  margin-left: 0%;
  margin-bottom: 12%;
  /* margin-top: 25%;
  margin-left: 38%; */
  margin-left: 4.5%;
}

#prettysmall {
  margin-bottom: 7%;
}

.displayproduct {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: repeat(2, 1fr);
  grid-column-gap: 0px;
  grid-row-gap: 0px;
  height: 100%;
}

.displayproduct span {
  font-style: normal;
  position: absolute;
  width: 20%;
  font-size: 0.8vw;
  height: auto;
  margin-left: 40%;
  margin-right: auto;
  display: inline-flex;
  background-color: none;
  bottom: 0%;
  text-align: left;
  /* margin-bottom: 22%; */
  margin-bottom: 16%;
  white-space: nowrap;
}

.displayproductsmall {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: repeat(2, 1fr);
  grid-column-gap: 0px;
  grid-row-gap: 0px;
  height: 100%;
}

.displayproductsmall span {
  font-style: normal;
  position: absolute;
  width: 20%;
  font-size: 0.8vw;
  height: auto;
  margin-left: 33%;
  margin-right: auto;
  display: inline-flex;
  background-color: none;
  bottom: 0%;
  text-align: left;
  /* margin-bottom: 22%; */
  margin-bottom: 16%;
  white-space: nowrap;
}

.ProductDiscription {
  width: 40%;
  height: 100%;
  text-align: left;
  font-size: 0.8vw;
  /* padding-top: 2%; */
  padding-top: 3%;
  /* padding-left: 5%;
  padding-right: 5%; */
  padding-left: 7.5%;
  padding-right: 2.5%;
  /* padding-bottom: 3%; */
  line-height: 1.25;
}

.ProductDiscription p {
  text-align: center;
  margin: 0;
  margin-block-start: 0em;
  margin-block-end: 0em;
}

.DetailsNewTitles {
  width: 100%;
  height: 50%;
  display: flex;
}

.DetailsNewTitles.TOOLY {
  height: 25% !important;

}

.NewDesc.TOOLY {
  font-size: 0.7vw;
}

.NewTitle {
  width: 30%;
  padding-left: 1%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  font-size: 1vw;
  /* font-weight: 600; */
}

.NewDesc {
  width: 70%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-left: 1%;
  font-size: 1vw;
  /* font-weight: 600; */
}

.shopbag {
  position: relative;
  display: block;
  width: auto;
}

.countershop {
  position: absolute;
  width: 15%;
  font-size: 0.7vw;
  height: auto;
  margin-left: 40%;
  margin-right: auto;
  display: flex;
  justify-content: left;
  align-items: left;
  text-align: left;
  background-color: #f5f5f5;
  bottom: 0%;
  margin-bottom: 8%;
}

#editcounter {
  margin-left: 33%;
}

.countershop button {
  display: inline;
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 2%;
  margin: 0%;
  width: 30%;
  box-sizing: border-box;
}

.reduce {
  /* display: inline;
    width: 36%; */
  float: left;
}

.increase {
  float: right;
  margin-top: 1% !important;
  /* display: inline;
    width: 36%; */
}

.numbercounter {
  width: 40%;
  display: block;
  position: relative;
  /* top: 25%; */
  padding-top: 5%;
  height: 50%;
  /* margin-left: 30% !important;
  margin-top: 0% !important; */
  align-items: center;
  text-align: center;
  box-sizing: border-box;
}

.shopcart {
  width: 40%;
  background-color: black;
  color: #fff;
  padding: 0.2em;
  cursor: pointer;
  position: relative;
  display: block;
  float: left;
  border: none;
  margin-top: 4%;
}

.shopcart:hover {
  background-color: #fff;
  color: black;
}

.VoiceContainer {
  position: relative;
  float: right;
  white-space: nowrap;
  display: flex;
  margin-top: 0px;
}

.media-controls {
  display: inline-block;
}

.PlayPuase {
  background-color: transparent;
  border: 1px solid black;
  cursor: pointer;
}

.PlayPuase:hover {
  background-color: black;
  color: white;
}

.PlayPuase:active {
  background-color: black;
  color: white;
}

.counterEdited {
  position: absolute;
  width: 15%;
  height: 10%;
  margin-left: 40%;
  /* margin-right: auto; */
  display: flex;
  bottom: 0%;
  margin-bottom: 22%;
  /* margin-bottom: 19%; */
  background-color: #f5f5f5;
  padding: 0;
  justify-content: center;
  text-align: center;
  box-sizing: border-box;
}

.counterEdited button {
  width: 30%;
  height: 100% !important;
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 0%;
  margin: 0%;
  justify-content: center;
  text-align: center;
  margin-top: 0%;
}

.counterEdited span {
  position: relative;
  width: 30%;
  text-align: center !important;
  justify-content: center;
  margin: 0 auto;
  align-items: center;
  padding: 0% 0 !important;
  box-sizing: border-box;
  margin-left: 3%;
  margin-right: 3%;
  margin-top: 0%;
}

.counterEditedsmall {
  position: absolute;
  width: 15%;
  height: 10%;
  margin-left: 33%;
  /* margin-right: auto; */
  display: flex;
  bottom: 0%;
  margin-bottom: 22%;
  /* margin-bottom: 19%; */
  background-color: #f5f5f5;
  padding: 0;
  justify-content: center;
  text-align: center;
  box-sizing: border-box;
}

.counterEditedsmall button {
  width: 30%;
  height: 100% !important;
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 0%;
  margin: 0%;
  justify-content: center;
  text-align: center;
  margin-top: 0%;
}

.counterEditedsmall span {
  position: relative;
  width: 30%;
  text-align: center !important;
  justify-content: center;
  margin: 0 auto;
  align-items: center;
  padding: 0% 0 !important;
  box-sizing: border-box;
  margin-left: 3%;
  margin-right: 3%;
  margin-top: 0%;
}

.reduceEdited {
  float: left;
}

.increaseEdited {
  float: right;
  justify-content: center;
  align-items: center;
  margin-top: 1% !important;
  /* display: inline;
  width: 36%; */
}

@media only screen and (max-width: 600px) {
  .PlayPuase {
    font-size: 0.6em !important;
  }

  .VoiceContainer {
    position: relative;
    float: right;
    white-space: nowrap;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    /* margin-left: auto; */
    /* margin-right: auto; */
    width: 100%;
    /* margin-top: 63%;
  }
  #editcounter {
    margin-left: 12%;
  }
  .displayproduct {
    display: flex;
  }
  .numbercounter {
    font-size: 3vw;
  }
  .displayproduct span {
    font-style: normal;
    position: absolute;
    width: 20%;
    font-size: 3vw;
    height: auto;
    margin-left: 0%;
    margin-right: auto;
    display: inline-flex;
    background-color: none;
    bottom: 0%;
    text-align: left;
    margin-bottom: 20%;
  }
  #toolimg {
    width: 10vw;
    margin-left: 10%;
    margin-top: 10%;
  }
  #toolimg1 {
    width: 10vw;
    margin-left: 17%;
    margin-top: 10%;
  }
  .countershop {
    position: absolute;
    width: 50%;
    font-size: 0.7vw;
    height: auto;
    margin-left: auto;
    margin-right: auto;
    display: block;
    background-color: #f5f5f5;
    bottom: 0%;
    margin-bottom: 2%;
  }
  #editimg {
    width: 8vw;
  }
  .ProductImage img {
    position: absolute;
    width: 8vw;
    height: auto;
    margin-right: auto;
    margin-left: 15%;
    top: 0;
    margin-top: 5%;
  }
  .Shop {
    position: absolute;
    height: 86%;
    width: 96%;
    left: 2%;
    top: 7%;
    z-index: 999;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 0px;
    padding: 0%;
    overflow-y: hidden;
    overflow-x: hidden;
    display: block;
    font-family: Caslon;
    backdrop-filter: blur(8px);
  }
  .product {
    width: 100%;
    height: 50%;
    text-align: center;
    align-items: center;
    display: flex;
    margin-top: 0.2%;
    border-bottom: 0.5px solid #000000;
  }
  .ShopPageContent {
    width: 100%;
    height: 30%;
    margin-left: 0;
    margin-right: auto;
    padding: 0%;
    padding-left: 0%;
    padding-bottom: 2%;
    padding-top: 1%;
    overflow-y: scroll;
  }
  .shopdiv {
    width: 100%;
    height: 70%;
    position: relative;
    display: block;
    /* right: 0;
    top: 0; */
    padding-left: 1%;
    padding-right: 1%;
    border-left: none;
  }

  .ProductDiscription {
    width: 40%;
    height: 100%;
    text-align: left;
    font-size: 2vw;
    padding-top: 6%;
    padding-left: 5%;
    padding-right: 5%;
    line-height: 1.25;
  }

  .ProductDiscription p {
    text-align: center;
    margin: 0;
    margin-block-start: 0em;
    margin-block-end: 0em;
  }

  .ProductTitle p {
    text-align: left;
    display: block;
    font-size: 3vw;
    font-style: italic;
    padding-top: 2.5%;
    border-top: 0.5px solid black;
    line-height: 3.5vw;
  }

  .ProductTitle span {
    font-weight: normal;
    display: block;
    text-align: left;
    font-size: 3.5vw;
  }
}