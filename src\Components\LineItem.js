import React from "react";

function LineItemm(props) {
  // Helper function to get the product title from the new Cart API structure
  const getProductTitle = () => {
    return (
      props.line_item.merchandise?.product?.title ||
      props.line_item.merchandise?.title ||
      props.line_item.title ||
      ""
    );
  };

  const addtoCart = (lineItemId) => {
    if (
      lineItemId ==
      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE2ODAxNjEzMA=="
    ) {
      props.setObsidiancounter(props.Obsidiancounter + 1);
    }
    if (
      lineItemId ==
      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE5Mjc1NTk3MA=="
    ) {
      props.setOpalcounter(props.Opalcounter + 1);
    }
    if (
      lineItemId ==
      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE1OTEzNjAwMg=="
    ) {
      props.setibigcounter(props.ibigcounter + 1);
    } else if (
      lineItemId ==
      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODAzMzQ3MDcyMg=="
    ) {
      props.setismallcounter(props.ismallcounter + 1);
    } else if (
      lineItemId ==
      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM3MTQ4OTY2MTE4Ng=="
    ) {
      props.setyoubigcounter(props.youbigcounter + 1);
    } else if (
      lineItemId ==
      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE2MDExOTA0Mg=="
    ) {
      props.setyousmallcounterl(props.yousmallcounter + 1);
    } else if (
      lineItemId ==
      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM3MTQ5NTM5NTU4Ng=="
    ) {
      props.setwewebigcounter(props.webigcounter + 1);
    } else if (
      lineItemId ==
      "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE1OTg4OTY2Ng=="
    ) {
      props.setwesmallcounerl(props.wesmallcouner + 1);
    }
    props.addVariantToCart(lineItemId, 1);
  };

  const removetoCart = (lineItemId) => {
    if (
      lineItemId ==
        "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE1OTEzNjAwMg==" &&
      props.ibigcounter > 0
    ) {
      props.setibigcounter(props.ibigcounter - 1);
      props.updateLineItemInCart(lineItemId, props.ibigcounterref);
    } else if (
      lineItemId ==
        "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODAzMzQ3MDcyMg==" &&
      props.ismallcounter > 0
    ) {
      props.setismallcounter(props.ismallcounter - 1);
      props.updateLineItemInCart(lineItemId, props.ismallcounterref);
    } else if (
      lineItemId ==
        "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM3MTQ4OTY2MTE4Ng==" &&
      props.youbigcounter > 0
    ) {
      props.setyoubigcounter(props.youbigcounter - 1);
      props.updateLineItemInCart(lineItemId, props.youbigcounterref);
    } else if (
      lineItemId ==
        "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE2MDExOTA0Mg==" &&
      props.yousmallcounter > 0
    ) {
      props.setyousmallcounterl(props.yousmallcounter - 1);
      props.updateLineItemInCart(lineItemId, props.yousmallcounterref);
    } else if (
      lineItemId ==
        "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM3MTQ5NTM5NTU4Ng==" &&
      props.webigcounter > 0
    ) {
      props.setwewebigcounter(props.webigcounter - 1);
      props.updateLineItemInCart(lineItemId, props.webigcounterref);
    } else if (
      lineItemId ==
        "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE1OTg4OTY2Ng==" &&
      props.wesmallcouner > 0
    ) {
      props.setwesmallcounerl(props.wesmallcouner - 1);
      props.updateLineItemInCart(lineItemId, props.wesmallcounerref);
    } else if (
      lineItemId ==
        "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE2ODAxNjEzMA==" &&
      props.Obsidiancounter > 0
    ) {
      props.setObsidiancounter(props.Obsidiancounter - 1);
      // console.log(lineItemId, Obsidiancounterref)
      props.updateLineItemInCart(lineItemId, props.Obsidiancounterref);
    } else if (
      lineItemId ==
        "Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0VmFyaWFudC80MjM4ODE5Mjc1NTk3MA==" &&
      props.Opalcounter > 0
    ) {
      props.setOpalcounter(props.Opalcounter - 1);
      props.updateLineItemInCart(lineItemId, props.Opalcounterref);
    }
  };

  const decrementQuantity = (lineItemId, lineItemTitle) => {
    if (window.innerWidth > 601) {
      let event = new CustomEvent("remove-item-from-cart", {
        detail: lineItemTitle,
      });
      window.dispatchEvent(event);
      const updatedQuantity = props.line_item.quantity - 1;

      props.updateLineItemInCartLessMobile(
        lineItemId,
        updatedQuantity,
        props.line_item.quantity,
      );
    } else {
      const updatedQuantity = props.line_item.quantity - 1;
      props.updateLineItemInCartLessMobile(
        lineItemId,
        updatedQuantity,
        props.line_item.quantity,
      );
    }
    // console.log(lineItemId, updatedQuantity)
  };

  const removequantity = (lineItemId, lineItemTitle) => {
    let event = new CustomEvent("remove-item-from-cart", {
      detail: lineItemTitle,
    });
    window.dispatchEvent(event);
    const updatedQuantity = props.line_item.quantity - props.line_item.quantity;

    props.updateLineItemInCartLessMobile(
      lineItemId,
      updatedQuantity,
      props.line_item.quantity,
    );
  };

  const incrementQuantity = (lineItemId) => {
    const updatedQuantity = props.line_item.quantity + 1;
    if (window.innerWidth > 601) {
      props.updateLineItemInCartAddMobile(lineItemId, updatedQuantity);
    } else {
      props.updateLineItemInCartAddMobile(lineItemId, updatedQuantity);
    }
  };

  const slogin1 = () => {
    const title = getProductTitle();
    if (title.slice(-4).toLowerCase() != "tool") {
      if (title.slice(0, 1).toLowerCase() === "i") {
        return "I am focused.";
      }
      if (title.slice(0, 1).toLowerCase() === "y") {
        return "YOU are unconditional love.";
      }
      if (title.slice(0, 1).toLowerCase() === "w") {
        return "WE are all encompassing.";
      }
    }
  };
  const slogin2 = () => {
    const title = getProductTitle();
    if (title.slice(-4).toLowerCase() != "tool") {
      if (title.slice(0, 1).toLowerCase() === "i") {
        return "I am energized";
      }
      if (title.slice(0, 1).toLowerCase() === "y") {
        return "YOU are light";
      }
      if (title.slice(0, 1).toLowerCase() === "w") {
        return "WE are independent";
      }
    }
  };

  const cartImage = () => {
    const title = getProductTitle();
    var src = "";
    if (title.slice(0, 8).toLowerCase() === "obsidian") {
      return "obsidian tool.png";
    }
    if (title.slice(0, 4).toLowerCase() === "opal") {
      return "Opal_lg.png";
    }
    if (title.slice(0, 5).toLowerCase() === "tiger") {
      return "tigerseyetool_lrg.png";
    }
    if (title.slice(0, 1).toLowerCase() === "i") {
      src = "I_trans";
    }
    if (title.slice(0, 1).toLowerCase() === "y") {
      src = "You_trans";
    }
    if (title.slice(0, 1).toLowerCase() === "w") {
      src = "We_trans";
    }
    if (title.slice(-3).toLowerCase() === "5ml") {
      src = src.substring(0, src.indexOf("_")) + "Small";
    }
    return src + ".png";
  };
  const renderedPrice = props.line_item.merchandise.price.amount
    ? (
        props.line_item.quantity *
        parseFloat(props.line_item.merchandise.price.amount)
      ).toFixed(2)
    : (props.line_item.quantity * props.line_item.merchandise.price).toFixed(2);

  return (
    <li className="CartList" style={{ height: "auto" }}>
      <div className="ItemImage">
        <img
          style={
            getProductTitle().slice(-4).toLowerCase() === "tool" &&
            getProductTitle().slice(0, 8).toLowerCase() === "obsidian"
              ? { width: " 74%" }
              : getProductTitle().slice(-4).toLowerCase() === "tool"
              ? { width: "56%" }
              : { transform: "rotate(0deg)" }
          }
          src={cartImage()}
        />
      </div>
      <div className="ItemDescription">
        <p>
          {getProductTitle().slice(-4).toLowerCase() === "tool"
            ? "Tool"
            : "Fragrance"}
        </p>
        <div className="titleDescription">{getProductTitle()}</div>
        {getProductTitle().slice(-4).toLowerCase() !== "tool" ? (
          <>
            <p id="topppy">{slogin1()}</p>
            <br></br>
            <p id="topppy">{slogin2()}</p>
          </>
        ) : (
          <p></p>
        )}
        <span className="Line-item__price">
          €
          {renderedPrice < 999
            ? renderedPrice
            : renderedPrice.slice(0, -6) + "," + renderedPrice.slice(-6)}
        </span>
      </div>
      <div className="ItemCounter">
        {window.innerWidth >= 601 ? (
          <button
            className="RemoveCartItem"
            onClick={() =>
              removequantity(props.line_item.id, getProductTitle())
            }
          >
            <img src="close.png" />
          </button>
        ) : null}

        {window.innerWidth < 601 ? (
          <div className="cartcounter">
            <button
              className="cartreduce"
              onClick={() =>
                decrementQuantity(props.line_item.id, getProductTitle())
              }
            >
              -
            </button>
            <div className="cartnumbercounter">{props.line_item.quantity}</div>
            <button
              className="cartincrease"
              onClick={() => incrementQuantity(props.line_item.id)}
            >
              +
            </button>
          </div>
        ) : (
          <div className="counterEdited">
            <button
              className="reduceEdited"
              onClick={() =>
                decrementQuantity(props.line_item.id, getProductTitle())
              }
            >
              -
            </button>
            <span style={{ marginTop: "5%" }}>{props.line_item.quantity}</span>
            <button
              className="increaseEdited"
              onClick={() => incrementQuantity(props.line_item.id)}
            >
              +
            </button>
          </div>
        )}
      </div>
    </li>
  );
}

export default LineItemm;
